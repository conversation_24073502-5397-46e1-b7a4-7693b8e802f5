(()=>{var e={};e.id=273,e.ids=[273],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},871:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var o={};r.r(o),r.d(o,{POST:()=>u});var i=r(6559),s=r(8088),n=r(7719),a=r(2190),l=r(6921),c=r(8556),p=r(2833);async function u(e){try{if(!p.s.validateOrigin(e))return a.NextResponse.json({error:"Invalid origin"},{status:403});let t=e.headers.get("x-forwarded-for")||"unknown";if(!p.s.checkRateLimit(`config_${t}`,20,6e4))return a.NextResponse.json({error:"Rate limit exceeded"},{status:429});let{fileId:r,mode:o="edit",userId:i="user1",userName:s="User"}=await e.json();if(!r)return a.NextResponse.json({error:"File ID is required"},{status:400});let n=new c.a().getFileMetadata(r);if(!n)return a.NextResponse.json({error:"File not found"},{status:404});let u=new l.s,d="http://************:3001";(d.includes("localhost")||d.includes("127.0.0.1")||d.includes("************"))&&(console.warn("⚠️  WARNING: Using local URL that may not be accessible from OnlyOffice server:",d),console.warn("   Consider using ngrok or setting NEXT_PUBLIC_APP_URL to a public URL"));let f=`${d}/api/files/serve/${r}`,m=`${d}/api/onlyoffice/callback`;console.log("Creating OnlyOffice config for:",{fileId:r,fileName:n.name,fileUrl:f,callbackUrl:m,mode:o});let x=u.createConfig(n.name,f,m,i,s,o),h=u.signConfig(x);console.log("OnlyOffice config created successfully:",{documentType:x.documentType,documentKey:x.document.key,hasToken:!!h});let g={...x,serverUrl:"https://only.34sy.org"};return a.NextResponse.json({success:!0,config:g,token:h,serverUrl:"https://only.34sy.org"})}catch(e){return console.error("Config generation error:",e),a.NextResponse.json({error:"Failed to generate configuration"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/onlyoffice/config/route",pathname:"/api/onlyoffice/config",filename:"route",bundlePath:"app/api/onlyoffice/config/route"},resolvedPagePath:"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\onlyoffice\\config\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:x}=d;function h(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},2833:(e,t,r)=>{"use strict";r.d(t,{i:()=>s,s:()=>n});var o=r(3205),i=r.n(o);class s{constructor(){this.rateLimitStore=new Map,this.jwtSecret=process.env.ONLYOFFICE_JWT_SECRET||"fzX5rJRaYYPtns6t"}validateOnlyOfficeToken(e){try{return i().verify(e,this.jwtSecret)}catch(e){throw Error("Invalid OnlyOffice JWT token")}}signOnlyOfficeData(e){return i().sign(e,this.jwtSecret,{algorithm:"HS256"})}extractTokenFromRequest(e){let t=e.headers.get("authorization");return t?t.replace("Bearer ",""):null}validateFileUpload(e){if(e.size>0x3200000)return{valid:!1,error:"File size exceeds 50MB limit"};if(!["application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.openxmlformats-officedocument.presentationml.presentation","application/msword","application/vnd.ms-excel","application/vnd.ms-powerpoint","application/pdf","text/plain","text/csv","application/vnd.oasis.opendocument.text","application/vnd.oasis.opendocument.spreadsheet","application/vnd.oasis.opendocument.presentation"].includes(e.type)&&""!==e.type){let t=e.name.split(".").pop()?.toLowerCase();if(!t||!["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt","xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv","ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp","pdf"].includes(t))return{valid:!1,error:"File type not supported"}}return this.containsSuspiciousPatterns(e.name)?{valid:!1,error:"Filename contains invalid characters"}:{valid:!0}}containsSuspiciousPatterns(e){return[/\.\./,/[<>:"|?*]/,/^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,/^\./,/\.(exe|bat|cmd|scr|vbs|js|jar|com|pif)$/i].some(t=>t.test(e))}sanitizeFilename(e){return e.replace(/[^a-zA-Z0-9._-]/g,"_").replace(/_{2,}/g,"_").replace(/^_+|_+$/g,"").substring(0,255)}generateSecureDocumentKey(e,t){let r=`${e}_${t}_${this.jwtSecret}`;return Buffer.from(r).toString("base64").replace(/[^a-zA-Z0-9]/g,"").substring(0,20)}validateOrigin(e){let t=e.headers.get("origin"),r=e.headers.get("referer"),o=["https://only.34sy.org","http://localhost:3000","http://localhost:3001","http://127.0.0.1:3000","http://127.0.0.1:3001","http://************:3001"].filter(Boolean);if(!t&&!r||t&&o.includes(t))return!0;if(r)try{let e=new URL(r),t=`${e.protocol}//${e.host}`;return o.includes(t)}catch{return!1}return!0}checkRateLimit(e,t=100,r=6e4){let o=Date.now(),i=this.rateLimitStore.get(e);return!i||o>i.resetTime?(this.rateLimitStore.set(e,{count:1,resetTime:o+r}),!0):!(i.count>=t)&&(i.count++,!0)}cleanupRateLimit(){let e=Date.now();for(let[t,r]of this.rateLimitStore.entries())e>r.resetTime&&this.rateLimitStore.delete(t)}}let n=new s},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},6921:(e,t,r)=>{"use strict";r.d(t,{s:()=>s});var o=r(3205),i=r.n(o);class s{constructor(){this.serverUrl="https://only.34sy.org",this.jwtSecret=process.env.ONLYOFFICE_JWT_SECRET||"fzX5rJRaYYPtns6t"}getDocumentType(e){if(["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt","html","htm","mht","pdf","djvu","fb2","epub","xps"].includes(e.toLowerCase()));else if(["xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv"].includes(e.toLowerCase()))return"cell";else if(["ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp"].includes(e.toLowerCase()))return"slide";return"word"}generateDocumentKey(e,t){let r=`${e}_${t}_${Date.now()}`;return Buffer.from(r).toString("base64").replace(/[^a-zA-Z0-9]/g,"").substring(0,20)}createConfig(e,t,r,o="user1",i="User",s="edit"){let n=e.split(".").pop()||"docx",a=this.getDocumentType(n);return{documentType:a,document:{fileType:n,key:this.generateDocumentKey(e,Date.now()),title:e,url:t,permissions:{comment:!0,copy:!0,download:!0,edit:"edit"===s,fillForms:!0,modifyFilter:!0,modifyContentControl:!0,review:!0,print:!0}},editorConfig:{mode:s,lang:"en",callbackUrl:r,user:{id:o,name:i},customization:{autosave:!0,forcesave:!1,comments:!0,compactHeader:!1,compactToolbar:!1,compatibleFeatures:!1,customer:{address:"",info:"",logo:"",mail:"",name:"OnlyOffice Demo",phone:"",www:""},feedback:{url:"",visible:!1},goback:{url:"http://************:3001",text:"Back to Documents"},logo:{image:"",imageEmbedded:"",url:""},reviewDisplay:"original",showReviewChanges:!1,spellcheck:!0,toolbarNoTabs:!1,unit:"cm",zoom:100}},width:"100%",height:"100%"}}signConfig(e){return i().sign(e,this.jwtSecret,{algorithm:"HS256"})}verifyToken(e){try{return i().verify(e,this.jwtSecret)}catch(e){throw Error("Invalid JWT token")}}}},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},8556:(e,t,r)=>{"use strict";r.d(t,{a:()=>l});var o=r(9021),i=r.n(o),s=r(3873),n=r.n(s),a=r(3870);class l{constructor(){this.uploadDir=n().join(process.cwd(),"public","uploads"),this.ensureUploadDir()}ensureUploadDir(){i().existsSync(this.uploadDir)||i().mkdirSync(this.uploadDir,{recursive:!0})}async saveFile(e){let t=(0,a.A)(),r=n().extname(e.name),o=`${t}${r}`,s=n().join(this.uploadDir,o),l=await e.arrayBuffer(),c=Buffer.from(l);i().writeFileSync(s,c);let p={id:t,name:e.name||`document${r}`,size:e.size,type:e.type,extension:r.slice(1)||"unknown",uploadDate:new Date,lastModified:new Date,url:`/uploads/${o}`};return this.saveFileMetadata(p),p}saveFileMetadata(e){let t=n().join(this.uploadDir,`${e.id}.json`);i().writeFileSync(t,JSON.stringify(e,null,2))}getFileMetadata(e){try{let t=n().join(this.uploadDir,`${e}.json`);if(!i().existsSync(t))return null;let r=i().readFileSync(t,"utf-8");return JSON.parse(r)}catch(e){return null}}getAllFiles(){try{return i().readdirSync(this.uploadDir).filter(e=>e.endsWith(".json")).map(e=>{let t=i().readFileSync(n().join(this.uploadDir,e),"utf-8");return JSON.parse(t)}).sort((e,t)=>new Date(t.uploadDate).getTime()-new Date(e.uploadDate).getTime())}catch(e){return[]}}deleteFile(e){try{let t=this.getFileMetadata(e);if(!t)return!1;let r=n().extname(t.name),o=`${e}${r}`,s=n().join(this.uploadDir,o),a=n().join(this.uploadDir,`${e}.json`);return i().existsSync(s)&&i().unlinkSync(s),i().existsSync(a)&&i().unlinkSync(a),!0}catch(e){return!1}}getFileUrl(e){let t=this.getFileMetadata(e);return t?`http://************:3001${t.url}`:null}isValidFileType(e){return["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt","xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv","ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp","pdf"].includes(n().extname(e).slice(1).toLowerCase())}}},9021:e=>{"use strict";e.exports=require("fs")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,580,26],()=>r(871));module.exports=o})();