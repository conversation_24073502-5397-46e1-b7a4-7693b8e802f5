(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[92],{1739:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>d});var t=o(5155),n=o(2115),s=o(5695);function l(e){let{fileId:r,mode:o="edit",userId:s="user1",userName:l="User",onError:i,onDocumentReady:a}=e,[c,d]=(0,n.useState)(!0),[u,m]=(0,n.useState)(null),f=(0,n.useRef)(null),h=(0,n.useRef)(null);(0,n.useEffect)(()=>{let e=async()=>{try{d(!0),m(null),await x();let c=await fetch("/api/onlyoffice/config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fileId:r,mode:o,userId:s,userName:l})}),u=await c.json();if(!u.success)throw Error(u.error||"Failed to get configuration");if(window.DocsAPI&&f.current){var e,t,n;if(h.current)try{h.current.destroyEditor()}catch(e){console.warn("Error destroying previous editor:",e)}f.current.innerHTML="";let r={...u.config,events:{onDocumentReady:()=>{console.log("OnlyOffice: Document ready"),d(!1),null==a||a()},onError:e=>{console.error("OnlyOffice Error Event:",e);let r="OnlyOffice Error: ";"string"==typeof e.data?r+=e.data:"object"==typeof e.data?r+=JSON.stringify(e.data):r+="Unknown error occurred",m(r),null==i||i(r),d(!1)},onWarning:e=>{console.warn("OnlyOffice Warning:",e.data)},onInfo:e=>{console.log("OnlyOffice Info:",e.data)}}};u.token&&(r.token=u.token);let o=window.DocsAPI&&!window.DocsAPI.toString().includes("Mock");console.log("OnlyOffice API type:",o?"Real OnlyOffice API":"Mock API"),console.log("window.DocsAPI:",window.DocsAPI),console.log("window.DocsAPI.DocEditor:",null==(e=window.DocsAPI)?void 0:e.DocEditor),console.log("Initializing OnlyOffice editor with config:",{documentType:r.documentType,documentKey:null==(t=r.document)?void 0:t.key,fileUrl:null==(n=r.document)?void 0:n.url,hasToken:!!r.token,serverUrl:u.serverUrl,apiType:o?"Real":"Mock"});try{if(!window.DocsAPI||"function"!=typeof window.DocsAPI.DocEditor)throw Error("DocsAPI.DocEditor is not available");h.current=new window.DocsAPI.DocEditor(f.current.id,r),console.log("OnlyOffice editor initialized successfully"),d(!1)}catch(e){console.error("Error initializing OnlyOffice editor:",e),m("Failed to initialize editor: ".concat(e)),d(!1)}}}catch(r){let e=r instanceof Error?r.message:"Failed to initialize editor";m(e),null==i||i(e),d(!1)}};return r&&e(),()=>{if(h.current)try{h.current.destroyEditor()}catch(e){console.warn("Error destroying editor:",e)}}},[r,o,s,l]);let x=()=>new Promise((e,r)=>{if(window.DocsAPI&&"function"==typeof window.DocsAPI.DocEditor){console.log("OnlyOffice API already available"),e();return}console.log("Loading OnlyOffice API..."),new Promise((e,r)=>{let o=document.createElement("script");o.src="https://only.34sy.org/web-apps/apps/api/documents/api.js",o.type="text/javascript";let t=setTimeout(()=>{console.log("Real API loading timeout, falling back to mock"),r(Error("Timeout loading real API"))},1e4);o.onload=()=>{clearTimeout(t),console.log("Real OnlyOffice API script loaded"),window.DocsAPI&&"function"==typeof window.DocsAPI.DocEditor?(console.log("Real OnlyOffice API is ready"),e()):(console.log("Real OnlyOffice API loaded but DocsAPI not available"),r(Error("DocsAPI not available")))},o.onerror=()=>{clearTimeout(t),console.log("Failed to load real OnlyOffice API"),r(Error("Failed to load real API"))},document.head.appendChild(o)}).then(()=>{console.log("Successfully loaded real OnlyOffice API"),e()}).catch(o=>{console.log("Real API failed, loading mock API:",o.message),g().then(e).catch(r)})}),g=()=>new Promise((e,r)=>{console.log("Loading OnlyOffice mock API as fallback...");let o=document.createElement("script");o.src="/onlyoffice-api-mock.js",o.async=!0,o.onload=()=>{console.log("OnlyOffice Mock API loaded successfully"),window.DocsAPI?e():r(Error("Failed to load OnlyOffice API (including mock)"))},o.onerror=()=>{r(Error("Failed to load OnlyOffice API from all available sources"))},document.head.appendChild(o)});return u?(0,t.jsx)("div",{className:"flex items-center justify-center h-full min-h-[400px] bg-red-50 border border-red-200 rounded-lg",children:(0,t.jsxs)("div",{className:"text-center p-6",children:[(0,t.jsx)("div",{className:"text-red-600 mb-2",children:(0,t.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-red-800 mb-2",children:"Editor Error"}),(0,t.jsx)("p",{className:"text-red-600",children:u}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:"Reload Page"})]})}):(0,t.jsxs)("div",{className:"relative w-full h-full min-h-[600px]",children:[c&&(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading document editor..."})]})}),(0,t.jsx)("div",{ref:f,id:"onlyoffice-editor-".concat(r),className:"w-full h-full min-h-[600px]"})]})}var i=o(5080);function a(e){let{size:r="md",color:o="blue",text:n,className:s=""}=e;return(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(s),children:[(0,t.jsx)("div",{className:"animate-spin rounded-full border-2 border-t-transparent ".concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[r]," ").concat({blue:"border-blue-600",gray:"border-gray-600",green:"border-green-600",red:"border-red-600",yellow:"border-yellow-600"}[o]),role:"status","aria-label":"Loading"}),n&&(0,t.jsx)("p",{className:"mt-2 text-gray-600 ".concat({sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"}[r]),children:n})]})}function c(e){let{text:r="Loading..."}=e;return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsx)(a,{size:"lg",text:r})})}function d(){let e=(0,s.useParams)(),r=(0,s.useRouter)(),o=e.id,[a,d]=(0,n.useState)(null),[u,m]=(0,n.useState)(!0),[f,h]=(0,n.useState)(null);(0,n.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/files/".concat(o)),r=await e.json();r.success?d(r.file):h(r.error||"File not found")}catch(e){h("Failed to load file information")}finally{m(!1)}};o&&e()},[o]);let x=()=>{r.push("/")};return u?(0,t.jsx)(c,{text:"Loading document..."}):f||!a?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center p-6",children:[(0,t.jsx)("div",{className:"text-red-600 mb-4",children:(0,t.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Document Not Found"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:f||"The requested document could not be found."}),(0,t.jsx)("button",{onClick:x,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Back to Documents"})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,t.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("button",{onClick:x,className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,t.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Documents"]}),(0,t.jsx)("div",{className:"h-6 border-l border-gray-300"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:a.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Last modified: ",new Date(a.lastModified).toLocaleString()]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:a.extension.toUpperCase()}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[(a.size/1024/1024).toFixed(2)," MB"]})]})]})}),(0,t.jsx)("div",{className:"h-[calc(100vh-80px)]",children:(0,t.jsx)(i.A,{onError:(e,r)=>{console.error("Editor Error Boundary:",e,r),h("Editor Error: ".concat(e.message))},children:(0,t.jsx)(l,{fileId:o,mode:"edit",userId:"user1",userName:"Demo User",onError:e=>{h(e)},onDocumentReady:()=>{console.log("Document is ready for editing")}})})})]})}},5080:(e,r,o)=>{"use strict";o.d(r,{A:()=>l});var t=o(5155),n=o(2115);class s extends n.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("ErrorBoundary caught an error:",e,r),this.setState({hasError:!0,error:e,errorInfo:r}),this.props.onError&&this.props.onError(e,r)}render(){if(this.state.hasError){var e;return this.props.fallback?this.props.fallback:(0,t.jsx)("div",{className:"min-h-[400px] flex items-center justify-center bg-red-50 border border-red-200 rounded-lg",children:(0,t.jsxs)("div",{className:"text-center p-6 max-w-md",children:[(0,t.jsx)("div",{className:"text-red-600 mb-4",children:(0,t.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-red-800 mb-2",children:"Something went wrong"}),(0,t.jsx)("p",{className:"text-red-600 mb-4",children:(null==(e=this.state.error)?void 0:e.message)||"An unexpected error occurred"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("button",{onClick:this.handleReset,className:"w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Try Again"}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",children:"Reload Page"})]}),!1]})})}return this.props.children}constructor(e){super(e),this.handleReset=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.state={hasError:!1}}}let l=s},5695:(e,r,o)=>{"use strict";var t=o(8999);o.o(t,"useParams")&&o.d(r,{useParams:function(){return t.useParams}}),o.o(t,"useRouter")&&o.d(r,{useRouter:function(){return t.useRouter}})},9212:(e,r,o)=>{Promise.resolve().then(o.bind(o,1739))}},e=>{var r=r=>e(e.s=r);e.O(0,[441,684,358],()=>r(9212)),_N_E=e.O()}]);