import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  extension: string;
  uploadDate: Date;
  lastModified: Date;
  url: string;
}

export class FileManager {
  private uploadDir: string;

  constructor() {
    this.uploadDir = path.join(process.cwd(), 'public', 'uploads');
    this.ensureUploadDir();
  }

  private ensureUploadDir(): void {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  async saveFile(file: File): Promise<FileInfo> {
    const fileId = uuidv4();
    const extension = path.extname(file.name);
    const filename = `${fileId}${extension}`;
    const filepath = path.join(this.uploadDir, filename);

    // Convert File to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Save file
    fs.writeFileSync(filepath, buffer);

    const fileInfo: FileInfo = {
      id: fileId,
      name: file.name || `document${extension}`, // Ensure we have a proper name
      size: file.size,
      type: file.type,
      extension: extension.slice(1) || 'unknown', // Remove the dot, fallback to 'unknown'
      uploadDate: new Date(),
      lastModified: new Date(),
      url: `/uploads/${filename}`,
    };

    // Save metadata
    this.saveFileMetadata(fileInfo);

    return fileInfo;
  }

  private saveFileMetadata(fileInfo: FileInfo): void {
    const metadataPath = path.join(this.uploadDir, `${fileInfo.id}.json`);
    fs.writeFileSync(metadataPath, JSON.stringify(fileInfo, null, 2));
  }

  getFileMetadata(fileId: string): FileInfo | null {
    try {
      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);
      if (!fs.existsSync(metadataPath)) {
        return null;
      }
      const metadata = fs.readFileSync(metadataPath, 'utf-8');
      return JSON.parse(metadata);
    } catch (error) {
      return null;
    }
  }

  getAllFiles(): FileInfo[] {
    try {
      const files = fs.readdirSync(this.uploadDir);
      const metadataFiles = files.filter(file => file.endsWith('.json'));
      
      return metadataFiles.map(file => {
        const metadata = fs.readFileSync(path.join(this.uploadDir, file), 'utf-8');
        return JSON.parse(metadata);
      }).sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());
    } catch (error) {
      return [];
    }
  }

  deleteFile(fileId: string): boolean {
    try {
      const fileInfo = this.getFileMetadata(fileId);
      if (!fileInfo) {
        return false;
      }

      const extension = path.extname(fileInfo.name);
      const filename = `${fileId}${extension}`;
      const filepath = path.join(this.uploadDir, filename);
      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);

      // Delete file and metadata
      if (fs.existsSync(filepath)) {
        fs.unlinkSync(filepath);
      }
      if (fs.existsSync(metadataPath)) {
        fs.unlinkSync(metadataPath);
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  getFileUrl(fileId: string): string | null {
    const fileInfo = this.getFileMetadata(fileId);
    if (!fileInfo) {
      return null;
    }
    
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    return `${baseUrl}${fileInfo.url}`;
  }

  isValidFileType(filename: string): boolean {
    const allowedExtensions = [
      // Word documents
      'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt',
      // Excel spreadsheets
      'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv',
      // PowerPoint presentations
      'ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp',
      // PDF
      'pdf'
    ];

    const extension = path.extname(filename).slice(1).toLowerCase();
    return allowedExtensions.includes(extension);
  }
}
