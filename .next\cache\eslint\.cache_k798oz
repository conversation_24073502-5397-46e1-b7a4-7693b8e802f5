[{"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\create\\route.ts": "1", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\files\\serve\\[id]\\route.ts": "2", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\files\\[id]\\route.ts": "3", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\onlyoffice\\callback\\route.ts": "4", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\onlyoffice\\config\\route.ts": "5", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\test-file-access\\route.ts": "6", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\test-onlyoffice\\route.ts": "7", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\upload\\route.ts": "8", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\debug\\page.tsx": "9", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\editor\\[id]\\page.tsx": "10", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\layout.tsx": "11", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\page.tsx": "12", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\test-onlyoffice\\page.tsx": "13", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\CreateFileModal.tsx": "14", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\ErrorBoundary.tsx": "15", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\FileCard.tsx": "16", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\FileList.tsx": "17", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\FileUpload.tsx": "18", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\LoadingSpinner.tsx": "19", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\OnlyOfficeEditor.tsx": "20", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\lib\\fileUtils.ts": "21", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\lib\\onlyoffice.ts": "22", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\lib\\security.ts": "23", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\middleware.ts": "24", "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\types\\onlyoffice.ts": "25"}, {"size": 2758, "mtime": 1752389666539, "results": "26", "hashOfConfig": "27"}, {"size": 6209, "mtime": 1752387824933, "results": "28", "hashOfConfig": "27"}, {"size": 1406, "mtime": 1752385100850, "results": "29", "hashOfConfig": "27"}, {"size": 4233, "mtime": 1752385377007, "results": "30", "hashOfConfig": "27"}, {"size": 3166, "mtime": 1752390580125, "results": "31", "hashOfConfig": "27"}, {"size": 1583, "mtime": 1752390611378, "results": "32", "hashOfConfig": "27"}, {"size": 1383, "mtime": 1752387170743, "results": "33", "hashOfConfig": "27"}, {"size": 2351, "mtime": 1752384827369, "results": "34", "hashOfConfig": "27"}, {"size": 7553, "mtime": 1752386484448, "results": "35", "hashOfConfig": "27"}, {"size": 4538, "mtime": 1752384653420, "results": "36", "hashOfConfig": "27"}, {"size": 1017, "mtime": 1752385114617, "results": "37", "hashOfConfig": "27"}, {"size": 9959, "mtime": 1752389913828, "results": "38", "hashOfConfig": "27"}, {"size": 4049, "mtime": 1752387305996, "results": "39", "hashOfConfig": "27"}, {"size": 6209, "mtime": 1752389730731, "results": "40", "hashOfConfig": "27"}, {"size": 3138, "mtime": 1752384561415, "results": "41", "hashOfConfig": "27"}, {"size": 8313, "mtime": 1752384350269, "results": "42", "hashOfConfig": "27"}, {"size": 7744, "mtime": 1752384382746, "results": "43", "hashOfConfig": "27"}, {"size": 4666, "mtime": 1752384247383, "results": "44", "hashOfConfig": "27"}, {"size": 1784, "mtime": 1752384574817, "results": "45", "hashOfConfig": "27"}, {"size": 9504, "mtime": 1752387390039, "results": "46", "hashOfConfig": "27"}, {"size": 3993, "mtime": 1752385346873, "results": "47", "hashOfConfig": "27"}, {"size": 5127, "mtime": 1752386036877, "results": "48", "hashOfConfig": "27"}, {"size": 6009, "mtime": 1752385130687, "results": "49", "hashOfConfig": "27"}, {"size": 2198, "mtime": 1752385820803, "results": "50", "hashOfConfig": "27"}, {"size": 5482, "mtime": 1752384545013, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "h325fl", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\create\\route.ts", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\files\\serve\\[id]\\route.ts", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\files\\[id]\\route.ts", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\onlyoffice\\callback\\route.ts", ["127", "128", "129"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\onlyoffice\\config\\route.ts", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\test-file-access\\route.ts", ["130"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\test-onlyoffice\\route.ts", ["131"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\upload\\route.ts", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\debug\\page.tsx", ["132", "133", "134", "135", "136"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\editor\\[id]\\page.tsx", ["137"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\layout.tsx", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\page.tsx", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\test-onlyoffice\\page.tsx", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\CreateFileModal.tsx", ["138", "139"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\ErrorBoundary.tsx", ["140", "141"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\FileCard.tsx", ["142", "143"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\FileList.tsx", ["144", "145"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\FileUpload.tsx", ["146", "147"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\components\\OnlyOfficeEditor.tsx", ["148", "149", "150", "151", "152", "153"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\lib\\fileUtils.ts", ["154", "155", "156"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\lib\\onlyoffice.ts", ["157", "158"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\lib\\security.ts", ["159", "160", "161"], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\middleware.ts", [], [], "C:\\xampp\\htdocs\\ai\\onlyofice\\src\\types\\onlyoffice.ts", ["162", "163"], [], {"ruleId": "164", "severity": 2, "message": "165", "line": 13, "column": 13, "nodeType": "166", "messageId": "167", "endLine": 13, "endColumn": 16, "suggestions": "168"}, {"ruleId": "169", "severity": 2, "message": "170", "line": 36, "column": 11, "nodeType": null, "messageId": "171", "endLine": 36, "endColumn": 28}, {"ruleId": "169", "severity": 2, "message": "172", "line": 71, "column": 20, "nodeType": null, "messageId": "171", "endLine": 71, "endColumn": 25}, {"ruleId": "169", "severity": 2, "message": "173", "line": 3, "column": 27, "nodeType": null, "messageId": "171", "endLine": 3, "endColumn": 34}, {"ruleId": "169", "severity": 2, "message": "173", "line": 3, "column": 27, "nodeType": null, "messageId": "171", "endLine": 3, "endColumn": 34}, {"ruleId": "164", "severity": 2, "message": "165", "line": 14, "column": 24, "nodeType": "166", "messageId": "167", "endLine": 14, "endColumn": 27, "suggestions": "174"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 41, "column": 30, "nodeType": "166", "messageId": "167", "endLine": 41, "endColumn": 33, "suggestions": "175"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 43, "column": 72, "nodeType": "166", "messageId": "167", "endLine": 43, "endColumn": 75, "suggestions": "176"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 67, "column": 28, "nodeType": "166", "messageId": "167", "endLine": 67, "endColumn": 31, "suggestions": "177"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 69, "column": 63, "nodeType": "166", "messageId": "167", "endLine": 69, "endColumn": 66, "suggestions": "178"}, {"ruleId": "169", "severity": 2, "message": "179", "line": 32, "column": 16, "nodeType": null, "messageId": "171", "endLine": 32, "endColumn": 19}, {"ruleId": "164", "severity": 2, "message": "165", "line": 8, "column": 27, "nodeType": "166", "messageId": "167", "endLine": 8, "endColumn": 30, "suggestions": "180"}, {"ruleId": "169", "severity": 2, "message": "172", "line": 52, "column": 14, "nodeType": null, "messageId": "171", "endLine": 52, "endColumn": 19}, {"ruleId": "164", "severity": 2, "message": "165", "line": 9, "column": 39, "nodeType": "166", "messageId": "167", "endLine": 9, "endColumn": 42, "suggestions": "181"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 25, "column": 46, "nodeType": "166", "messageId": "167", "endLine": 25, "endColumn": 49, "suggestions": "182"}, {"ruleId": "183", "severity": 2, "message": "184", "line": 177, "column": 47, "nodeType": "185", "messageId": "186", "suggestions": "187"}, {"ruleId": "183", "severity": 2, "message": "184", "line": 177, "column": 59, "nodeType": "185", "messageId": "186", "suggestions": "188"}, {"ruleId": "169", "severity": 2, "message": "179", "line": 43, "column": 14, "nodeType": null, "messageId": "171", "endLine": 43, "endColumn": 17}, {"ruleId": "164", "severity": 2, "message": "165", "line": 154, "column": 64, "nodeType": "166", "messageId": "167", "endLine": 154, "endColumn": 67, "suggestions": "189"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 6, "column": 27, "nodeType": "166", "messageId": "167", "endLine": 6, "endColumn": 30, "suggestions": "190"}, {"ruleId": "169", "severity": 2, "message": "172", "line": 37, "column": 14, "nodeType": null, "messageId": "171", "endLine": 37, "endColumn": 19}, {"ruleId": "164", "severity": 2, "message": "165", "line": 16, "column": 14, "nodeType": "166", "messageId": "167", "endLine": 16, "endColumn": 17, "suggestions": "191"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 31, "column": 31, "nodeType": "166", "messageId": "167", "endLine": 31, "endColumn": 34, "suggestions": "192"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 85, "column": 32, "nodeType": "166", "messageId": "167", "endLine": 85, "endColumn": 35, "suggestions": "193"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 99, "column": 34, "nodeType": "166", "messageId": "167", "endLine": 99, "endColumn": 37, "suggestions": "194"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 102, "column": 31, "nodeType": "166", "messageId": "167", "endLine": 102, "endColumn": 34, "suggestions": "195"}, {"ruleId": "196", "severity": 1, "message": "197", "line": 163, "column": 6, "nodeType": "198", "endLine": 163, "endColumn": 38, "suggestions": "199"}, {"ruleId": "169", "severity": 2, "message": "172", "line": 73, "column": 14, "nodeType": null, "messageId": "171", "endLine": 73, "endColumn": 19}, {"ruleId": "169", "severity": 2, "message": "172", "line": 87, "column": 14, "nodeType": null, "messageId": "171", "endLine": 87, "endColumn": 19}, {"ruleId": "169", "severity": 2, "message": "172", "line": 113, "column": 14, "nodeType": null, "messageId": "171", "endLine": 113, "endColumn": 19}, {"ruleId": "164", "severity": 2, "message": "165", "line": 189, "column": 31, "nodeType": "166", "messageId": "167", "endLine": 189, "endColumn": 34, "suggestions": "200"}, {"ruleId": "169", "severity": 2, "message": "172", "line": 192, "column": 14, "nodeType": null, "messageId": "171", "endLine": 192, "endColumn": 19}, {"ruleId": "164", "severity": 2, "message": "165", "line": 14, "column": 43, "nodeType": "166", "messageId": "167", "endLine": 14, "endColumn": 46, "suggestions": "201"}, {"ruleId": "169", "severity": 2, "message": "172", "line": 17, "column": 14, "nodeType": null, "messageId": "171", "endLine": 17, "endColumn": 19}, {"ruleId": "164", "severity": 2, "message": "165", "line": 25, "column": 28, "nodeType": "166", "messageId": "167", "endLine": 25, "endColumn": 31, "suggestions": "202"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 230, "column": 13, "nodeType": "166", "messageId": "167", "endLine": 230, "endColumn": 16, "suggestions": "203"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 236, "column": 15, "nodeType": "166", "messageId": "167", "endLine": 236, "endColumn": 18, "suggestions": "204"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["205", "206"], "@typescript-eslint/no-unused-vars", "'onlyOfficeService' is assigned a value but never used.", "unusedVar", "'error' is defined but never used.", "'request' is defined but never used.", ["207", "208"], ["209", "210"], ["211", "212"], ["213", "214"], ["215", "216"], "'err' is defined but never used.", ["217", "218"], ["219", "220"], ["221", "222"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["223", "224", "225", "226"], ["227", "228", "229", "230"], ["231", "232"], ["233", "234"], ["235", "236"], ["237", "238"], ["239", "240"], ["241", "242"], ["243", "244"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadOnlyOfficeAPI', 'onDocumentReady', and 'onError'. Either include them or remove the dependency array. If 'onDocumentReady' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["245"], ["246", "247"], ["248", "249"], ["250", "251"], ["252", "253"], ["254", "255"], {"messageId": "256", "fix": "257", "desc": "258"}, {"messageId": "259", "fix": "260", "desc": "261"}, {"messageId": "256", "fix": "262", "desc": "258"}, {"messageId": "259", "fix": "263", "desc": "261"}, {"messageId": "256", "fix": "264", "desc": "258"}, {"messageId": "259", "fix": "265", "desc": "261"}, {"messageId": "256", "fix": "266", "desc": "258"}, {"messageId": "259", "fix": "267", "desc": "261"}, {"messageId": "256", "fix": "268", "desc": "258"}, {"messageId": "259", "fix": "269", "desc": "261"}, {"messageId": "256", "fix": "270", "desc": "258"}, {"messageId": "259", "fix": "271", "desc": "261"}, {"messageId": "256", "fix": "272", "desc": "258"}, {"messageId": "259", "fix": "273", "desc": "261"}, {"messageId": "256", "fix": "274", "desc": "258"}, {"messageId": "259", "fix": "275", "desc": "261"}, {"messageId": "256", "fix": "276", "desc": "258"}, {"messageId": "259", "fix": "277", "desc": "261"}, {"messageId": "278", "data": "279", "fix": "280", "desc": "281"}, {"messageId": "278", "data": "282", "fix": "283", "desc": "284"}, {"messageId": "278", "data": "285", "fix": "286", "desc": "287"}, {"messageId": "278", "data": "288", "fix": "289", "desc": "290"}, {"messageId": "278", "data": "291", "fix": "292", "desc": "281"}, {"messageId": "278", "data": "293", "fix": "294", "desc": "284"}, {"messageId": "278", "data": "295", "fix": "296", "desc": "287"}, {"messageId": "278", "data": "297", "fix": "298", "desc": "290"}, {"messageId": "256", "fix": "299", "desc": "258"}, {"messageId": "259", "fix": "300", "desc": "261"}, {"messageId": "256", "fix": "301", "desc": "258"}, {"messageId": "259", "fix": "302", "desc": "261"}, {"messageId": "256", "fix": "303", "desc": "258"}, {"messageId": "259", "fix": "304", "desc": "261"}, {"messageId": "256", "fix": "305", "desc": "258"}, {"messageId": "259", "fix": "306", "desc": "261"}, {"messageId": "256", "fix": "307", "desc": "258"}, {"messageId": "259", "fix": "308", "desc": "261"}, {"messageId": "256", "fix": "309", "desc": "258"}, {"messageId": "259", "fix": "310", "desc": "261"}, {"messageId": "256", "fix": "311", "desc": "258"}, {"messageId": "259", "fix": "312", "desc": "261"}, {"desc": "313", "fix": "314"}, {"messageId": "256", "fix": "315", "desc": "258"}, {"messageId": "259", "fix": "316", "desc": "261"}, {"messageId": "256", "fix": "317", "desc": "258"}, {"messageId": "259", "fix": "318", "desc": "261"}, {"messageId": "256", "fix": "319", "desc": "258"}, {"messageId": "259", "fix": "320", "desc": "261"}, {"messageId": "256", "fix": "321", "desc": "258"}, {"messageId": "259", "fix": "322", "desc": "261"}, {"messageId": "256", "fix": "323", "desc": "258"}, {"messageId": "259", "fix": "324", "desc": "261"}, "suggestUnknown", {"range": "325", "text": "326"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "327", "text": "328"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "329", "text": "326"}, {"range": "330", "text": "328"}, {"range": "331", "text": "326"}, {"range": "332", "text": "328"}, {"range": "333", "text": "326"}, {"range": "334", "text": "328"}, {"range": "335", "text": "326"}, {"range": "336", "text": "328"}, {"range": "337", "text": "326"}, {"range": "338", "text": "328"}, {"range": "339", "text": "326"}, {"range": "340", "text": "328"}, {"range": "341", "text": "326"}, {"range": "342", "text": "328"}, {"range": "343", "text": "326"}, {"range": "344", "text": "328"}, "replaceWithAlt", {"alt": "345"}, {"range": "346", "text": "347"}, "Replace with `&quot;`.", {"alt": "348"}, {"range": "349", "text": "350"}, "Replace with `&ldquo;`.", {"alt": "351"}, {"range": "352", "text": "353"}, "Replace with `&#34;`.", {"alt": "354"}, {"range": "355", "text": "356"}, "Replace with `&rdquo;`.", {"alt": "345"}, {"range": "357", "text": "358"}, {"alt": "348"}, {"range": "359", "text": "360"}, {"alt": "351"}, {"range": "361", "text": "362"}, {"alt": "354"}, {"range": "363", "text": "364"}, {"range": "365", "text": "326"}, {"range": "366", "text": "328"}, {"range": "367", "text": "326"}, {"range": "368", "text": "328"}, {"range": "369", "text": "326"}, {"range": "370", "text": "328"}, {"range": "371", "text": "326"}, {"range": "372", "text": "328"}, {"range": "373", "text": "326"}, {"range": "374", "text": "328"}, {"range": "375", "text": "326"}, {"range": "376", "text": "328"}, {"range": "377", "text": "326"}, {"range": "378", "text": "328"}, "Update the dependencies array to be: [fileId, loadOnlyOfficeAPI, mode, onDocumentReady, onError, userId, userName]", {"range": "379", "text": "380"}, {"range": "381", "text": "326"}, {"range": "382", "text": "328"}, {"range": "383", "text": "326"}, {"range": "384", "text": "328"}, {"range": "385", "text": "326"}, {"range": "386", "text": "328"}, {"range": "387", "text": "326"}, {"range": "388", "text": "328"}, {"range": "389", "text": "326"}, {"range": "390", "text": "328"}, [361, 364], "unknown", [361, 364], "never", [429, 432], [429, 432], [1426, 1429], [1426, 1429], [1556, 1559], [1556, 1559], [2345, 2348], [2345, 2348], [2464, 2467], [2464, 2467], [158, 161], [158, 161], [242, 245], [242, 245], [618, 621], [618, 621], "&quot;", [7438, 7486], "\n              Are you sure you want to delete &quot;", "&ldquo;", [7438, 7486], "\n              Are you sure you want to delete &ldquo;", "&#34;", [7438, 7486], "\n              Are you sure you want to delete &#34;", "&rdquo;", [7438, 7486], "\n              Are you sure you want to delete &rdquo;", [7497, 7542], "&quot;? This action cannot be undone.\n            ", [7497, 7542], "&ldquo;? This action cannot be undone.\n            ", [7497, 7542], "&#34;? This action cannot be undone.\n            ", [7497, 7542], "&rdquo;? This action cannot be undone.\n            ", [4605, 4608], [4605, 4608], [119, 122], [119, 122], [317, 320], [317, 320], [689, 692], [689, 692], [2193, 2196], [2193, 2196], [2819, 2822], [2819, 2822], [2941, 2944], [2941, 2944], [5052, 5084], "[fileId, loadOnlyOfficeAPI, mode, onDocumentReady, onError, userId, userName]", [4985, 4988], [4985, 4988], [341, 344], [341, 344], [571, 574], [571, 574], [4493, 4496], [4493, 4496], [4591, 4594], [4591, 4594]]