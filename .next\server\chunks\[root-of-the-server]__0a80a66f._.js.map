{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/lib/fileUtils.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { v4 as uuidv4 } from 'uuid';\n\nexport interface FileInfo {\n  id: string;\n  name: string;\n  size: number;\n  type: string;\n  extension: string;\n  uploadDate: Date;\n  lastModified: Date;\n  url: string;\n}\n\nexport class FileManager {\n  private uploadDir: string;\n\n  constructor() {\n    this.uploadDir = path.join(process.cwd(), 'public', 'uploads');\n    this.ensureUploadDir();\n  }\n\n  private ensureUploadDir(): void {\n    if (!fs.existsSync(this.uploadDir)) {\n      fs.mkdirSync(this.uploadDir, { recursive: true });\n    }\n  }\n\n  async saveFile(file: File): Promise<FileInfo> {\n    const fileId = uuidv4();\n    const extension = path.extname(file.name);\n    const filename = `${fileId}${extension}`;\n    const filepath = path.join(this.uploadDir, filename);\n\n    // Convert File to Buffer\n    const arrayBuffer = await file.arrayBuffer();\n    const buffer = Buffer.from(arrayBuffer);\n\n    // Save file\n    fs.writeFileSync(filepath, buffer);\n\n    const fileInfo: FileInfo = {\n      id: fileId,\n      name: file.name || `document${extension}`, // Ensure we have a proper name\n      size: file.size,\n      type: file.type,\n      extension: extension.slice(1) || 'unknown', // Remove the dot, fallback to 'unknown'\n      uploadDate: new Date(),\n      lastModified: new Date(),\n      url: `/uploads/${filename}`,\n    };\n\n    // Save metadata\n    this.saveFileMetadata(fileInfo);\n\n    return fileInfo;\n  }\n\n  private saveFileMetadata(fileInfo: FileInfo): void {\n    const metadataPath = path.join(this.uploadDir, `${fileInfo.id}.json`);\n    fs.writeFileSync(metadataPath, JSON.stringify(fileInfo, null, 2));\n  }\n\n  getFileMetadata(fileId: string): FileInfo | null {\n    try {\n      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);\n      if (!fs.existsSync(metadataPath)) {\n        return null;\n      }\n      const metadata = fs.readFileSync(metadataPath, 'utf-8');\n      return JSON.parse(metadata);\n    } catch (error) {\n      return null;\n    }\n  }\n\n  getAllFiles(): FileInfo[] {\n    try {\n      const files = fs.readdirSync(this.uploadDir);\n      const metadataFiles = files.filter(file => file.endsWith('.json'));\n      \n      return metadataFiles.map(file => {\n        const metadata = fs.readFileSync(path.join(this.uploadDir, file), 'utf-8');\n        return JSON.parse(metadata);\n      }).sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());\n    } catch (error) {\n      return [];\n    }\n  }\n\n  deleteFile(fileId: string): boolean {\n    try {\n      const fileInfo = this.getFileMetadata(fileId);\n      if (!fileInfo) {\n        return false;\n      }\n\n      const extension = path.extname(fileInfo.name);\n      const filename = `${fileId}${extension}`;\n      const filepath = path.join(this.uploadDir, filename);\n      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);\n\n      // Delete file and metadata\n      if (fs.existsSync(filepath)) {\n        fs.unlinkSync(filepath);\n      }\n      if (fs.existsSync(metadataPath)) {\n        fs.unlinkSync(metadataPath);\n      }\n\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  getFileUrl(fileId: string): string | null {\n    const fileInfo = this.getFileMetadata(fileId);\n    if (!fileInfo) {\n      return null;\n    }\n    \n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';\n    return `${baseUrl}${fileInfo.url}`;\n  }\n\n  isValidFileType(filename: string): boolean {\n    const allowedExtensions = [\n      // Word documents\n      'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt',\n      // Excel spreadsheets\n      'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv',\n      // PowerPoint presentations\n      'ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp',\n      // PDF\n      'pdf'\n    ];\n\n    const extension = path.extname(filename).slice(1).toLowerCase();\n    return allowedExtensions.includes(extension);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAaO,MAAM;IACH,UAAkB;IAE1B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACpD,IAAI,CAAC,eAAe;IACtB;IAEQ,kBAAwB;QAC9B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,GAAG;YAClC,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE;gBAAE,WAAW;YAAK;QACjD;IACF;IAEA,MAAM,SAAS,IAAU,EAAqB;QAC5C,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QACpB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,KAAK,IAAI;QACxC,MAAM,WAAW,GAAG,SAAS,WAAW;QACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAE3C,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,YAAY;QACZ,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU;QAE3B,MAAM,WAAqB;YACzB,IAAI;YACJ,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,WAAW;YACzC,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,WAAW,UAAU,KAAK,CAAC,MAAM;YACjC,YAAY,IAAI;YAChB,cAAc,IAAI;YAClB,KAAK,CAAC,SAAS,EAAE,UAAU;QAC7B;QAEA,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,CAAC;QAEtB,OAAO;IACT;IAEQ,iBAAiB,QAAkB,EAAQ;QACjD,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC,KAAK,CAAC;QACpE,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,cAAc,KAAK,SAAS,CAAC,UAAU,MAAM;IAChE;IAEA,gBAAgB,MAAc,EAAmB;QAC/C,IAAI;YACF,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,OAAO,KAAK,CAAC;YAC/D,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;gBAChC,OAAO;YACT;YACA,MAAM,WAAW,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,cAAc;YAC/C,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,cAA0B;QACxB,IAAI;YACF,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YAC3C,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;YAEzD,OAAO,cAAc,GAAG,CAAC,CAAA;gBACvB,MAAM,WAAW,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO;gBAClE,OAAO,KAAK,KAAK,CAAC;YACpB,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QACrF,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA,WAAW,MAAc,EAAW;QAClC,IAAI;YACF,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;YACtC,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YAEA,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,SAAS,IAAI;YAC5C,MAAM,WAAW,GAAG,SAAS,WAAW;YACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC3C,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,OAAO,KAAK,CAAC;YAE/D,2BAA2B;YAC3B,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;gBAC3B,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YAChB;YACA,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;gBAC/B,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YAChB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,WAAW,MAAc,EAAiB;QACxC,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QACtC,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,MAAM,UAAU,gEAAmC;QACnD,OAAO,GAAG,UAAU,SAAS,GAAG,EAAE;IACpC;IAEA,gBAAgB,QAAgB,EAAW;QACzC,MAAM,oBAAoB;YACxB,iBAAiB;YACjB;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YAAO;YAC3E,qBAAqB;YACrB;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YACpE,2BAA2B;YAC3B;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAC7D,MAAM;YACN;SACD;QAED,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,GAAG,WAAW;QAC7D,OAAO,kBAAkB,QAAQ,CAAC;IACpC;AACF", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/api/files/serve/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { FileManager } from '@/lib/fileUtils';\nimport fs from 'fs';\nimport path from 'path';\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id: fileId } = await params;\n    const fileManager = new FileManager();\n\n    const fileInfo = fileManager.getFileMetadata(fileId);\n\n    if (!fileInfo) {\n      return NextResponse.json(\n        { error: 'File not found' },\n        { status: 404 }\n      );\n    }\n\n    // Construct file path\n    const uploadDir = path.join(process.cwd(), 'public', 'uploads');\n    const extension = path.extname(fileInfo.name) || `.${fileInfo.extension}`;\n    const filename = `${fileId}${extension}`;\n    const filepath = path.join(uploadDir, filename);\n\n    // Check if file exists\n    if (!fs.existsSync(filepath)) {\n      return NextResponse.json(\n        { error: 'File not found on disk' },\n        { status: 404 }\n      );\n    }\n\n    // Read file\n    const fileBuffer = fs.readFileSync(filepath);\n\n    // Set appropriate headers for OnlyOffice\n    const headers = new Headers();\n    headers.set('Content-Type', fileInfo.type || 'application/octet-stream');\n    headers.set('Content-Length', fileBuffer.length.toString());\n    headers.set('Content-Disposition', `inline; filename=\"${fileInfo.name}\"`);\n\n    // CORS headers for OnlyOffice - must allow all origins for external server access\n    headers.set('Access-Control-Allow-Origin', '*');\n    headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');\n    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');\n    headers.set('Access-Control-Allow-Credentials', 'false');\n\n    // Cache headers\n    headers.set('Cache-Control', 'public, max-age=3600');\n    headers.set('ETag', `\"${fileId}-${fileInfo.lastModified}\"`);\n\n    // Additional headers for OnlyOffice compatibility\n    headers.set('Accept-Ranges', 'bytes');\n    headers.set('X-Content-Type-Options', 'nosniff');\n\n    return new NextResponse(fileBuffer, {\n      status: 200,\n      headers,\n    });\n  } catch (error) {\n    console.error('File serve error:', error);\n    return NextResponse.json(\n      { error: 'Failed to serve file' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function HEAD(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id: fileId } = await params;\n    const fileManager = new FileManager();\n\n    const fileInfo = fileManager.getFileMetadata(fileId);\n\n    if (!fileInfo) {\n      return new NextResponse(null, { status: 404 });\n    }\n\n    // Construct file path\n    const uploadDir = path.join(process.cwd(), 'public', 'uploads');\n    const extension = path.extname(fileInfo.name) || `.${fileInfo.extension}`;\n    const filename = `${fileId}${extension}`;\n    const filepath = path.join(uploadDir, filename);\n\n    // Check if file exists\n    if (!fs.existsSync(filepath)) {\n      return new NextResponse(null, { status: 404 });\n    }\n\n    // Get file stats\n    const stats = fs.statSync(filepath);\n\n    // Set appropriate headers\n    const headers = new Headers();\n    headers.set('Content-Type', fileInfo.type || 'application/octet-stream');\n    headers.set('Content-Length', stats.size.toString());\n    headers.set('Content-Disposition', `inline; filename=\"${fileInfo.name}\"`);\n\n    // CORS headers for OnlyOffice - must allow all origins for external server access\n    headers.set('Access-Control-Allow-Origin', '*');\n    headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');\n    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');\n    headers.set('Access-Control-Allow-Credentials', 'false');\n\n    // Cache headers\n    headers.set('Cache-Control', 'public, max-age=3600');\n    headers.set('ETag', `\"${fileId}-${fileInfo.lastModified}\"`);\n\n    // Additional headers for OnlyOffice compatibility\n    headers.set('Accept-Ranges', 'bytes');\n    headers.set('X-Content-Type-Options', 'nosniff');\n\n    return new NextResponse(null, {\n      status: 200,\n      headers,\n    });\n  } catch (error) {\n    console.error('File head error:', error);\n    return new NextResponse(null, { status: 500 });\n  }\n}\n\nexport async function OPTIONS() {\n  const headers = new Headers();\n  headers.set('Access-Control-Allow-Origin', '*');\n  headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');\n  headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');\n  headers.set('Access-Control-Allow-Credentials', 'false');\n  headers.set('Access-Control-Max-Age', '86400');\n\n  return new NextResponse(null, {\n    status: 200,\n    headers,\n  });\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,IAAI,MAAM,EAAE,GAAG,MAAM;QAC7B,MAAM,cAAc,IAAI,yHAAA,CAAA,cAAW;QAEnC,MAAM,WAAW,YAAY,eAAe,CAAC;QAE7C,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACrD,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC,CAAC,EAAE,SAAS,SAAS,EAAE;QACzE,MAAM,WAAW,GAAG,SAAS,WAAW;QACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;QAEtC,uBAAuB;QACvB,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY;QACZ,MAAM,aAAa,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC;QAEnC,yCAAyC;QACzC,MAAM,UAAU,IAAI;QACpB,QAAQ,GAAG,CAAC,gBAAgB,SAAS,IAAI,IAAI;QAC7C,QAAQ,GAAG,CAAC,kBAAkB,WAAW,MAAM,CAAC,QAAQ;QACxD,QAAQ,GAAG,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC;QAExE,kFAAkF;QAClF,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,QAAQ,GAAG,CAAC,oCAAoC;QAEhD,gBAAgB;QAChB,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,YAAY,CAAC,CAAC,CAAC;QAE1D,kDAAkD;QAClD,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,YAAY;YAClC,QAAQ;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,IAAI,MAAM,EAAE,GAAG,MAAM;QAC7B,MAAM,cAAc,IAAI,yHAAA,CAAA,cAAW;QAEnC,MAAM,WAAW,YAAY,eAAe,CAAC;QAE7C,IAAI,CAAC,UAAU;YACb,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;gBAAE,QAAQ;YAAI;QAC9C;QAEA,sBAAsB;QACtB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACrD,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC,CAAC,EAAE,SAAS,SAAS,EAAE;QACzE,MAAM,WAAW,GAAG,SAAS,WAAW;QACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;QAEtC,uBAAuB;QACvB,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;YAC5B,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;gBAAE,QAAQ;YAAI;QAC9C;QAEA,iBAAiB;QACjB,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;QAE1B,0BAA0B;QAC1B,MAAM,UAAU,IAAI;QACpB,QAAQ,GAAG,CAAC,gBAAgB,SAAS,IAAI,IAAI;QAC7C,QAAQ,GAAG,CAAC,kBAAkB,MAAM,IAAI,CAAC,QAAQ;QACjD,QAAQ,GAAG,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC;QAExE,kFAAkF;QAClF,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,QAAQ,GAAG,CAAC,oCAAoC;QAEhD,gBAAgB;QAChB,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,YAAY,CAAC,CAAC,CAAC;QAE1D,kDAAkD;QAClD,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;YAC5B,QAAQ;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC9C;AACF;AAEO,eAAe;IACpB,MAAM,UAAU,IAAI;IACpB,QAAQ,GAAG,CAAC,+BAA+B;IAC3C,QAAQ,GAAG,CAAC,gCAAgC;IAC5C,QAAQ,GAAG,CAAC,gCAAgC;IAC5C,QAAQ,GAAG,CAAC,oCAAoC;IAChD,QAAQ,GAAG,CAAC,0BAA0B;IAEtC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR;IACF;AACF", "debugId": null}}]}