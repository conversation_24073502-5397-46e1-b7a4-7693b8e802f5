{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/api/test-local-onlyoffice/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const onlyOfficeUrl = process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080';\n    \n    console.log('Testing local OnlyOffice server accessibility...');\n    console.log('OnlyOffice URL:', onlyOfficeUrl);\n\n    // Test if the OnlyOffice server is accessible\n    try {\n      const response = await fetch(`${onlyOfficeUrl}/web-apps/apps/api/documents/api.js`, {\n        method: 'HEAD',\n        headers: {\n          'User-Agent': 'OnlyOffice-Test'\n        }\n      });\n\n      console.log('OnlyOffice server response:', {\n        status: response.status,\n        statusText: response.statusText,\n        headers: Object.fromEntries(response.headers.entries())\n      });\n\n      return NextResponse.json({\n        success: true,\n        onlyOfficeUrl,\n        status: response.status,\n        statusText: response.statusText,\n        headers: Object.fromEntries(response.headers.entries()),\n        accessible: response.ok,\n        message: response.ok \n          ? 'Local OnlyOffice server is accessible!'\n          : 'Local OnlyOffice server is not accessible'\n      });\n    } catch (error) {\n      console.error('OnlyOffice server test error:', error);\n      return NextResponse.json({\n        success: false,\n        onlyOfficeUrl,\n        error: error instanceof Error ? error.message : 'Unknown error',\n        accessible: false,\n        message: 'Cannot connect to local OnlyOffice server'\n      });\n    }\n  } catch (error) {\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,gBAAgB,6DAAqC;QAE3D,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,8CAA8C;QAC9C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,cAAc,mCAAmC,CAAC,EAAE;gBAClF,QAAQ;gBACR,SAAS;oBACP,cAAc;gBAChB;YACF;YAEA,QAAQ,GAAG,CAAC,+BAA+B;gBACzC,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;YACtD;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT;gBACA,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;gBACpD,YAAY,SAAS,EAAE;gBACvB,SAAS,SAAS,EAAE,GAChB,2CACA;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT;gBACA,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,YAAY;gBACZ,SAAS;YACX;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}