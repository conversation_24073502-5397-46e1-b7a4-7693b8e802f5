import jwt from 'jsonwebtoken';

export interface OnlyOfficeConfig {
  documentType: 'word' | 'cell' | 'slide';
  document: {
    fileType: string;
    key: string;
    title: string;
    url: string;
    permissions: {
      comment: boolean;
      copy: boolean;
      download: boolean;
      edit: boolean;
      fillForms: boolean;
      modifyFilter: boolean;
      modifyContentControl: boolean;
      review: boolean;
      print: boolean;
    };
  };
  editorConfig: {
    mode: 'edit' | 'view';
    lang: string;
    callbackUrl: string;
    user: {
      id: string;
      name: string;
    };
    customization: {
      autosave: boolean;
      forcesave: boolean;
      comments: boolean;
      compactHeader: boolean;
      compactToolbar: boolean;
      compatibleFeatures: boolean;
      customer: {
        address: string;
        info: string;
        logo: string;
        mail: string;
        name: string;
        phone: string;
        www: string;
      };
      feedback: {
        url: string;
        visible: boolean;
      };
      goback: {
        url: string;
        text: string;
      };
      logo: {
        image: string;
        imageEmbedded: string;
        url: string;
      };
      reviewDisplay: string;
      showReviewChanges: boolean;
      spellcheck: boolean;
      toolbarNoTabs: boolean;
      unit: string;
      zoom: number;
    };
  };
  width: string;
  height: string;
}

export class OnlyOfficeService {
  private serverUrl: string;
  private jwtSecret: string;

  constructor() {
    this.serverUrl = process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080';
    this.jwtSecret = process.env.ONLYOFFICE_JWT_SECRET || '';
  }

  getDocumentType(fileExtension: string): 'word' | 'cell' | 'slide' {
    const wordFormats = ['doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt', 'html', 'htm', 'mht', 'pdf', 'djvu', 'fb2', 'epub', 'xps'];
    const cellFormats = ['xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv'];
    const slideFormats = ['ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp'];

    if (wordFormats.includes(fileExtension.toLowerCase())) {
      return 'word';
    } else if (cellFormats.includes(fileExtension.toLowerCase())) {
      return 'cell';
    } else if (slideFormats.includes(fileExtension.toLowerCase())) {
      return 'slide';
    }
    
    return 'word'; // default
  }

  generateDocumentKey(filename: string, lastModified: number): string {
    const keyData = `${filename}_${lastModified}_${Date.now()}`;
    return Buffer.from(keyData).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);
  }

  createConfig(
    filename: string,
    fileUrl: string,
    callbackUrl: string,
    userId: string = 'user1',
    userName: string = 'User',
    mode: 'edit' | 'view' = 'edit'
  ): OnlyOfficeConfig {
    const fileExtension = filename.split('.').pop() || 'docx'; // Default to docx if no extension
    const documentType = this.getDocumentType(fileExtension);
    const documentKey = this.generateDocumentKey(filename, Date.now());

    const config: OnlyOfficeConfig = {
      documentType,
      document: {
        fileType: fileExtension,
        key: documentKey,
        title: filename,
        url: fileUrl,
        permissions: {
          comment: true,
          copy: true,
          download: true,
          edit: mode === 'edit',
          fillForms: true,
          modifyFilter: true,
          modifyContentControl: true,
          review: true,
          print: true,
        },
      },
      editorConfig: {
        mode,
        lang: 'en',
        callbackUrl,
        user: {
          id: userId,
          name: userName,
        },
        customization: {
          autosave: true,
          forcesave: false,
          comments: true,
          compactHeader: false,
          compactToolbar: false,
          compatibleFeatures: false,
          customer: {
            address: '',
            info: '',
            logo: '',
            mail: '',
            name: 'OnlyOffice Demo',
            phone: '',
            www: '',
          },
          feedback: {
            url: '',
            visible: false,
          },
          goback: {
            url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
            text: 'Back to Documents',
          },
          logo: {
            image: '',
            imageEmbedded: '',
            url: '',
          },
          reviewDisplay: 'original',
          showReviewChanges: false,
          spellcheck: true,
          toolbarNoTabs: false,
          unit: 'cm',
          zoom: 100,
        },
      },
      width: '100%',
      height: '100%',
    };

    return config;
  }

  signConfig(config: OnlyOfficeConfig): string | null {
    // Skip JWT signing if no secret is provided (local Docker instance)
    if (!this.jwtSecret) {
      console.log('JWT signing disabled - using local OnlyOffice Docker instance');
      return null;
    }

    // OnlyOffice expects the entire config as payload
    return jwt.sign(config, this.jwtSecret, { algorithm: 'HS256' });
  }

  verifyToken(token: string): any {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }
}
