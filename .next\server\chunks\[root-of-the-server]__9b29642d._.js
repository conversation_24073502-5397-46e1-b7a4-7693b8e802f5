module.exports = {

"[project]/.next-internal/server/app/api/onlyoffice/config/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/onlyoffice.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OnlyOfficeService": (()=>OnlyOfficeService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
;
class OnlyOfficeService {
    serverUrl;
    jwtSecret;
    constructor(){
        this.serverUrl = ("TURBOPACK compile-time value", "https://only.34sy.org") || 'https://only.34sy.org';
        this.jwtSecret = process.env.ONLYOFFICE_JWT_SECRET || 'fzX5rJRaYYPtns6t';
    }
    getDocumentType(fileExtension) {
        const wordFormats = [
            'doc',
            'docx',
            'docm',
            'dot',
            'dotx',
            'dotm',
            'odt',
            'fodt',
            'ott',
            'rtf',
            'txt',
            'html',
            'htm',
            'mht',
            'pdf',
            'djvu',
            'fb2',
            'epub',
            'xps'
        ];
        const cellFormats = [
            'xls',
            'xlsx',
            'xlsm',
            'xlt',
            'xltx',
            'xltm',
            'ods',
            'fods',
            'ots',
            'csv'
        ];
        const slideFormats = [
            'ppt',
            'pptx',
            'pptm',
            'pot',
            'potx',
            'potm',
            'odp',
            'fodp',
            'otp'
        ];
        if (wordFormats.includes(fileExtension.toLowerCase())) {
            return 'word';
        } else if (cellFormats.includes(fileExtension.toLowerCase())) {
            return 'cell';
        } else if (slideFormats.includes(fileExtension.toLowerCase())) {
            return 'slide';
        }
        return 'word'; // default
    }
    generateDocumentKey(filename, lastModified) {
        const keyData = `${filename}_${lastModified}_${Date.now()}`;
        return Buffer.from(keyData).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);
    }
    createConfig(filename, fileUrl, callbackUrl, userId = 'user1', userName = 'User', mode = 'edit') {
        const fileExtension = filename.split('.').pop() || 'docx'; // Default to docx if no extension
        const documentType = this.getDocumentType(fileExtension);
        const documentKey = this.generateDocumentKey(filename, Date.now());
        const config = {
            documentType,
            document: {
                fileType: fileExtension,
                key: documentKey,
                title: filename,
                url: fileUrl,
                permissions: {
                    comment: true,
                    copy: true,
                    download: true,
                    edit: mode === 'edit',
                    fillForms: true,
                    modifyFilter: true,
                    modifyContentControl: true,
                    review: true,
                    print: true
                }
            },
            editorConfig: {
                mode,
                lang: 'en',
                callbackUrl,
                user: {
                    id: userId,
                    name: userName
                },
                customization: {
                    autosave: true,
                    forcesave: false,
                    comments: true,
                    compactHeader: false,
                    compactToolbar: false,
                    compatibleFeatures: false,
                    customer: {
                        address: '',
                        info: '',
                        logo: '',
                        mail: '',
                        name: 'OnlyOffice Demo',
                        phone: '',
                        www: ''
                    },
                    feedback: {
                        url: '',
                        visible: false
                    },
                    goback: {
                        url: ("TURBOPACK compile-time value", "http://localhost:3001") || 'http://localhost:3000',
                        text: 'Back to Documents'
                    },
                    logo: {
                        image: '',
                        imageEmbedded: '',
                        url: ''
                    },
                    reviewDisplay: 'original',
                    showReviewChanges: false,
                    spellcheck: true,
                    toolbarNoTabs: false,
                    unit: 'cm',
                    zoom: 100
                }
            },
            width: '100%',
            height: '100%'
        };
        return config;
    }
    signConfig(config) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(config, this.jwtSecret, {
            algorithm: 'HS256'
        });
    }
    verifyToken(token) {
        try {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, this.jwtSecret);
        } catch (error) {
            throw new Error('Invalid JWT token');
        }
    }
}
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/fileUtils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FileManager": (()=>FileManager)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
;
;
;
class FileManager {
    uploadDir;
    constructor(){
        this.uploadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'uploads');
        this.ensureUploadDir();
    }
    ensureUploadDir() {
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(this.uploadDir)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(this.uploadDir, {
                recursive: true
            });
        }
    }
    async saveFile(file) {
        const fileId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(file.name);
        const filename = `${fileId}${extension}`;
        const filepath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, filename);
        // Convert File to Buffer
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        // Save file
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filepath, buffer);
        const fileInfo = {
            id: fileId,
            name: file.name || `document${extension}`,
            size: file.size,
            type: file.type,
            extension: extension.slice(1) || 'unknown',
            uploadDate: new Date(),
            lastModified: new Date(),
            url: `/uploads/${filename}`
        };
        // Save metadata
        this.saveFileMetadata(fileInfo);
        return fileInfo;
    }
    saveFileMetadata(fileInfo) {
        const metadataPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, `${fileInfo.id}.json`);
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(metadataPath, JSON.stringify(fileInfo, null, 2));
    }
    getFileMetadata(fileId) {
        try {
            const metadataPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, `${fileId}.json`);
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(metadataPath)) {
                return null;
            }
            const metadata = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(metadataPath, 'utf-8');
            return JSON.parse(metadata);
        } catch (error) {
            return null;
        }
    }
    getAllFiles() {
        try {
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(this.uploadDir);
            const metadataFiles = files.filter((file)=>file.endsWith('.json'));
            return metadataFiles.map((file)=>{
                const metadata = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, file), 'utf-8');
                return JSON.parse(metadata);
            }).sort((a, b)=>new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());
        } catch (error) {
            return [];
        }
    }
    deleteFile(fileId) {
        try {
            const fileInfo = this.getFileMetadata(fileId);
            if (!fileInfo) {
                return false;
            }
            const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(fileInfo.name);
            const filename = `${fileId}${extension}`;
            const filepath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, filename);
            const metadataPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, `${fileId}.json`);
            // Delete file and metadata
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filepath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(filepath);
            }
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(metadataPath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(metadataPath);
            }
            return true;
        } catch (error) {
            return false;
        }
    }
    getFileUrl(fileId) {
        const fileInfo = this.getFileMetadata(fileId);
        if (!fileInfo) {
            return null;
        }
        const baseUrl = ("TURBOPACK compile-time value", "http://localhost:3001") || 'http://localhost:3000';
        return `${baseUrl}${fileInfo.url}`;
    }
    isValidFileType(filename) {
        const allowedExtensions = [
            // Word documents
            'doc',
            'docx',
            'docm',
            'dot',
            'dotx',
            'dotm',
            'odt',
            'fodt',
            'ott',
            'rtf',
            'txt',
            // Excel spreadsheets
            'xls',
            'xlsx',
            'xlsm',
            'xlt',
            'xltx',
            'xltm',
            'ods',
            'fods',
            'ots',
            'csv',
            // PowerPoint presentations
            'ppt',
            'pptx',
            'pptm',
            'pot',
            'potx',
            'potm',
            'odp',
            'fodp',
            'otp',
            // PDF
            'pdf'
        ];
        const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filename).slice(1).toLowerCase();
        return allowedExtensions.includes(extension);
    }
}
}}),
"[project]/src/lib/security.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SecurityService": (()=>SecurityService),
    "securityService": (()=>securityService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
;
class SecurityService {
    jwtSecret;
    constructor(){
        this.jwtSecret = process.env.ONLYOFFICE_JWT_SECRET || 'fzX5rJRaYYPtns6t';
    }
    /**
   * Validate JWT token from OnlyOffice callback
   */ validateOnlyOfficeToken(token) {
        try {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, this.jwtSecret);
        } catch (error) {
            throw new Error('Invalid OnlyOffice JWT token');
        }
    }
    /**
   * Sign data with JWT for OnlyOffice
   */ signOnlyOfficeData(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(data, this.jwtSecret, {
            algorithm: 'HS256'
        });
    }
    /**
   * Extract and validate JWT from request headers
   */ extractTokenFromRequest(request) {
        const authHeader = request.headers.get('authorization');
        if (!authHeader) return null;
        const token = authHeader.replace('Bearer ', '');
        return token;
    }
    /**
   * Validate file upload security
   */ validateFileUpload(file) {
        // Check file size (50MB limit)
        const maxSize = 50 * 1024 * 1024;
        if (file.size > maxSize) {
            return {
                valid: false,
                error: 'File size exceeds 50MB limit'
            };
        }
        // Check file type
        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/msword',
            'application/vnd.ms-excel',
            'application/vnd.ms-powerpoint',
            'application/pdf',
            'text/plain',
            'text/csv',
            'application/vnd.oasis.opendocument.text',
            'application/vnd.oasis.opendocument.spreadsheet',
            'application/vnd.oasis.opendocument.presentation'
        ];
        if (!allowedTypes.includes(file.type) && file.type !== '') {
            // Also check by extension if MIME type is not recognized
            const extension = file.name.split('.').pop()?.toLowerCase();
            const allowedExtensions = [
                'doc',
                'docx',
                'docm',
                'dot',
                'dotx',
                'dotm',
                'odt',
                'fodt',
                'ott',
                'rtf',
                'txt',
                'xls',
                'xlsx',
                'xlsm',
                'xlt',
                'xltx',
                'xltm',
                'ods',
                'fods',
                'ots',
                'csv',
                'ppt',
                'pptx',
                'pptm',
                'pot',
                'potx',
                'potm',
                'odp',
                'fodp',
                'otp',
                'pdf'
            ];
            if (!extension || !allowedExtensions.includes(extension)) {
                return {
                    valid: false,
                    error: 'File type not supported'
                };
            }
        }
        // Check filename for security
        if (this.containsSuspiciousPatterns(file.name)) {
            return {
                valid: false,
                error: 'Filename contains invalid characters'
            };
        }
        return {
            valid: true
        };
    }
    /**
   * Check for suspicious patterns in filenames
   */ containsSuspiciousPatterns(filename) {
        const suspiciousPatterns = [
            /\.\./,
            /[<>:"|?*]/,
            /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,
            /^\./,
            /\.(exe|bat|cmd|scr|vbs|js|jar|com|pif)$/i
        ];
        return suspiciousPatterns.some((pattern)=>pattern.test(filename));
    }
    /**
   * Sanitize filename for safe storage
   */ sanitizeFilename(filename) {
        // Remove or replace unsafe characters
        return filename.replace(/[^a-zA-Z0-9._-]/g, '_') // Replace unsafe chars with underscore
        .replace(/_{2,}/g, '_') // Replace multiple underscores with single
        .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
        .substring(0, 255); // Limit length
    }
    /**
   * Generate secure document key
   */ generateSecureDocumentKey(fileId, timestamp) {
        const data = `${fileId}_${timestamp}_${this.jwtSecret}`;
        return Buffer.from(data).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);
    }
    /**
   * Validate request origin
   */ validateOrigin(request) {
        const origin = request.headers.get('origin');
        const referer = request.headers.get('referer');
        const allowedOrigins = [
            'https://only.34sy.org',
            'http://localhost:3000',
            'http://localhost:3001',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
            ("TURBOPACK compile-time value", "http://localhost:3001")
        ].filter(Boolean);
        // Allow requests without origin (direct API calls, same-origin requests)
        if (!origin && !referer) return true;
        // Check origin
        if (origin && allowedOrigins.includes(origin)) return true;
        // Check referer as fallback
        if (referer) {
            try {
                const refererUrl = new URL(referer);
                const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`;
                return allowedOrigins.includes(refererOrigin);
            } catch  {
                return false;
            }
        }
        // Allow same-origin requests (when origin is null but referer matches)
        return true;
    }
    /**
   * Rate limiting check (simple in-memory implementation)
   */ rateLimitStore = new Map();
    checkRateLimit(identifier, maxRequests = 100, windowMs = 60000) {
        const now = Date.now();
        const record = this.rateLimitStore.get(identifier);
        if (!record || now > record.resetTime) {
            this.rateLimitStore.set(identifier, {
                count: 1,
                resetTime: now + windowMs
            });
            return true;
        }
        if (record.count >= maxRequests) {
            return false;
        }
        record.count++;
        return true;
    }
    /**
   * Clean up expired rate limit records
   */ cleanupRateLimit() {
        const now = Date.now();
        for (const [key, record] of this.rateLimitStore.entries()){
            if (now > record.resetTime) {
                this.rateLimitStore.delete(key);
            }
        }
    }
}
const securityService = new SecurityService();
}}),
"[project]/src/app/api/onlyoffice/config/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$onlyoffice$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/onlyoffice.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fileUtils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/fileUtils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security.ts [app-route] (ecmascript)");
;
;
;
;
async function POST(request) {
    try {
        // Validate origin
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityService"].validateOrigin(request)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid origin'
            }, {
                status: 403
            });
        }
        // Rate limiting
        const clientIp = request.headers.get('x-forwarded-for') || 'unknown';
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityService"].checkRateLimit(`config_${clientIp}`, 20, 60000)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Rate limit exceeded'
            }, {
                status: 429
            });
        }
        const { fileId, mode = 'edit', userId = 'user1', userName = 'User' } = await request.json();
        if (!fileId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File ID is required'
            }, {
                status: 400
            });
        }
        const fileManager = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fileUtils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FileManager"]();
        const fileInfo = fileManager.getFileMetadata(fileId);
        if (!fileInfo) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File not found'
            }, {
                status: 404
            });
        }
        const onlyOfficeService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$onlyoffice$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OnlyOfficeService"]();
        const baseUrl = ("TURBOPACK compile-time value", "http://localhost:3001") || 'http://localhost:3001';
        // Use the dedicated file serving endpoint for OnlyOffice
        const fileUrl = `${baseUrl}/api/files/serve/${fileId}`;
        const callbackUrl = `${baseUrl}/api/onlyoffice/callback`;
        console.log('Creating OnlyOffice config for:', {
            fileId,
            fileName: fileInfo.name,
            fileUrl,
            callbackUrl,
            mode
        });
        // Create OnlyOffice configuration
        const config = onlyOfficeService.createConfig(fileInfo.name, fileUrl, callbackUrl, userId, userName, mode);
        // Sign the configuration with JWT
        const token = onlyOfficeService.signConfig(config);
        console.log('OnlyOffice config created successfully:', {
            documentType: config.documentType,
            documentKey: config.document.key,
            hasToken: !!token
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            config,
            token,
            serverUrl: ("TURBOPACK compile-time value", "https://only.34sy.org") || 'https://only.34sy.org'
        });
    } catch (error) {
        console.error('Config generation error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate configuration'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9b29642d._.js.map