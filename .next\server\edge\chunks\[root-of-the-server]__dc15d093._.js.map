{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport function middleware(request: NextRequest) {\n  // Create response\n  const response = NextResponse.next();\n\n  // CORS headers for OnlyOffice integration\n  const origin = request.headers.get('origin');\n  const allowedOrigins = [\n    'https://only.34sy.org',\n    'http://localhost:3000',\n    'http://localhost:3001',\n    'http://127.0.0.1:3000',\n    'http://127.0.0.1:3001',\n    process.env.NEXT_PUBLIC_APP_URL,\n  ].filter(Boolean);\n\n  // For file serving endpoints, allow all origins (OnlyOffice server needs access)\n  if (request.nextUrl.pathname.startsWith('/api/files/serve/')) {\n    response.headers.set('Access-Control-Allow-Origin', '*');\n    response.headers.set('Access-Control-Allow-Credentials', 'false');\n  } else if (origin && allowedOrigins.includes(origin)) {\n    response.headers.set('Access-Control-Allow-Origin', origin);\n    response.headers.set('Access-Control-Allow-Credentials', 'true');\n  }\n\n  response.headers.set(\n    'Access-Control-Allow-Methods',\n    'GET, POST, PUT, DELETE, OPTIONS'\n  );\n  response.headers.set(\n    'Access-Control-Allow-Headers',\n    'Content-Type, Authorization, X-Requested-With, Accept, Origin'\n  );\n\n  // Security headers\n  response.headers.set('X-Frame-Options', 'SAMEORIGIN');\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  response.headers.set(\n    'Content-Security-Policy',\n    \"default-src 'self'; \" +\n    \"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://only.34sy.org; \" +\n    \"style-src 'self' 'unsafe-inline'; \" +\n    \"img-src 'self' data: https:; \" +\n    \"font-src 'self' data:; \" +\n    \"connect-src 'self' https://only.34sy.org; \" +\n    \"frame-src 'self' https://only.34sy.org; \" +\n    \"object-src 'none'; \" +\n    \"base-uri 'self';\"\n  );\n\n  // Handle preflight requests\n  if (request.method === 'OPTIONS') {\n    return new Response(null, { status: 200, headers: response.headers });\n  }\n\n  return response;\n}\n\nexport const config = {\n  matcher: [\n    '/api/:path*',\n    '/editor/:path*',\n    '/uploads/:path*',\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,SAAS,WAAW,OAAoB;IAC7C,kBAAkB;IAClB,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,0CAA0C;IAC1C,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IACnC,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;;KAED,CAAC,MAAM,CAAC;IAET,iFAAiF;IACjF,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,sBAAsB;QAC5D,SAAS,OAAO,CAAC,GAAG,CAAC,+BAA+B;QACpD,SAAS,OAAO,CAAC,GAAG,CAAC,oCAAoC;IAC3D,OAAO,IAAI,UAAU,eAAe,QAAQ,CAAC,SAAS;QACpD,SAAS,OAAO,CAAC,GAAG,CAAC,+BAA+B;QACpD,SAAS,OAAO,CAAC,GAAG,CAAC,oCAAoC;IAC3D;IAEA,SAAS,OAAO,CAAC,GAAG,CAClB,gCACA;IAEF,SAAS,OAAO,CAAC,GAAG,CAClB,gCACA;IAGF,mBAAmB;IACnB,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAClB,2BACA,yBACA,4EACA,uCACA,kCACA,4BACA,+CACA,6CACA,wBACA;IAGF,4BAA4B;IAC5B,IAAI,QAAQ,MAAM,KAAK,WAAW;QAChC,OAAO,IAAI,SAAS,MAAM;YAAE,QAAQ;YAAK,SAAS,SAAS,OAAO;QAAC;IACrE;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;QACA;KACD;AACH"}}]}