import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const debugInfo = {
    currentUrl: request.url,
    host: request.headers.get('host'),
    origin: request.headers.get('origin'),
    referer: request.headers.get('referer'),
    userAgent: request.headers.get('user-agent'),
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      ONLYOFFICE_SERVER_URL: process.env.ONLYOFFICE_SERVER_URL,
      ONLYOFFICE_JWT_DISABLED: process.env.ONLYOFFICE_JWT_DISABLED,
      PORT: process.env.PORT
    },
    detectedPort: request.headers.get('host')?.split(':')[1] || 'unknown'
  };

  return NextResponse.json({
    success: true,
    debug: debugInfo,
    message: 'Port and environment debug information'
  });
} 