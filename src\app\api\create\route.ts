import { NextRequest, NextResponse } from 'next/server';
import { FileManager } from '@/lib/fileUtils';
import { SecurityService } from '@/lib/security';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { fileType, fileName } = await request.json();

    // Validate input
    if (!fileType || !fileName) {
      return NextResponse.json(
        { error: 'File type and name are required' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['excel', 'word', 'powerpoint'];
    if (!allowedTypes.includes(fileType)) {
      return NextResponse.json(
        { error: 'Invalid file type. Supported types: excel, word, powerpoint' },
        { status: 400 }
      );
    }

    const securityService = new SecurityService();
    const sanitizedFileName = securityService.sanitizeFilename(fileName);

    // Get template file path and MIME type
    const templateInfo = getTemplateInfo(fileType);
    const templatePath = path.join(process.cwd(), 'templates', templateInfo.templateFile);

    // Check if template exists
    if (!fs.existsSync(templatePath)) {
      return NextResponse.json(
        { error: 'Template file not found' },
        { status: 500 }
      );
    }

    // Read template file
    const templateBuffer = fs.readFileSync(templatePath);

    // Create a File object from the template
    const fullFileName = `${sanitizedFileName}${templateInfo.extension}`;
    const file = new File([templateBuffer], fullFileName, {
      type: templateInfo.mimeType,
      lastModified: Date.now(),
    });

    // Save the file using FileManager
    const fileManager = new FileManager();
    const fileInfo = await fileManager.saveFile(file);

    return NextResponse.json({
      success: true,
      file: fileInfo,
    });
  } catch (error) {
    console.error('Create file error:', error);
    return NextResponse.json(
      { error: 'Failed to create file' },
      { status: 500 }
    );
  }
}

function getTemplateInfo(fileType: string) {
  switch (fileType) {
    case 'excel':
      return {
        templateFile: 'template.xlsx',
        extension: '.xlsx',
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
    case 'word':
      return {
        templateFile: 'template.docx',
        extension: '.docx',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      };
    case 'powerpoint':
      return {
        templateFile: 'template.pptx',
        extension: '.pptx',
        mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      };
    default:
      throw new Error('Invalid file type');
  }
}