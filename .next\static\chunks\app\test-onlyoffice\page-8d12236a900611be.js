(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[416],{578:(e,o,t)=>{"use strict";t.r(o),t.d(o,{default:()=>n});var a=t(5155),s=t(2115);function n(){let[e,o]=(0,s.useState)([]),[t,n]=(0,s.useState)("Not loaded"),c=e=>{o(o=>[...o,"".concat(new Date().toLocaleTimeString(),": ").concat(e)])};return(0,s.useEffect)(()=>{if(c("Starting OnlyOffice API test..."),window.DocsAPI){c("DocsAPI already available on window"),n("Already available");return}c("DocsAPI not available, loading script...");let e=document.createElement("script");return e.src="https://only.34sy.org/web-apps/apps/api/documents/api.js",e.async=!0,e.crossOrigin="anonymous",e.onload=()=>{c("Script loaded successfully");let e=function(){let o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(c("Checking for DocsAPI, attempt ".concat(o+1,"/10")),c("window.DocsAPI: ".concat(typeof window.DocsAPI)),window.DocsAPI)c("DocsAPI found! Type: ".concat(typeof window.DocsAPI)),c("DocsAPI.DocEditor: ".concat(typeof window.DocsAPI.DocEditor)),"function"==typeof window.DocsAPI.DocEditor?(c("DocsAPI.DocEditor is a function - SUCCESS!"),n("Available and ready")):(c("DocsAPI.DocEditor is not a function"),n("Available but DocEditor missing"));else if(o<10)c("DocsAPI not yet available, retrying in 100ms..."),setTimeout(()=>e(o+1),100);else{c("DocsAPI not available after 10 attempts"),n("Failed to load");let e=Object.keys(window).filter(e=>e.toLowerCase().includes("docs")||e.toLowerCase().includes("only")||e.toLowerCase().includes("office"));c("Related window properties: ".concat(e.join(", ")))}};e()},e.onerror=e=>{c("Script loading error: ".concat(e)),n("Script loading failed")},document.head.appendChild(e),()=>{document.querySelectorAll('script[src*="only.34sy.org"]').forEach(e=>e.remove())}},[]),(0,a.jsxs)("div",{className:"container mx-auto p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"OnlyOffice API Test"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Status"}),(0,a.jsx)("div",{className:"p-3 rounded ".concat("Available and ready"===t?"bg-green-100 text-green-800":"Failed to load"===t||"Script loading failed"===t?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:t})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Debug Logs"}),(0,a.jsx)("div",{className:"bg-gray-100 p-4 rounded max-h-96 overflow-y-auto",children:e.map((e,o)=>(0,a.jsx)("div",{className:"text-sm font-mono mb-1",children:e},o))})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("button",{onClick:()=>{o([]),n("Not loaded"),window.location.reload()},className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",children:"Reload Test"})})]})}},4873:(e,o,t)=>{Promise.resolve().then(t.bind(t,578))}},e=>{var o=o=>e(e.s=o);e.O(0,[441,684,358],()=>o(4873)),_N_E=e.O()}]);