{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/api/test-onlyoffice/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    console.log('Testing OnlyOffice server accessibility...');\n    \n    // Test if the OnlyOffice server is accessible\n    const response = await fetch('https://only.34sy.org/web-apps/apps/api/documents/api.js', {\n      method: 'HEAD',\n      headers: {\n        'User-Agent': 'OnlyOffice-Test-Client'\n      }\n    });\n\n    console.log('OnlyOffice server response:', {\n      status: response.status,\n      statusText: response.statusText,\n      headers: Object.fromEntries(response.headers.entries())\n    });\n\n    if (response.ok) {\n      return NextResponse.json({\n        success: true,\n        message: 'OnlyOffice server is accessible',\n        status: response.status,\n        headers: Object.fromEntries(response.headers.entries())\n      });\n    } else {\n      return NextResponse.json({\n        success: false,\n        message: 'OnlyOffice server returned error',\n        status: response.status,\n        statusText: response.statusText\n      }, { status: 500 });\n    }\n  } catch (error) {\n    console.error('Error testing OnlyOffice server:', error);\n    return NextResponse.json({\n      success: false,\n      message: 'Failed to connect to OnlyOffice server',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,8CAA8C;QAC9C,MAAM,WAAW,MAAM,MAAM,4DAA4D;YACvF,QAAQ;YACR,SAAS;gBACP,cAAc;YAChB;QACF;QAEA,QAAQ,GAAG,CAAC,+BAA+B;YACzC,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,SAAS,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;QACtD;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,QAAQ,SAAS,MAAM;gBACvB,SAAS,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;YACtD;QACF,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;YACjC,GAAG;gBAAE,QAAQ;YAAI;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}