{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/lib/fileUtils.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { v4 as uuidv4 } from 'uuid';\n\nexport interface FileInfo {\n  id: string;\n  name: string;\n  size: number;\n  type: string;\n  extension: string;\n  uploadDate: Date;\n  lastModified: Date;\n  url: string;\n}\n\nexport class FileManager {\n  private uploadDir: string;\n\n  constructor() {\n    this.uploadDir = path.join(process.cwd(), 'public', 'uploads');\n    this.ensureUploadDir();\n  }\n\n  private ensureUploadDir(): void {\n    if (!fs.existsSync(this.uploadDir)) {\n      fs.mkdirSync(this.uploadDir, { recursive: true });\n    }\n  }\n\n  async saveFile(file: File): Promise<FileInfo> {\n    const fileId = uuidv4();\n    const extension = path.extname(file.name);\n    const filename = `${fileId}${extension}`;\n    const filepath = path.join(this.uploadDir, filename);\n\n    // Convert File to Buffer\n    const arrayBuffer = await file.arrayBuffer();\n    const buffer = Buffer.from(arrayBuffer);\n\n    // Save file\n    fs.writeFileSync(filepath, buffer);\n\n    const fileInfo: FileInfo = {\n      id: fileId,\n      name: file.name || `document${extension}`, // Ensure we have a proper name\n      size: file.size,\n      type: file.type,\n      extension: extension.slice(1) || 'unknown', // Remove the dot, fallback to 'unknown'\n      uploadDate: new Date(),\n      lastModified: new Date(),\n      url: `/uploads/${filename}`,\n    };\n\n    // Save metadata\n    this.saveFileMetadata(fileInfo);\n\n    return fileInfo;\n  }\n\n  private saveFileMetadata(fileInfo: FileInfo): void {\n    const metadataPath = path.join(this.uploadDir, `${fileInfo.id}.json`);\n    fs.writeFileSync(metadataPath, JSON.stringify(fileInfo, null, 2));\n  }\n\n  getFileMetadata(fileId: string): FileInfo | null {\n    try {\n      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);\n      if (!fs.existsSync(metadataPath)) {\n        return null;\n      }\n      const metadata = fs.readFileSync(metadataPath, 'utf-8');\n      return JSON.parse(metadata);\n    } catch (error) {\n      return null;\n    }\n  }\n\n  getAllFiles(): FileInfo[] {\n    try {\n      const files = fs.readdirSync(this.uploadDir);\n      const metadataFiles = files.filter(file => file.endsWith('.json'));\n      \n      return metadataFiles.map(file => {\n        const metadata = fs.readFileSync(path.join(this.uploadDir, file), 'utf-8');\n        return JSON.parse(metadata);\n      }).sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());\n    } catch (error) {\n      return [];\n    }\n  }\n\n  deleteFile(fileId: string): boolean {\n    try {\n      const fileInfo = this.getFileMetadata(fileId);\n      if (!fileInfo) {\n        return false;\n      }\n\n      const extension = path.extname(fileInfo.name);\n      const filename = `${fileId}${extension}`;\n      const filepath = path.join(this.uploadDir, filename);\n      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);\n\n      // Delete file and metadata\n      if (fs.existsSync(filepath)) {\n        fs.unlinkSync(filepath);\n      }\n      if (fs.existsSync(metadataPath)) {\n        fs.unlinkSync(metadataPath);\n      }\n\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  getFileUrl(fileId: string): string | null {\n    const fileInfo = this.getFileMetadata(fileId);\n    if (!fileInfo) {\n      return null;\n    }\n    \n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';\n    return `${baseUrl}${fileInfo.url}`;\n  }\n\n  isValidFileType(filename: string): boolean {\n    const allowedExtensions = [\n      // Word documents\n      'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt',\n      // Excel spreadsheets\n      'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv',\n      // PowerPoint presentations\n      'ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp',\n      // PDF\n      'pdf'\n    ];\n\n    const extension = path.extname(filename).slice(1).toLowerCase();\n    return allowedExtensions.includes(extension);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAaO,MAAM;IACH,UAAkB;IAE1B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACpD,IAAI,CAAC,eAAe;IACtB;IAEQ,kBAAwB;QAC9B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,GAAG;YAClC,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE;gBAAE,WAAW;YAAK;QACjD;IACF;IAEA,MAAM,SAAS,IAAU,EAAqB;QAC5C,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QACpB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,KAAK,IAAI;QACxC,MAAM,WAAW,GAAG,SAAS,WAAW;QACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAE3C,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,YAAY;QACZ,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU;QAE3B,MAAM,WAAqB;YACzB,IAAI;YACJ,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,WAAW;YACzC,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,WAAW,UAAU,KAAK,CAAC,MAAM;YACjC,YAAY,IAAI;YAChB,cAAc,IAAI;YAClB,KAAK,CAAC,SAAS,EAAE,UAAU;QAC7B;QAEA,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,CAAC;QAEtB,OAAO;IACT;IAEQ,iBAAiB,QAAkB,EAAQ;QACjD,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC,KAAK,CAAC;QACpE,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,cAAc,KAAK,SAAS,CAAC,UAAU,MAAM;IAChE;IAEA,gBAAgB,MAAc,EAAmB;QAC/C,IAAI;YACF,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,OAAO,KAAK,CAAC;YAC/D,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;gBAChC,OAAO;YACT;YACA,MAAM,WAAW,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,cAAc;YAC/C,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,cAA0B;QACxB,IAAI;YACF,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YAC3C,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;YAEzD,OAAO,cAAc,GAAG,CAAC,CAAA;gBACvB,MAAM,WAAW,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO;gBAClE,OAAO,KAAK,KAAK,CAAC;YACpB,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QACrF,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA,WAAW,MAAc,EAAW;QAClC,IAAI;YACF,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;YACtC,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YAEA,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,SAAS,IAAI;YAC5C,MAAM,WAAW,GAAG,SAAS,WAAW;YACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC3C,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,OAAO,KAAK,CAAC;YAE/D,2BAA2B;YAC3B,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;gBAC3B,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YAChB;YACA,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;gBAC/B,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YAChB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,WAAW,MAAc,EAAiB;QACxC,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QACtC,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,MAAM,UAAU,iEAAmC;QACnD,OAAO,GAAG,UAAU,SAAS,GAAG,EAAE;IACpC;IAEA,gBAAgB,QAAgB,EAAW;QACzC,MAAM,oBAAoB;YACxB,iBAAiB;YACjB;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YAAO;YAC3E,qBAAqB;YACrB;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YACpE,2BAA2B;YAC3B;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAC7D,MAAM;YACN;SACD;QAED,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,GAAG,WAAW;QAC7D,OAAO,kBAAkB,QAAQ,CAAC;IACpC;AACF", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/api/debug-onlyoffice/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { FileManager } from '@/lib/fileUtils';\r\nimport fs from 'fs';\r\nimport path from 'path';\r\n\r\nexport async function GET(request: NextRequest) {\r\n  const debugInfo = {\r\n    timestamp: new Date().toISOString(),\r\n    environment: {\r\n      ONLYOFFICE_SERVER_URL: process.env.ONLYOFFICE_SERVER_URL || 'NOT SET',\r\n      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'NOT SET',\r\n      ONLYOFFICE_JWT_SECRET: process.env.ONLYOFFICE_JWT_SECRET ? 'SET' : 'NOT SET',\r\n      NODE_ENV: process.env.NODE_ENV || 'NOT SET',\r\n      PORT: process.env.PORT || 'NOT SET'\r\n    },\r\n    files: {\r\n      uploadDir: path.join(process.cwd(), 'public', 'uploads'),\r\n      exists: false,\r\n      contents: [] as string[]\r\n    },\r\n    onlyofficeServer: {\r\n      url: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',\r\n      accessible: false,\r\n      response: null as any\r\n    },\r\n    sampleFile: {\r\n      exists: false,\r\n      accessible: false,\r\n      fileId: null as string | null,\r\n      metadata: null as any\r\n    }\r\n  };\r\n\r\n  try {\r\n    // Check uploads directory\r\n    const uploadDir = path.join(process.cwd(), 'public', 'uploads');\r\n    if (fs.existsSync(uploadDir)) {\r\n      debugInfo.files.exists = true;\r\n      debugInfo.files.contents = fs.readdirSync(uploadDir).filter(f => !f.startsWith('.'));\r\n    }\r\n\r\n    // Check if we have any files\r\n    const fileManager = new FileManager();\r\n    const allFiles = fileManager.getAllFiles();\r\n    if (allFiles.length > 0) {\r\n      const sampleFile = allFiles[0];\r\n      debugInfo.sampleFile.exists = true;\r\n      debugInfo.sampleFile.fileId = sampleFile.id;\r\n      debugInfo.sampleFile.metadata = sampleFile;\r\n\r\n      // Check if file is accessible\r\n      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001';\r\n      const fileUrl = `${baseUrl}/api/files/serve/${sampleFile.id}`;\r\n      \r\n      try {\r\n        const fileResponse = await fetch(fileUrl, { method: 'HEAD' });\r\n        debugInfo.sampleFile.accessible = fileResponse.ok;\r\n      } catch (error) {\r\n        debugInfo.sampleFile.accessible = false;\r\n      }\r\n    }\r\n\r\n    // Test OnlyOffice server accessibility\r\n    const onlyofficeUrl = process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080';\r\n    try {\r\n      const response = await fetch(`${onlyofficeUrl}/healthcheck`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Accept': 'application/json'\r\n        }\r\n      });\r\n      debugInfo.onlyofficeServer.accessible = response.ok;\r\n      debugInfo.onlyofficeServer.response = {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        headers: Object.fromEntries(response.headers.entries())\r\n      };\r\n    } catch (error) {\r\n      debugInfo.onlyofficeServer.accessible = false;\r\n      debugInfo.onlyofficeServer.response = {\r\n        error: error instanceof Error ? error.message : 'Unknown error'\r\n      };\r\n    }\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      debug: debugInfo,\r\n      recommendations: generateRecommendations(debugInfo)\r\n    });\r\n  } catch (error) {\r\n    return NextResponse.json({\r\n      success: false,\r\n      error: error instanceof Error ? error.message : 'Unknown error',\r\n      debug: debugInfo\r\n    });\r\n  }\r\n}\r\n\r\nfunction generateRecommendations(debugInfo: any): string[] {\r\n  const recommendations: string[] = [];\r\n\r\n  if (debugInfo.environment.ONLYOFFICE_SERVER_URL === 'NOT SET') {\r\n    recommendations.push('Set ONLYOFFICE_SERVER_URL environment variable');\r\n  }\r\n\r\n  if (debugInfo.environment.NEXT_PUBLIC_APP_URL === 'NOT SET') {\r\n    recommendations.push('Set NEXT_PUBLIC_APP_URL environment variable');\r\n  }\r\n\r\n  if (debugInfo.environment.NEXT_PUBLIC_APP_URL.includes('localhost')) {\r\n    recommendations.push('Consider using your machine\\'s IP address instead of localhost for Docker integration');\r\n  }\r\n\r\n  if (!debugInfo.files.exists) {\r\n    recommendations.push('Create uploads directory: public/uploads');\r\n  }\r\n\r\n  if (debugInfo.files.contents.length === 0) {\r\n    recommendations.push('Upload some test files to test the integration');\r\n  }\r\n\r\n  if (!debugInfo.onlyofficeServer.accessible) {\r\n    recommendations.push('Start OnlyOffice Docker container: docker run -d --name onlyoffice -p 8080:80 onlyoffice/documentserver');\r\n  }\r\n\r\n  if (debugInfo.sampleFile.exists && !debugInfo.sampleFile.accessible) {\r\n    recommendations.push('Check file serving endpoint - files may not be accessible from OnlyOffice server');\r\n  }\r\n\r\n  return recommendations;\r\n} "], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,YAAY;QAChB,WAAW,IAAI,OAAO,WAAW;QACjC,aAAa;YACX,uBAAuB,6DAAqC;YAC5D,qBAAqB,iEAAmC;YACxD,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB,GAAG,QAAQ;YACnE,UAAU,mDAAwB;YAClC,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI;QAC5B;QACA,OAAO;YACL,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;YAC9C,QAAQ;YACR,UAAU,EAAE;QACd;QACA,kBAAkB;YAChB,KAAK,6DAAqC;YAC1C,YAAY;YACZ,UAAU;QACZ;QACA,YAAY;YACV,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,IAAI;QACF,0BAA0B;QAC1B,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACrD,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,YAAY;YAC5B,UAAU,KAAK,CAAC,MAAM,GAAG;YACzB,UAAU,KAAK,CAAC,QAAQ,GAAG,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC,WAAW,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,CAAC;QACjF;QAEA,6BAA6B;QAC7B,MAAM,cAAc,IAAI,yHAAA,CAAA,cAAW;QACnC,MAAM,WAAW,YAAY,WAAW;QACxC,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,MAAM,aAAa,QAAQ,CAAC,EAAE;YAC9B,UAAU,UAAU,CAAC,MAAM,GAAG;YAC9B,UAAU,UAAU,CAAC,MAAM,GAAG,WAAW,EAAE;YAC3C,UAAU,UAAU,CAAC,QAAQ,GAAG;YAEhC,8BAA8B;YAC9B,MAAM,UAAU,iEAAmC;YACnD,MAAM,UAAU,GAAG,QAAQ,iBAAiB,EAAE,WAAW,EAAE,EAAE;YAE7D,IAAI;gBACF,MAAM,eAAe,MAAM,MAAM,SAAS;oBAAE,QAAQ;gBAAO;gBAC3D,UAAU,UAAU,CAAC,UAAU,GAAG,aAAa,EAAE;YACnD,EAAE,OAAO,OAAO;gBACd,UAAU,UAAU,CAAC,UAAU,GAAG;YACpC;QACF;QAEA,uCAAuC;QACvC,MAAM,gBAAgB,6DAAqC;QAC3D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,cAAc,YAAY,CAAC,EAAE;gBAC3D,QAAQ;gBACR,SAAS;oBACP,UAAU;gBACZ;YACF;YACA,UAAU,gBAAgB,CAAC,UAAU,GAAG,SAAS,EAAE;YACnD,UAAU,gBAAgB,CAAC,QAAQ,GAAG;gBACpC,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;YACtD;QACF,EAAE,OAAO,OAAO;YACd,UAAU,gBAAgB,CAAC,UAAU,GAAG;YACxC,UAAU,gBAAgB,CAAC,QAAQ,GAAG;gBACpC,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,iBAAiB,wBAAwB;QAC3C;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,OAAO;QACT;IACF;AACF;AAEA,SAAS,wBAAwB,SAAc;IAC7C,MAAM,kBAA4B,EAAE;IAEpC,IAAI,UAAU,WAAW,CAAC,qBAAqB,KAAK,WAAW;QAC7D,gBAAgB,IAAI,CAAC;IACvB;IAEA,IAAI,UAAU,WAAW,CAAC,mBAAmB,KAAK,WAAW;QAC3D,gBAAgB,IAAI,CAAC;IACvB;IAEA,IAAI,UAAU,WAAW,CAAC,mBAAmB,CAAC,QAAQ,CAAC,cAAc;QACnE,gBAAgB,IAAI,CAAC;IACvB;IAEA,IAAI,CAAC,UAAU,KAAK,CAAC,MAAM,EAAE;QAC3B,gBAAgB,IAAI,CAAC;IACvB;IAEA,IAAI,UAAU,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG;QACzC,gBAAgB,IAAI,CAAC;IACvB;IAEA,IAAI,CAAC,UAAU,gBAAgB,CAAC,UAAU,EAAE;QAC1C,gBAAgB,IAAI,CAAC;IACvB;IAEA,IAAI,UAAU,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,UAAU,CAAC,UAAU,EAAE;QACnE,gBAAgB,IAAI,CAAC;IACvB;IAEA,OAAO;AACT", "debugId": null}}]}