module.exports = {

"[project]/.next-internal/server/app/api/debug-onlyoffice/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/fileUtils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FileManager": (()=>FileManager)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
;
;
;
class FileManager {
    uploadDir;
    constructor(){
        this.uploadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'uploads');
        this.ensureUploadDir();
    }
    ensureUploadDir() {
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(this.uploadDir)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(this.uploadDir, {
                recursive: true
            });
        }
    }
    async saveFile(file) {
        const fileId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(file.name);
        const filename = `${fileId}${extension}`;
        const filepath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, filename);
        // Convert File to Buffer
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        // Save file
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filepath, buffer);
        const fileInfo = {
            id: fileId,
            name: file.name || `document${extension}`,
            size: file.size,
            type: file.type,
            extension: extension.slice(1) || 'unknown',
            uploadDate: new Date(),
            lastModified: new Date(),
            url: `/uploads/${filename}`
        };
        // Save metadata
        this.saveFileMetadata(fileInfo);
        return fileInfo;
    }
    saveFileMetadata(fileInfo) {
        const metadataPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, `${fileInfo.id}.json`);
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(metadataPath, JSON.stringify(fileInfo, null, 2));
    }
    getFileMetadata(fileId) {
        try {
            const metadataPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, `${fileId}.json`);
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(metadataPath)) {
                return null;
            }
            const metadata = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(metadataPath, 'utf-8');
            return JSON.parse(metadata);
        } catch (error) {
            return null;
        }
    }
    getAllFiles() {
        try {
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(this.uploadDir);
            const metadataFiles = files.filter((file)=>file.endsWith('.json'));
            return metadataFiles.map((file)=>{
                const metadata = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, file), 'utf-8');
                return JSON.parse(metadata);
            }).sort((a, b)=>new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());
        } catch (error) {
            return [];
        }
    }
    deleteFile(fileId) {
        try {
            const fileInfo = this.getFileMetadata(fileId);
            if (!fileInfo) {
                return false;
            }
            const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(fileInfo.name);
            const filename = `${fileId}${extension}`;
            const filepath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, filename);
            const metadataPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, `${fileId}.json`);
            // Delete file and metadata
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filepath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(filepath);
            }
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(metadataPath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(metadataPath);
            }
            return true;
        } catch (error) {
            return false;
        }
    }
    getFileUrl(fileId) {
        const fileInfo = this.getFileMetadata(fileId);
        if (!fileInfo) {
            return null;
        }
        const baseUrl = ("TURBOPACK compile-time value", "http://*************:3001") || 'http://localhost:3000';
        return `${baseUrl}${fileInfo.url}`;
    }
    isValidFileType(filename) {
        const allowedExtensions = [
            // Word documents
            'doc',
            'docx',
            'docm',
            'dot',
            'dotx',
            'dotm',
            'odt',
            'fodt',
            'ott',
            'rtf',
            'txt',
            // Excel spreadsheets
            'xls',
            'xlsx',
            'xlsm',
            'xlt',
            'xltx',
            'xltm',
            'ods',
            'fods',
            'ots',
            'csv',
            // PowerPoint presentations
            'ppt',
            'pptx',
            'pptm',
            'pot',
            'potx',
            'potm',
            'odp',
            'fodp',
            'otp',
            // PDF
            'pdf'
        ];
        const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filename).slice(1).toLowerCase();
        return allowedExtensions.includes(extension);
    }
}
}}),
"[project]/src/app/api/debug-onlyoffice/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fileUtils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/fileUtils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
async function GET(request) {
    const debugInfo = {
        timestamp: new Date().toISOString(),
        environment: {
            ONLYOFFICE_SERVER_URL: ("TURBOPACK compile-time value", "http://localhost:8080") || 'NOT SET',
            NEXT_PUBLIC_APP_URL: ("TURBOPACK compile-time value", "http://*************:3001") || 'NOT SET',
            ONLYOFFICE_JWT_SECRET: process.env.ONLYOFFICE_JWT_SECRET ? 'SET' : 'NOT SET',
            NODE_ENV: ("TURBOPACK compile-time value", "development") || 'NOT SET',
            PORT: process.env.PORT || 'NOT SET'
        },
        files: {
            uploadDir: __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'uploads'),
            exists: false,
            contents: []
        },
        onlyofficeServer: {
            url: ("TURBOPACK compile-time value", "http://localhost:8080") || 'http://localhost:8080',
            accessible: false,
            response: null
        },
        sampleFile: {
            exists: false,
            accessible: false,
            fileId: null,
            metadata: null
        }
    };
    try {
        // Check uploads directory
        const uploadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'uploads');
        if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(uploadDir)) {
            debugInfo.files.exists = true;
            debugInfo.files.contents = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(uploadDir).filter((f)=>!f.startsWith('.'));
        }
        // Check if we have any files
        const fileManager = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fileUtils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FileManager"]();
        const allFiles = fileManager.getAllFiles();
        if (allFiles.length > 0) {
            const sampleFile = allFiles[0];
            debugInfo.sampleFile.exists = true;
            debugInfo.sampleFile.fileId = sampleFile.id;
            debugInfo.sampleFile.metadata = sampleFile;
            // Check if file is accessible
            const baseUrl = ("TURBOPACK compile-time value", "http://*************:3001") || 'http://localhost:3001';
            const fileUrl = `${baseUrl}/api/files/serve/${sampleFile.id}`;
            try {
                const fileResponse = await fetch(fileUrl, {
                    method: 'HEAD'
                });
                debugInfo.sampleFile.accessible = fileResponse.ok;
            } catch (error) {
                debugInfo.sampleFile.accessible = false;
            }
        }
        // Test OnlyOffice server accessibility
        const onlyofficeUrl = ("TURBOPACK compile-time value", "http://localhost:8080") || 'http://localhost:8080';
        try {
            const response = await fetch(`${onlyofficeUrl}/healthcheck`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });
            debugInfo.onlyofficeServer.accessible = response.ok;
            debugInfo.onlyofficeServer.response = {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries())
            };
        } catch (error) {
            debugInfo.onlyofficeServer.accessible = false;
            debugInfo.onlyofficeServer.response = {
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            debug: debugInfo,
            recommendations: generateRecommendations(debugInfo)
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            debug: debugInfo
        });
    }
}
function generateRecommendations(debugInfo) {
    const recommendations = [];
    if (debugInfo.environment.ONLYOFFICE_SERVER_URL === 'NOT SET') {
        recommendations.push('Set ONLYOFFICE_SERVER_URL environment variable');
    }
    if (debugInfo.environment.NEXT_PUBLIC_APP_URL === 'NOT SET') {
        recommendations.push('Set NEXT_PUBLIC_APP_URL environment variable');
    }
    if (debugInfo.environment.NEXT_PUBLIC_APP_URL.includes('localhost')) {
        recommendations.push('Consider using your machine\'s IP address instead of localhost for Docker integration');
    }
    if (!debugInfo.files.exists) {
        recommendations.push('Create uploads directory: public/uploads');
    }
    if (debugInfo.files.contents.length === 0) {
        recommendations.push('Upload some test files to test the integration');
    }
    if (!debugInfo.onlyofficeServer.accessible) {
        recommendations.push('Start OnlyOffice Docker container: docker run -d --name onlyoffice -p 8080:80 onlyoffice/documentserver');
    }
    if (debugInfo.sampleFile.exists && !debugInfo.sampleFile.accessible) {
        recommendations.push('Check file serving endpoint - files may not be accessible from OnlyOffice server');
    }
    return recommendations;
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__556a9609._.js.map