module.exports = {

"[project]/.next-internal/server/app/api/debug-final-config/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/onlyoffice.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OnlyOfficeService": (()=>OnlyOfficeService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
;
class OnlyOfficeService {
    serverUrl;
    jwtSecret;
    constructor(){
        this.serverUrl = process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080';
        this.jwtSecret = process.env.ONLYOFFICE_JWT_SECRET || 'fzX5rJRaYYPtns6t'; // Use the actual secret from your setup
        // Log configuration for debugging
        console.log('OnlyOffice Service Configuration:', {
            serverUrl: this.serverUrl,
            hasJwtSecret: !!this.jwtSecret,
            jwtEnabled: !!this.jwtSecret && this.jwtSecret !== 'mysecret',
            jwtDisabled: process.env.ONLYOFFICE_JWT_DISABLED === 'true',
            jwtSecret: this.jwtSecret === 'fzX5rJRaYYPtns6t' ? 'Using production secret' : 'Using custom secret'
        });
    }
    getDocumentType(fileExtension) {
        const wordFormats = [
            'doc',
            'docx',
            'docm',
            'dot',
            'dotx',
            'dotm',
            'odt',
            'fodt',
            'ott',
            'rtf',
            'txt',
            'html',
            'htm',
            'mht',
            'pdf',
            'djvu',
            'fb2',
            'epub',
            'xps'
        ];
        const cellFormats = [
            'xls',
            'xlsx',
            'xlsm',
            'xlt',
            'xltx',
            'xltm',
            'ods',
            'fods',
            'ots',
            'csv'
        ];
        const slideFormats = [
            'ppt',
            'pptx',
            'pptm',
            'pot',
            'potx',
            'potm',
            'odp',
            'fodp',
            'otp'
        ];
        if (wordFormats.includes(fileExtension.toLowerCase())) {
            return 'word';
        } else if (cellFormats.includes(fileExtension.toLowerCase())) {
            return 'cell';
        } else if (slideFormats.includes(fileExtension.toLowerCase())) {
            return 'slide';
        }
        return 'word'; // default
    }
    generateDocumentKey(filename, lastModified) {
        const keyData = `${filename}_${lastModified}_${Date.now()}`;
        return Buffer.from(keyData).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);
    }
    createConfig(filename, fileUrl, callbackUrl, userId = 'user1', userName = 'User', mode = 'edit') {
        const fileExtension = filename.split('.').pop() || 'docx'; // Default to docx if no extension
        const documentType = this.getDocumentType(fileExtension);
        const documentKey = this.generateDocumentKey(filename, Date.now());
        const config = {
            documentType,
            document: {
                fileType: fileExtension,
                key: documentKey,
                title: filename,
                url: fileUrl,
                permissions: {
                    comment: true,
                    copy: true,
                    download: true,
                    edit: mode === 'edit',
                    fillForms: true,
                    modifyFilter: true,
                    modifyContentControl: true,
                    review: true,
                    print: true
                }
            },
            editorConfig: {
                mode,
                lang: 'en',
                callbackUrl,
                user: {
                    id: userId,
                    name: userName
                },
                customization: {
                    autosave: true,
                    forcesave: false,
                    comments: true,
                    compactHeader: false,
                    compactToolbar: false,
                    compatibleFeatures: false,
                    customer: {
                        address: '',
                        info: '',
                        logo: '',
                        mail: '',
                        name: 'OnlyOffice Demo',
                        phone: '',
                        www: ''
                    },
                    feedback: {
                        url: '',
                        visible: false
                    },
                    goback: {
                        url: ("TURBOPACK compile-time value", "http://*************:3002") || 'http://localhost:3000',
                        text: 'Back to Documents'
                    },
                    logo: {
                        image: '',
                        imageEmbedded: '',
                        url: ''
                    },
                    reviewDisplay: 'original',
                    showReviewChanges: false,
                    spellcheck: true,
                    toolbarNoTabs: false,
                    unit: 'cm',
                    zoom: 100
                }
            },
            width: '100%',
            height: '100%'
        };
        return config;
    }
    signConfig(config) {
        // Check if JWT is explicitly disabled
        if (process.env.ONLYOFFICE_JWT_DISABLED === 'true') {
            console.log('JWT signing explicitly disabled via ONLYOFFICE_JWT_DISABLED environment variable');
            return null;
        }
        // For the OnlyOffice server at only.34sy.org, we need to provide JWT
        if (!this.jwtSecret) {
            console.log('JWT signing disabled - no secret provided');
            return null;
        }
        try {
            // Create the payload exactly as OnlyOffice expects it
            const payload = {
                document: config.document,
                editorConfig: config.editorConfig,
                documentType: config.documentType,
                width: config.width,
                height: config.height
            };
            console.log('Signing JWT payload with secret:', this.jwtSecret === 'fzX5rJRaYYPtns6t' ? 'production secret' : 'custom secret');
            // OnlyOffice expects the entire config as payload
            const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(payload, this.jwtSecret, {
                algorithm: 'HS256',
                expiresIn: '1h' // Add expiration for security
            });
            console.log('JWT token generated successfully, length:', token.length);
            return token;
        } catch (error) {
            console.error('JWT signing error:', error);
            return null;
        }
    }
    verifyToken(token) {
        try {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, this.jwtSecret);
        } catch (error) {
            throw new Error('Invalid JWT token');
        }
    }
}
}}),
"[project]/src/app/api/debug-final-config/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$onlyoffice$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/onlyoffice.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        // Get current configuration
        const onlyOfficeService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$onlyoffice$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OnlyOfficeService"]();
        // Get request details
        const host = request.headers.get('host') || 'localhost:3001';
        const protocol = request.headers.get('x-forwarded-proto') || 'http';
        const currentUrl = `${protocol}://${host}`;
        // Environment check
        const envConfig = {
            ONLYOFFICE_SERVER_URL: process.env.ONLYOFFICE_SERVER_URL || 'NOT SET',
            ONLYOFFICE_JWT_SECRET: process.env.ONLYOFFICE_JWT_SECRET ? 'SET (length: ' + process.env.ONLYOFFICE_JWT_SECRET.length + ')' : 'NOT SET',
            ONLYOFFICE_JWT_DISABLED: process.env.ONLYOFFICE_JWT_DISABLED || 'NOT SET',
            NEXT_PUBLIC_APP_URL: ("TURBOPACK compile-time value", "http://*************:3002") || 'NOT SET',
            NODE_ENV: ("TURBOPACK compile-time value", "development") || 'NOT SET',
            PORT: process.env.PORT || 'NOT SET'
        };
        // Port analysis
        const detectedPort = host.split(':')[1] || '3000';
        const expectedPort = '3002';
        const portMismatch = detectedPort !== expectedPort;
        // URL analysis
        const baseUrl = ("TURBOPACK compile-time value", "http://*************:3002") || currentUrl;
        const expectedFileUrl = `${baseUrl}/api/files/serve/test-file-id`;
        const expectedCallbackUrl = `${baseUrl}/api/onlyoffice/callback`;
        // JWT analysis
        const hasJwtSecret = !!process.env.ONLYOFFICE_JWT_SECRET;
        const jwtSecretCorrect = process.env.ONLYOFFICE_JWT_SECRET === 'fzX5rJRaYYPtns6t';
        const jwtDisabled = process.env.ONLYOFFICE_JWT_DISABLED === 'true';
        // Create test config to see actual behavior
        let testConfig = null;
        let jwtToken = null;
        try {
            testConfig = onlyOfficeService.createConfig('test.docx', expectedFileUrl, expectedCallbackUrl, 'debug-user', 'Debug User', 'edit');
            jwtToken = onlyOfficeService.signConfig(testConfig);
        } catch (error) {
            console.error('Error creating test config:', error);
        }
        // Issues detection
        const issues = [];
        const fixes = [];
        if (!hasJwtSecret) {
            issues.push('JWT secret not set');
            fixes.push('Set ONLYOFFICE_JWT_SECRET=fzX5rJRaYYPtns6t in .env.local');
        } else if (!jwtSecretCorrect) {
            issues.push('JWT secret incorrect');
            fixes.push('Update ONLYOFFICE_JWT_SECRET to fzX5rJRaYYPtns6t');
        }
        if (portMismatch) {
            issues.push(`Port mismatch: detected ${detectedPort}, expected ${expectedPort}`);
            fixes.push(`Update NEXT_PUBLIC_APP_URL to http://*************:${detectedPort}`);
        }
        if (!jwtToken && !jwtDisabled) {
            issues.push('JWT token not generated despite having secret');
            fixes.push('Check JWT secret configuration and ensure it matches OnlyOffice server');
        }
        if (baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1')) {
            issues.push('Using localhost URL - OnlyOffice server cannot access');
            fixes.push('Use ngrok or update NEXT_PUBLIC_APP_URL to machine IP address');
        }
        const debugInfo = {
            timestamp: new Date().toISOString(),
            request: {
                currentUrl,
                host,
                protocol,
                detectedPort
            },
            environment: envConfig,
            ports: {
                detected: detectedPort,
                expected: expectedPort,
                mismatch: portMismatch
            },
            urls: {
                base: baseUrl,
                fileServing: expectedFileUrl,
                callback: expectedCallbackUrl
            },
            jwt: {
                hasSecret: hasJwtSecret,
                secretCorrect: jwtSecretCorrect,
                disabled: jwtDisabled,
                tokenGenerated: !!jwtToken,
                tokenLength: jwtToken ? jwtToken.length : 0
            },
            testConfig: testConfig ? {
                documentType: testConfig.documentType,
                documentKey: testConfig.document.key,
                fileUrl: testConfig.document.url,
                callbackUrl: testConfig.editorConfig.callbackUrl
            } : null,
            issues,
            fixes,
            status: issues.length === 0 ? 'READY' : 'NEEDS_FIXES'
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            debug: debugInfo,
            summary: {
                totalIssues: issues.length,
                status: issues.length === 0 ? 'Configuration is correct' : 'Configuration needs fixes',
                criticalIssues: issues.filter((issue)=>issue.includes('JWT') || issue.includes('port') || issue.includes('localhost')).length
            }
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            debug: {
                timestamp: new Date().toISOString(),
                status: 'ERROR'
            }
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__5be7fa61._.js.map