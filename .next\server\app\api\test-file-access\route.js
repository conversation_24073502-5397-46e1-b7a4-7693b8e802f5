(()=>{var e={};e.id=872,e.ids=[872],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2258:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>d,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var r={};t.r(r),t.d(r,{GET:()=>c});var a=t(6559),i=t(8088),n=t(7719),o=t(2190);async function c(e){try{let e="http://172.29.240.1:3001/api/files/serve/9231eaa4-be78-47eb-9e7b-7c786e7d9f48";console.log("Testing file accessibility from OnlyOffice server perspective..."),console.log("File URL:",e);try{let s=await fetch(e,{method:"HEAD",headers:{"User-Agent":"OnlyOffice-Test"}});return o.NextResponse.json({success:!0,fileUrl:e,status:s.status,statusText:s.statusText,headers:Object.fromEntries(s.headers.entries()),accessible:s.ok,message:s.ok?"File is accessible - OnlyOffice should be able to download it":"File is not accessible - This is why OnlyOffice cannot download it"})}catch(s){return o.NextResponse.json({success:!1,fileUrl:e,error:s instanceof Error?s.message:"Unknown error",accessible:!1,message:"File is not accessible - This is why OnlyOffice cannot download it"})}}catch(e){return o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/test-file-access/route",pathname:"/api/test-file-access",filename:"route",bundlePath:"app/api/test-file-access/route"},resolvedPagePath:"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\test-file-access\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:d}=l;function f(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,580],()=>t(2258));module.exports=r})();