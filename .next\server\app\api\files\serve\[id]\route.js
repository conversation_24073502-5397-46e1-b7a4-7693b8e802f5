(()=>{var e={};e.id=955,e.ids=[955],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1947:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>A,routeModule:()=>y,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var n={};s.r(n),s.d(n,{GET:()=>x,HEAD:()=>f,OPTIONS:()=>h});var r=s(6559),o=s(8088),i=s(7719),a=s(2190),l=s(8556),u=s(9021),c=s.n(u),d=s(3873),p=s.n(d);async function x(e,{params:t}){try{let{id:e}=await t,s=new l.a().getFileMetadata(e);if(!s)return a.NextResponse.json({error:"File not found"},{status:404});let n=p().join(process.cwd(),"public","uploads"),r=[p().join(n,`${e}${p().extname(s.name)}`),p().join(n,`${e}.${s.extension}`),p().join(n,e),p().join(n,`${e}.xlsx`),p().join(n,`${e}.docx`),p().join(n,`${e}.pptx`)],o="";for(let e of r)if(c().existsSync(e)){o=e;break}if(!o||!c().existsSync(o))return console.error("File not found. Tried paths:",r),a.NextResponse.json({error:"File not found on disk"},{status:404});let i=c().readFileSync(o),u=new Headers;return u.set("Content-Type",s.type||"application/octet-stream"),u.set("Content-Length",i.length.toString()),u.set("Content-Disposition",`inline; filename="${s.name}"`),u.set("Access-Control-Allow-Origin","*"),u.set("Access-Control-Allow-Methods","GET, HEAD, OPTIONS"),u.set("Access-Control-Allow-Headers","Content-Type, Authorization, X-Requested-With"),u.set("Access-Control-Allow-Credentials","false"),u.set("Cache-Control","public, max-age=3600"),u.set("ETag",`"${e}-${s.lastModified}"`),u.set("Accept-Ranges","bytes"),u.set("X-Content-Type-Options","nosniff"),new a.NextResponse(i,{status:200,headers:u})}catch(e){return console.error("File serve error:",e),a.NextResponse.json({error:"Failed to serve file"},{status:500})}}async function f(e,{params:t}){try{let{id:e}=await t,s=new l.a().getFileMetadata(e);if(!s)return new a.NextResponse(null,{status:404});let n=p().join(process.cwd(),"public","uploads"),r=[p().join(n,`${e}${p().extname(s.name)}`),p().join(n,`${e}.${s.extension}`),p().join(n,e),p().join(n,`${e}.xlsx`),p().join(n,`${e}.docx`),p().join(n,`${e}.pptx`)],o="";for(let e of r)if(c().existsSync(e)){o=e;break}if(!o||!c().existsSync(o))return new a.NextResponse(null,{status:404});let i=c().statSync(o),u=new Headers;return u.set("Content-Type",s.type||"application/octet-stream"),u.set("Content-Length",i.size.toString()),u.set("Content-Disposition",`inline; filename="${s.name}"`),u.set("Access-Control-Allow-Origin","*"),u.set("Access-Control-Allow-Methods","GET, HEAD, OPTIONS"),u.set("Access-Control-Allow-Headers","Content-Type, Authorization, X-Requested-With"),u.set("Access-Control-Allow-Credentials","false"),u.set("Cache-Control","public, max-age=3600"),u.set("ETag",`"${e}-${s.lastModified}"`),u.set("Accept-Ranges","bytes"),u.set("X-Content-Type-Options","nosniff"),new a.NextResponse(null,{status:200,headers:u})}catch(e){return console.error("File head error:",e),new a.NextResponse(null,{status:500})}}async function h(){let e=new Headers;return e.set("Access-Control-Allow-Origin","*"),e.set("Access-Control-Allow-Methods","GET, HEAD, OPTIONS"),e.set("Access-Control-Allow-Headers","Content-Type, Authorization, X-Requested-With"),e.set("Access-Control-Allow-Credentials","false"),e.set("Access-Control-Max-Age","86400"),new a.NextResponse(null,{status:200,headers:e})}let y=new r.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/files/serve/[id]/route",pathname:"/api/files/serve/[id]",filename:"route",bundlePath:"app/api/files/serve/[id]/route"},resolvedPagePath:"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\files\\serve\\[id]\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:g}=y;function A(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3870:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var n=s(5511);let r={randomUUID:n.randomUUID},o=new Uint8Array(256),i=o.length,a=[];for(let e=0;e<256;++e)a.push((e+256).toString(16).slice(1));let l=function(e,t,s){if(r.randomUUID&&!t&&!e)return r.randomUUID();let l=(e=e||{}).random??e.rng?.()??(i>o.length-16&&((0,n.randomFillSync)(o),i=0),o.slice(i,i+=16));if(l.length<16)throw Error("Random bytes length must be >= 16");if(l[6]=15&l[6]|64,l[8]=63&l[8]|128,t){if((s=s||0)<0||s+16>t.length)throw RangeError(`UUID byte range ${s}:${s+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[s+e]=l[e];return t}return function(e,t=0){return(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase()}(l)}},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},8335:()=>{},8556:(e,t,s)=>{"use strict";s.d(t,{a:()=>l});var n=s(9021),r=s.n(n),o=s(3873),i=s.n(o),a=s(3870);class l{constructor(){this.uploadDir=i().join(process.cwd(),"public","uploads"),this.ensureUploadDir()}ensureUploadDir(){r().existsSync(this.uploadDir)||r().mkdirSync(this.uploadDir,{recursive:!0})}async saveFile(e){let t=(0,a.A)(),s=i().extname(e.name),n=`${t}${s}`,o=i().join(this.uploadDir,n),l=await e.arrayBuffer(),u=Buffer.from(l);r().writeFileSync(o,u);let c={id:t,name:e.name||`document${s}`,size:e.size,type:e.type,extension:s.slice(1)||"unknown",uploadDate:new Date,lastModified:new Date,url:`/uploads/${n}`};return this.saveFileMetadata(c),c}saveFileMetadata(e){let t=i().join(this.uploadDir,`${e.id}.json`);r().writeFileSync(t,JSON.stringify(e,null,2))}getFileMetadata(e){try{let t=i().join(this.uploadDir,`${e}.json`);if(!r().existsSync(t))return null;let s=r().readFileSync(t,"utf-8");return JSON.parse(s)}catch(e){return null}}getAllFiles(){try{return r().readdirSync(this.uploadDir).filter(e=>e.endsWith(".json")).map(e=>{let t=r().readFileSync(i().join(this.uploadDir,e),"utf-8");return JSON.parse(t)}).sort((e,t)=>new Date(t.uploadDate).getTime()-new Date(e.uploadDate).getTime())}catch(e){return[]}}deleteFile(e){try{let t=this.getFileMetadata(e);if(!t)return!1;let s=i().extname(t.name),n=`${e}${s}`,o=i().join(this.uploadDir,n),a=i().join(this.uploadDir,`${e}.json`);return r().existsSync(o)&&r().unlinkSync(o),r().existsSync(a)&&r().unlinkSync(a),!0}catch(e){return!1}}getFileUrl(e){let t=this.getFileMetadata(e);return t?`http://172.29.240.1:3001${t.url}`:null}isValidFileType(e){return["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt","xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv","ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp","pdf"].includes(i().extname(e).slice(1).toLowerCase())}}},9021:e=>{"use strict";e.exports=require("fs")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[447,580],()=>s(1947));module.exports=n})();