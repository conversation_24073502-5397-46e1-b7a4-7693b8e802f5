import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const onlyOfficeUrl = process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080';
    
    console.log('Testing local OnlyOffice server accessibility...');
    console.log('OnlyOffice URL:', onlyOfficeUrl);

    // Test if the OnlyOffice server is accessible
    try {
      const response = await fetch(`${onlyOfficeUrl}/web-apps/apps/api/documents/api.js`, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'OnlyOffice-Test'
        }
      });

      console.log('OnlyOffice server response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      return NextResponse.json({
        success: true,
        onlyOfficeUrl,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        accessible: response.ok,
        message: response.ok 
          ? 'Local OnlyOffice server is accessible!'
          : 'Local OnlyOffice server is not accessible'
      });
    } catch (error) {
      console.error('OnlyOffice server test error:', error);
      return NextResponse.json({
        success: false,
        onlyOfficeUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
        accessible: false,
        message: 'Cannot connect to local OnlyOffice server'
      });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
