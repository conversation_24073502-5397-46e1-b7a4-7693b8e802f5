(()=>{var e={};e.id=63,e.ids=[63],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3870:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(5511);let n={randomUUID:s.randomUUID},i=new Uint8Array(256),o=i.length,a=[];for(let e=0;e<256;++e)a.push((e+256).toString(16).slice(1));let l=function(e,t,r){if(n.randomUUID&&!t&&!e)return n.randomUUID();let l=(e=e||{}).random??e.rng?.()??(o>i.length-16&&((0,s.randomFillSync)(i),o=0),i.slice(o,o+=16));if(l.length<16)throw Error("Random bytes length must be >= 16");if(l[6]=15&l[6]|64,l[8]=63&l[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=l[e];return t}return function(e,t=0){return(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase()}(l)}},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7065:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>u,GET:()=>d});var n=r(6559),i=r(8088),o=r(7719),a=r(2190),l=r(8556);async function u(e,{params:t}){try{let{id:e}=await t;if(!new l.a().deleteFile(e))return a.NextResponse.json({error:"File not found"},{status:404});return a.NextResponse.json({success:!0,message:"File deleted successfully"})}catch(e){return console.error("Delete file error:",e),a.NextResponse.json({error:"Failed to delete file"},{status:500})}}async function d(e,{params:t}){try{let{id:e}=await t,r=new l.a().getFileMetadata(e);if(!r)return a.NextResponse.json({error:"File not found"},{status:404});return a.NextResponse.json({success:!0,file:r})}catch(e){return console.error("Get file error:",e),a.NextResponse.json({error:"Failed to get file"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/files/[id]/route",pathname:"/api/files/[id]",filename:"route",bundlePath:"app/api/files/[id]/route"},resolvedPagePath:"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\files\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:f,serverHooks:x}=p;function h(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:f})}},8335:()=>{},8556:(e,t,r)=>{"use strict";r.d(t,{a:()=>l});var s=r(9021),n=r.n(s),i=r(3873),o=r.n(i),a=r(3870);class l{constructor(){this.uploadDir=o().join(process.cwd(),"public","uploads"),this.ensureUploadDir()}ensureUploadDir(){n().existsSync(this.uploadDir)||n().mkdirSync(this.uploadDir,{recursive:!0})}async saveFile(e){let t=(0,a.A)(),r=o().extname(e.name),s=`${t}${r}`,i=o().join(this.uploadDir,s),l=await e.arrayBuffer(),u=Buffer.from(l);n().writeFileSync(i,u);let d={id:t,name:e.name||`document${r}`,size:e.size,type:e.type,extension:r.slice(1)||"unknown",uploadDate:new Date,lastModified:new Date,url:`/uploads/${s}`};return this.saveFileMetadata(d),d}saveFileMetadata(e){let t=o().join(this.uploadDir,`${e.id}.json`);n().writeFileSync(t,JSON.stringify(e,null,2))}getFileMetadata(e){try{let t=o().join(this.uploadDir,`${e}.json`);if(!n().existsSync(t))return null;let r=n().readFileSync(t,"utf-8");return JSON.parse(r)}catch(e){return null}}getAllFiles(){try{return n().readdirSync(this.uploadDir).filter(e=>e.endsWith(".json")).map(e=>{let t=n().readFileSync(o().join(this.uploadDir,e),"utf-8");return JSON.parse(t)}).sort((e,t)=>new Date(t.uploadDate).getTime()-new Date(e.uploadDate).getTime())}catch(e){return[]}}deleteFile(e){try{let t=this.getFileMetadata(e);if(!t)return!1;let r=o().extname(t.name),s=`${e}${r}`,i=o().join(this.uploadDir,s),a=o().join(this.uploadDir,`${e}.json`);return n().existsSync(i)&&n().unlinkSync(i),n().existsSync(a)&&n().unlinkSync(a),!0}catch(e){return!1}}getFileUrl(e){let t=this.getFileMetadata(e);return t?`http://172.29.240.1:3001${t.url}`:null}isValidFileType(e){return["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt","xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv","ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp","pdf"].includes(o().extname(e).slice(1).toLowerCase())}}},9021:e=>{"use strict";e.exports=require("fs")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(7065));module.exports=s})();