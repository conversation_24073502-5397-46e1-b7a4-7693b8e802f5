'use client';

import React, { useEffect, useState } from 'react';

export default function DebugPage() {
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [docsAPIAvailable, setDocsAPIAvailable] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadScript = async () => {
      try {
        // Check if script is already loaded
        if ((window as any).DocsAPI) {
          setDocsAPIAvailable(true);
          setScriptLoaded(true);
          return;
        }

        // Try multiple OnlyOffice server URLs
        const serverUrls = [
          'https://only.34sy.org/web-apps/apps/api/documents/api.js',
          'https://documentserver.onlyoffice.com/web-apps/apps/api/documents/api.js',
          'https://api.onlyoffice.com/editors/api.js'
        ];

        let currentUrlIndex = 0;

        const tryLoadScript = () => {
          if (currentUrlIndex >= serverUrls.length) {
            // Try loading the mock API as fallback
            console.log('Trying OnlyOffice mock API as fallback...');
            const mockScript = document.createElement('script');
            mockScript.src = '/onlyoffice-api-mock.js';
            mockScript.async = true;

            mockScript.onload = () => {
              console.log('OnlyOffice Mock API loaded successfully');
              setScriptLoaded(true);

              if ((window as any).DocsAPI) {
                setDocsAPIAvailable(true);
                console.log('DocsAPI is available (Mock):', (window as any).DocsAPI);
              } else {
                setError('DocsAPI not available even with mock');
              }
            };

            mockScript.onerror = () => {
              setError('Failed to load OnlyOffice API from all available servers and mock');
            };

            document.head.appendChild(mockScript);
            return;
          }

          const script = document.createElement('script');
          script.src = serverUrls[currentUrlIndex];
          script.async = true;
          script.crossOrigin = 'anonymous';

          script.onload = () => {
            console.log(`OnlyOffice script loaded from: ${serverUrls[currentUrlIndex]}`);
            setScriptLoaded(true);

            // Check if DocsAPI is available
            if ((window as any).DocsAPI) {
              setDocsAPIAvailable(true);
              console.log('DocsAPI is available:', (window as any).DocsAPI);
            } else {
              console.warn(`DocsAPI not available from: ${serverUrls[currentUrlIndex]}`);
              currentUrlIndex++;
              tryLoadScript();
            }
          };

          script.onerror = (err) => {
            console.error(`Failed to load OnlyOffice script from: ${serverUrls[currentUrlIndex]}`, err);
            currentUrlIndex++;
            tryLoadScript();
          };

          console.log(`Trying to load OnlyOffice script from: ${serverUrls[currentUrlIndex]}`);
          document.head.appendChild(script);
        };

        tryLoadScript();
      } catch (err) {
        setError(`Error: ${err}`);
      }
    };

    loadScript();
  }, []);

  const testFileServing = async () => {
    try {
      const response = await fetch('/api/files/serve/79e10043-01ae-449b-930a-c81125abde3b', {
        method: 'HEAD'
      });
      console.log('File serving test:', response.status, response.headers);
    } catch (err) {
      console.error('File serving test failed:', err);
    }
  };

  const testConfig = async () => {
    try {
      const response = await fetch('/api/onlyoffice/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileId: '79e10043-01ae-449b-930a-c81125abde3b',
          mode: 'edit',
          userId: 'debug-user',
          userName: 'Debug User'
        })
      });
      const result = await response.json();
      console.log('Config test:', result);
    } catch (err) {
      console.error('Config test failed:', err);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">OnlyOffice Debug Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Script Loading Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Script Loading Status</h2>
            <div className="space-y-2">
              <div className={`flex items-center ${scriptLoaded ? 'text-green-600' : 'text-red-600'}`}>
                <span className="w-3 h-3 rounded-full mr-2" style={{backgroundColor: scriptLoaded ? 'green' : 'red'}}></span>
                Script Loaded: {scriptLoaded ? 'Yes' : 'No'}
              </div>
              <div className={`flex items-center ${docsAPIAvailable ? 'text-green-600' : 'text-red-600'}`}>
                <span className="w-3 h-3 rounded-full mr-2" style={{backgroundColor: docsAPIAvailable ? 'green' : 'red'}}></span>
                DocsAPI Available: {docsAPIAvailable ? 'Yes' : 'No'}
              </div>
              {error && (
                <div className="text-red-600 mt-2">
                  Error: {error}
                </div>
              )}
            </div>
          </div>

          {/* Test Buttons */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Test Functions</h2>
            <div className="space-y-3">
              <button
                onClick={testFileServing}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Test File Serving
              </button>
              <button
                onClick={testConfig}
                className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Test Config Generation
              </button>
              <button
                onClick={() => window.open('/api/files/serve/79e10043-01ae-449b-930a-c81125abde3b', '_blank')}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                Open File Direct
              </button>
            </div>
          </div>

          {/* Environment Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Environment Info</h2>
            <div className="space-y-2 text-sm">
              <div>User Agent: {typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A'}</div>
              <div>Current URL: {typeof window !== 'undefined' ? window.location.href : 'N/A'}</div>
              <div>Protocol: {typeof window !== 'undefined' ? window.location.protocol : 'N/A'}</div>
            </div>
          </div>

          {/* Console Output */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Instructions</h2>
            <div className="text-sm text-gray-600">
              <p>1. Check the browser console for detailed logs</p>
              <p>2. Test file serving to ensure CORS is working</p>
              <p>3. Test config generation to verify API</p>
              <p>4. Check if OnlyOffice script loads from only.34sy.org</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
