{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/debug/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\n\nexport default function DebugPage() {\n  const [scriptLoaded, setScriptLoaded] = useState(false);\n  const [docsAPIAvailable, setDocsAPIAvailable] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const loadScript = async () => {\n      try {\n        // Check if script is already loaded\n        if ((window as any).DocsAPI) {\n          setDocsAPIAvailable(true);\n          setScriptLoaded(true);\n          return;\n        }\n\n        // Try multiple OnlyOffice server URLs\n        const serverUrls = [\n          'https://only.34sy.org/web-apps/apps/api/documents/api.js',\n          'https://documentserver.onlyoffice.com/web-apps/apps/api/documents/api.js',\n          'https://api.onlyoffice.com/editors/api.js'\n        ];\n\n        let currentUrlIndex = 0;\n\n        const tryLoadScript = () => {\n          if (currentUrlIndex >= serverUrls.length) {\n            // Try loading the mock API as fallback\n            console.log('Trying OnlyOffice mock API as fallback...');\n            const mockScript = document.createElement('script');\n            mockScript.src = '/onlyoffice-api-mock.js';\n            mockScript.async = true;\n\n            mockScript.onload = () => {\n              console.log('OnlyOffice Mock API loaded successfully');\n              setScriptLoaded(true);\n\n              if ((window as any).DocsAPI) {\n                setDocsAPIAvailable(true);\n                console.log('DocsAPI is available (Mock):', (window as any).DocsAPI);\n              } else {\n                setError('DocsAPI not available even with mock');\n              }\n            };\n\n            mockScript.onerror = () => {\n              setError('Failed to load OnlyOffice API from all available servers and mock');\n            };\n\n            document.head.appendChild(mockScript);\n            return;\n          }\n\n          const script = document.createElement('script');\n          script.src = serverUrls[currentUrlIndex];\n          script.async = true;\n          script.crossOrigin = 'anonymous';\n\n          script.onload = () => {\n            console.log(`OnlyOffice script loaded from: ${serverUrls[currentUrlIndex]}`);\n            setScriptLoaded(true);\n\n            // Check if DocsAPI is available\n            if ((window as any).DocsAPI) {\n              setDocsAPIAvailable(true);\n              console.log('DocsAPI is available:', (window as any).DocsAPI);\n            } else {\n              console.warn(`DocsAPI not available from: ${serverUrls[currentUrlIndex]}`);\n              currentUrlIndex++;\n              tryLoadScript();\n            }\n          };\n\n          script.onerror = (err) => {\n            console.error(`Failed to load OnlyOffice script from: ${serverUrls[currentUrlIndex]}`, err);\n            currentUrlIndex++;\n            tryLoadScript();\n          };\n\n          console.log(`Trying to load OnlyOffice script from: ${serverUrls[currentUrlIndex]}`);\n          document.head.appendChild(script);\n        };\n\n        tryLoadScript();\n      } catch (err) {\n        setError(`Error: ${err}`);\n      }\n    };\n\n    loadScript();\n  }, []);\n\n  const testFileServing = async () => {\n    try {\n      const response = await fetch('/api/files/serve/79e10043-01ae-449b-930a-c81125abde3b', {\n        method: 'HEAD'\n      });\n      console.log('File serving test:', response.status, response.headers);\n    } catch (err) {\n      console.error('File serving test failed:', err);\n    }\n  };\n\n  const testConfig = async () => {\n    try {\n      const response = await fetch('/api/onlyoffice/config', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          fileId: '79e10043-01ae-449b-930a-c81125abde3b',\n          mode: 'edit',\n          userId: 'debug-user',\n          userName: 'Debug User'\n        })\n      });\n      const result = await response.json();\n      console.log('Config test:', result);\n    } catch (err) {\n      console.error('Config test failed:', err);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">OnlyOffice Debug Page</h1>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Script Loading Status */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Script Loading Status</h2>\n            <div className=\"space-y-2\">\n              <div className={`flex items-center ${scriptLoaded ? 'text-green-600' : 'text-red-600'}`}>\n                <span className=\"w-3 h-3 rounded-full mr-2\" style={{backgroundColor: scriptLoaded ? 'green' : 'red'}}></span>\n                Script Loaded: {scriptLoaded ? 'Yes' : 'No'}\n              </div>\n              <div className={`flex items-center ${docsAPIAvailable ? 'text-green-600' : 'text-red-600'}`}>\n                <span className=\"w-3 h-3 rounded-full mr-2\" style={{backgroundColor: docsAPIAvailable ? 'green' : 'red'}}></span>\n                DocsAPI Available: {docsAPIAvailable ? 'Yes' : 'No'}\n              </div>\n              {error && (\n                <div className=\"text-red-600 mt-2\">\n                  Error: {error}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Test Buttons */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Test Functions</h2>\n            <div className=\"space-y-3\">\n              <button\n                onClick={testFileServing}\n                className=\"w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                Test File Serving\n              </button>\n              <button\n                onClick={testConfig}\n                className=\"w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\"\n              >\n                Test Config Generation\n              </button>\n              <button\n                onClick={() => window.open('/api/files/serve/79e10043-01ae-449b-930a-c81125abde3b', '_blank')}\n                className=\"w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700\"\n              >\n                Open File Direct\n              </button>\n            </div>\n          </div>\n\n          {/* Environment Info */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Environment Info</h2>\n            <div className=\"space-y-2 text-sm\">\n              <div>User Agent: {typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A'}</div>\n              <div>Current URL: {typeof window !== 'undefined' ? window.location.href : 'N/A'}</div>\n              <div>Protocol: {typeof window !== 'undefined' ? window.location.protocol : 'N/A'}</div>\n            </div>\n          </div>\n\n          {/* Console Output */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Instructions</h2>\n            <div className=\"text-sm text-gray-600\">\n              <p>1. Check the browser console for detailed logs</p>\n              <p>2. Test file serving to ensure CORS is working</p>\n              <p>3. Test config generation to verify API</p>\n              <p>4. Check if OnlyOffice script loads from only.34sy.org</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,oCAAoC;gBACpC,IAAI,AAAC,OAAe,OAAO,EAAE;oBAC3B,oBAAoB;oBACpB,gBAAgB;oBAChB;gBACF;gBAEA,sCAAsC;gBACtC,MAAM,aAAa;oBACjB;oBACA;oBACA;iBACD;gBAED,IAAI,kBAAkB;gBAEtB,MAAM,gBAAgB;oBACpB,IAAI,mBAAmB,WAAW,MAAM,EAAE;wBACxC,uCAAuC;wBACvC,QAAQ,GAAG,CAAC;wBACZ,MAAM,aAAa,SAAS,aAAa,CAAC;wBAC1C,WAAW,GAAG,GAAG;wBACjB,WAAW,KAAK,GAAG;wBAEnB,WAAW,MAAM,GAAG;4BAClB,QAAQ,GAAG,CAAC;4BACZ,gBAAgB;4BAEhB,IAAI,AAAC,OAAe,OAAO,EAAE;gCAC3B,oBAAoB;gCACpB,QAAQ,GAAG,CAAC,gCAAgC,AAAC,OAAe,OAAO;4BACrE,OAAO;gCACL,SAAS;4BACX;wBACF;wBAEA,WAAW,OAAO,GAAG;4BACnB,SAAS;wBACX;wBAEA,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B;oBACF;oBAEA,MAAM,SAAS,SAAS,aAAa,CAAC;oBACtC,OAAO,GAAG,GAAG,UAAU,CAAC,gBAAgB;oBACxC,OAAO,KAAK,GAAG;oBACf,OAAO,WAAW,GAAG;oBAErB,OAAO,MAAM,GAAG;wBACd,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,UAAU,CAAC,gBAAgB,EAAE;wBAC3E,gBAAgB;wBAEhB,gCAAgC;wBAChC,IAAI,AAAC,OAAe,OAAO,EAAE;4BAC3B,oBAAoB;4BACpB,QAAQ,GAAG,CAAC,yBAAyB,AAAC,OAAe,OAAO;wBAC9D,OAAO;4BACL,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,UAAU,CAAC,gBAAgB,EAAE;4BACzE;4BACA;wBACF;oBACF;oBAEA,OAAO,OAAO,GAAG,CAAC;wBAChB,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,UAAU,CAAC,gBAAgB,EAAE,EAAE;wBACvF;wBACA;oBACF;oBAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,UAAU,CAAC,gBAAgB,EAAE;oBACnF,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;gBAEA;YACF,EAAE,OAAO,KAAK;gBACZ,SAAS,CAAC,OAAO,EAAE,KAAK;YAC1B;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yDAAyD;gBACpF,QAAQ;YACV;YACA,QAAQ,GAAG,CAAC,sBAAsB,SAAS,MAAM,EAAE,SAAS,OAAO;QACrE,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,UAAU;gBACZ;YACF;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,gBAAgB;QAC9B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAEtD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,kBAAkB,EAAE,eAAe,mBAAmB,gBAAgB;;8DACrF,8OAAC;oDAAK,WAAU;oDAA4B,OAAO;wDAAC,iBAAiB,eAAe,UAAU;oDAAK;;;;;;gDAAU;gDAC7F,eAAe,QAAQ;;;;;;;sDAEzC,8OAAC;4CAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,mBAAmB,gBAAgB;;8DACzF,8OAAC;oDAAK,WAAU;oDAA4B,OAAO;wDAAC,iBAAiB,mBAAmB,UAAU;oDAAK;;;;;;gDAAU;gDAC7F,mBAAmB,QAAQ;;;;;;;wCAEhD,uBACC,8OAAC;4CAAI,WAAU;;gDAAoB;gDACzB;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC,yDAAyD;4CACpF,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAI;gDAAa,OAAO,cAAc,cAAc,UAAU,SAAS,GAAG;;;;;;;sDAC3E,8OAAC;;gDAAI;gDAAc,6EAAuD;;;;;;;sDAC1E,8OAAC;;gDAAI;gDAAW,6EAA2D;;;;;;;;;;;;;;;;;;;sCAK/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}