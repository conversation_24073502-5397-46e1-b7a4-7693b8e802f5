{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/lib/onlyoffice.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\n\nexport interface OnlyOfficeConfig {\n  documentType: 'word' | 'cell' | 'slide';\n  document: {\n    fileType: string;\n    key: string;\n    title: string;\n    url: string;\n    permissions: {\n      comment: boolean;\n      copy: boolean;\n      download: boolean;\n      edit: boolean;\n      fillForms: boolean;\n      modifyFilter: boolean;\n      modifyContentControl: boolean;\n      review: boolean;\n      print: boolean;\n    };\n  };\n  editorConfig: {\n    mode: 'edit' | 'view';\n    lang: string;\n    callbackUrl: string;\n    user: {\n      id: string;\n      name: string;\n    };\n    customization: {\n      autosave: boolean;\n      forcesave: boolean;\n      comments: boolean;\n      compactHeader: boolean;\n      compactToolbar: boolean;\n      compatibleFeatures: boolean;\n      customer: {\n        address: string;\n        info: string;\n        logo: string;\n        mail: string;\n        name: string;\n        phone: string;\n        www: string;\n      };\n      feedback: {\n        url: string;\n        visible: boolean;\n      };\n      goback: {\n        url: string;\n        text: string;\n      };\n      logo: {\n        image: string;\n        imageEmbedded: string;\n        url: string;\n      };\n      reviewDisplay: string;\n      showReviewChanges: boolean;\n      spellcheck: boolean;\n      toolbarNoTabs: boolean;\n      unit: string;\n      zoom: number;\n    };\n  };\n  width: string;\n  height: string;\n  token?: string; // JWT token for authentication\n}\n\nexport class OnlyOfficeService {\n  private serverUrl: string;\n  private jwtSecret: string;\n\n  constructor() {\n    this.serverUrl = process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080';\n    this.jwtSecret = process.env.ONLYOFFICE_JWT_SECRET || 'fzX5rJRaYYPtns6t'; // Use the actual secret from your setup\n    \n    // Log configuration for debugging\n    console.log('OnlyOffice Service Configuration:', {\n      serverUrl: this.serverUrl,\n      hasJwtSecret: !!this.jwtSecret,\n      jwtEnabled: !!this.jwtSecret && this.jwtSecret !== 'mysecret',\n      jwtDisabled: process.env.ONLYOFFICE_JWT_DISABLED === 'true',\n      jwtSecret: this.jwtSecret === 'fzX5rJRaYYPtns6t' ? 'Using production secret' : 'Using custom secret'\n    });\n  }\n\n  getDocumentType(fileExtension: string): 'word' | 'cell' | 'slide' {\n    const wordFormats = ['doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt', 'html', 'htm', 'mht', 'pdf', 'djvu', 'fb2', 'epub', 'xps'];\n    const cellFormats = ['xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv'];\n    const slideFormats = ['ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp'];\n\n    if (wordFormats.includes(fileExtension.toLowerCase())) {\n      return 'word';\n    } else if (cellFormats.includes(fileExtension.toLowerCase())) {\n      return 'cell';\n    } else if (slideFormats.includes(fileExtension.toLowerCase())) {\n      return 'slide';\n    }\n    \n    return 'word'; // default\n  }\n\n  generateDocumentKey(filename: string, lastModified: number): string {\n    const keyData = `${filename}_${lastModified}_${Date.now()}`;\n    return Buffer.from(keyData).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);\n  }\n\n  createConfig(\n    filename: string,\n    fileUrl: string,\n    callbackUrl: string,\n    userId: string = 'user1',\n    userName: string = 'User',\n    mode: 'edit' | 'view' = 'edit'\n  ): OnlyOfficeConfig {\n    const fileExtension = filename.split('.').pop() || 'docx'; // Default to docx if no extension\n    const documentType = this.getDocumentType(fileExtension);\n    const documentKey = this.generateDocumentKey(filename, Date.now());\n\n    const config: OnlyOfficeConfig = {\n      documentType,\n      document: {\n        fileType: fileExtension,\n        key: documentKey,\n        title: filename,\n        url: fileUrl,\n        permissions: {\n          comment: true,\n          copy: true,\n          download: true,\n          edit: mode === 'edit',\n          fillForms: true,\n          modifyFilter: true,\n          modifyContentControl: true,\n          review: true,\n          print: true,\n        },\n      },\n      editorConfig: {\n        mode,\n        lang: 'en',\n        callbackUrl,\n        user: {\n          id: userId,\n          name: userName,\n        },\n        customization: {\n          autosave: true,\n          forcesave: false,\n          comments: true,\n          compactHeader: false,\n          compactToolbar: false,\n          compatibleFeatures: false,\n          customer: {\n            address: '',\n            info: '',\n            logo: '',\n            mail: '',\n            name: 'OnlyOffice Demo',\n            phone: '',\n            www: '',\n          },\n          feedback: {\n            url: '',\n            visible: false,\n          },\n          goback: {\n            url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n            text: 'Back to Documents',\n          },\n          logo: {\n            image: '',\n            imageEmbedded: '',\n            url: '',\n          },\n          reviewDisplay: 'original',\n          showReviewChanges: false,\n          spellcheck: true,\n          toolbarNoTabs: false,\n          unit: 'cm',\n          zoom: 100,\n        },\n      },\n      width: '100%',\n      height: '100%',\n    };\n\n    return config;\n  }\n\n  signConfig(config: OnlyOfficeConfig): string | null {\n    // Check if JWT is explicitly disabled\n    if (process.env.ONLYOFFICE_JWT_DISABLED === 'true') {\n      console.log('JWT signing explicitly disabled via ONLYOFFICE_JWT_DISABLED environment variable');\n      return null;\n    }\n\n    // For the OnlyOffice server at only.34sy.org, we need to provide JWT\n    if (!this.jwtSecret) {\n      console.log('JWT signing disabled - no secret provided');\n      return null;\n    }\n\n    try {\n      // Create the payload exactly as OnlyOffice expects it\n      const payload = {\n        document: config.document,\n        editorConfig: config.editorConfig,\n        documentType: config.documentType,\n        width: config.width,\n        height: config.height\n      };\n\n      console.log('Signing JWT payload with secret:', this.jwtSecret === 'fzX5rJRaYYPtns6t' ? 'production secret' : 'custom secret');\n\n      // OnlyOffice expects the entire config as payload\n      const token = jwt.sign(payload, this.jwtSecret, { \n        algorithm: 'HS256',\n        expiresIn: '1h' // Add expiration for security\n      });\n\n      console.log('JWT token generated successfully, length:', token.length);\n      return token;\n    } catch (error) {\n      console.error('JWT signing error:', error);\n      return null;\n    }\n  }\n\n  verifyToken(token: string): any {\n    try {\n      return jwt.verify(token, this.jwtSecret);\n    } catch (error) {\n      throw new Error('Invalid JWT token');\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAuEO,MAAM;IACH,UAAkB;IAClB,UAAkB;IAE1B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,qBAAqB,IAAI;QACtD,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,qBAAqB,IAAI,oBAAoB,wCAAwC;QAElH,kCAAkC;QAClC,QAAQ,GAAG,CAAC,qCAAqC;YAC/C,WAAW,IAAI,CAAC,SAAS;YACzB,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS;YAC9B,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK;YACnD,aAAa,QAAQ,GAAG,CAAC,uBAAuB,KAAK;YACrD,WAAW,IAAI,CAAC,SAAS,KAAK,qBAAqB,4BAA4B;QACjF;IACF;IAEA,gBAAgB,aAAqB,EAA6B;QAChE,MAAM,cAAc;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YAAO;YAAO;YAAQ;YAAO;YAAO;YAAO;YAAQ;YAAO;YAAQ;SAAM;QACjK,MAAM,cAAc;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;SAAM;QAC/F,MAAM,eAAe;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;SAAM;QAEzF,IAAI,YAAY,QAAQ,CAAC,cAAc,WAAW,KAAK;YACrD,OAAO;QACT,OAAO,IAAI,YAAY,QAAQ,CAAC,cAAc,WAAW,KAAK;YAC5D,OAAO;QACT,OAAO,IAAI,aAAa,QAAQ,CAAC,cAAc,WAAW,KAAK;YAC7D,OAAO;QACT;QAEA,OAAO,QAAQ,UAAU;IAC3B;IAEA,oBAAoB,QAAgB,EAAE,YAAoB,EAAU;QAClE,MAAM,UAAU,GAAG,SAAS,CAAC,EAAE,aAAa,CAAC,EAAE,KAAK,GAAG,IAAI;QAC3D,OAAO,OAAO,IAAI,CAAC,SAAS,QAAQ,CAAC,UAAU,OAAO,CAAC,iBAAiB,IAAI,SAAS,CAAC,GAAG;IAC3F;IAEA,aACE,QAAgB,EAChB,OAAe,EACf,WAAmB,EACnB,SAAiB,OAAO,EACxB,WAAmB,MAAM,EACzB,OAAwB,MAAM,EACZ;QAClB,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM,QAAQ,kCAAkC;QAC7F,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC;QAC1C,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC,UAAU,KAAK,GAAG;QAE/D,MAAM,SAA2B;YAC/B;YACA,UAAU;gBACR,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,KAAK;gBACL,aAAa;oBACX,SAAS;oBACT,MAAM;oBACN,UAAU;oBACV,MAAM,SAAS;oBACf,WAAW;oBACX,cAAc;oBACd,sBAAsB;oBACtB,QAAQ;oBACR,OAAO;gBACT;YACF;YACA,cAAc;gBACZ;gBACA,MAAM;gBACN;gBACA,MAAM;oBACJ,IAAI;oBACJ,MAAM;gBACR;gBACA,eAAe;oBACb,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,eAAe;oBACf,gBAAgB;oBAChB,oBAAoB;oBACpB,UAAU;wBACR,SAAS;wBACT,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,OAAO;wBACP,KAAK;oBACP;oBACA,UAAU;wBACR,KAAK;wBACL,SAAS;oBACX;oBACA,QAAQ;wBACN,KAAK,iEAAmC;wBACxC,MAAM;oBACR;oBACA,MAAM;wBACJ,OAAO;wBACP,eAAe;wBACf,KAAK;oBACP;oBACA,eAAe;oBACf,mBAAmB;oBACnB,YAAY;oBACZ,eAAe;oBACf,MAAM;oBACN,MAAM;gBACR;YACF;YACA,OAAO;YACP,QAAQ;QACV;QAEA,OAAO;IACT;IAEA,WAAW,MAAwB,EAAiB;QAClD,sCAAsC;QACtC,IAAI,QAAQ,GAAG,CAAC,uBAAuB,KAAK,QAAQ;YAClD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,qEAAqE;QACrE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,IAAI;YACF,sDAAsD;YACtD,MAAM,UAAU;gBACd,UAAU,OAAO,QAAQ;gBACzB,cAAc,OAAO,YAAY;gBACjC,cAAc,OAAO,YAAY;gBACjC,OAAO,OAAO,KAAK;gBACnB,QAAQ,OAAO,MAAM;YACvB;YAEA,QAAQ,GAAG,CAAC,oCAAoC,IAAI,CAAC,SAAS,KAAK,qBAAqB,sBAAsB;YAE9G,kDAAkD;YAClD,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;gBAC9C,WAAW;gBACX,WAAW,KAAK,8BAA8B;YAChD;YAEA,QAAQ,GAAG,CAAC,6CAA6C,MAAM,MAAM;YACrE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO;QACT;IACF;IAEA,YAAY,KAAa,EAAO;QAC9B,IAAI;YACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/api/debug-final-config/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { OnlyOfficeService } from '@/lib/onlyoffice';\r\n\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    // Get current configuration\r\n    const onlyOfficeService = new OnlyOfficeService();\r\n    \r\n    // Get request details\r\n    const host = request.headers.get('host') || 'localhost:3001';\r\n    const protocol = request.headers.get('x-forwarded-proto') || 'http';\r\n    const currentUrl = `${protocol}://${host}`;\r\n    \r\n    // Environment check\r\n    const envConfig = {\r\n      ONLYOFFICE_SERVER_URL: process.env.ONLYOFFICE_SERVER_URL || 'NOT SET',\r\n      ONLYOFFICE_JWT_SECRET: process.env.ONLYOFFICE_JWT_SECRET ? 'SET (length: ' + process.env.ONLYOFFICE_JWT_SECRET.length + ')' : 'NOT SET',\r\n      ONLYOFFICE_JWT_DISABLED: process.env.ONLYOFFICE_JWT_DISABLED || 'NOT SET',\r\n      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'NOT SET',\r\n      NODE_ENV: process.env.NODE_ENV || 'NOT SET',\r\n      PORT: process.env.PORT || 'NOT SET'\r\n    };\r\n    \r\n    // Port analysis\r\n    const detectedPort = host.split(':')[1] || '3000';\r\n    const expectedPort = '3002';\r\n    const portMismatch = detectedPort !== expectedPort;\r\n    \r\n    // URL analysis\r\n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || currentUrl;\r\n    const expectedFileUrl = `${baseUrl}/api/files/serve/test-file-id`;\r\n    const expectedCallbackUrl = `${baseUrl}/api/onlyoffice/callback`;\r\n    \r\n    // JWT analysis\r\n    const hasJwtSecret = !!process.env.ONLYOFFICE_JWT_SECRET;\r\n    const jwtSecretCorrect = process.env.ONLYOFFICE_JWT_SECRET === 'fzX5rJRaYYPtns6t';\r\n    const jwtDisabled = process.env.ONLYOFFICE_JWT_DISABLED === 'true';\r\n    \r\n    // Create test config to see actual behavior\r\n    let testConfig = null;\r\n    let jwtToken = null;\r\n    try {\r\n      testConfig = onlyOfficeService.createConfig(\r\n        'test.docx',\r\n        expectedFileUrl,\r\n        expectedCallbackUrl,\r\n        'debug-user',\r\n        'Debug User',\r\n        'edit'\r\n      );\r\n      jwtToken = onlyOfficeService.signConfig(testConfig);\r\n    } catch (error) {\r\n      console.error('Error creating test config:', error);\r\n    }\r\n    \r\n    // Issues detection\r\n    const issues = [];\r\n    const fixes = [];\r\n    \r\n    if (!hasJwtSecret) {\r\n      issues.push('JWT secret not set');\r\n      fixes.push('Set ONLYOFFICE_JWT_SECRET=fzX5rJRaYYPtns6t in .env.local');\r\n    } else if (!jwtSecretCorrect) {\r\n      issues.push('JWT secret incorrect');\r\n      fixes.push('Update ONLYOFFICE_JWT_SECRET to fzX5rJRaYYPtns6t');\r\n    }\r\n    \r\n    if (portMismatch) {\r\n      issues.push(`Port mismatch: detected ${detectedPort}, expected ${expectedPort}`);\r\n      fixes.push(`Update NEXT_PUBLIC_APP_URL to http://*************:${detectedPort}`);\r\n    }\r\n    \r\n    if (!jwtToken && !jwtDisabled) {\r\n      issues.push('JWT token not generated despite having secret');\r\n      fixes.push('Check JWT secret configuration and ensure it matches OnlyOffice server');\r\n    }\r\n    \r\n    if (baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1')) {\r\n      issues.push('Using localhost URL - OnlyOffice server cannot access');\r\n      fixes.push('Use ngrok or update NEXT_PUBLIC_APP_URL to machine IP address');\r\n    }\r\n    \r\n    const debugInfo = {\r\n      timestamp: new Date().toISOString(),\r\n      request: {\r\n        currentUrl,\r\n        host,\r\n        protocol,\r\n        detectedPort\r\n      },\r\n      environment: envConfig,\r\n      ports: {\r\n        detected: detectedPort,\r\n        expected: expectedPort,\r\n        mismatch: portMismatch\r\n      },\r\n      urls: {\r\n        base: baseUrl,\r\n        fileServing: expectedFileUrl,\r\n        callback: expectedCallbackUrl\r\n      },\r\n      jwt: {\r\n        hasSecret: hasJwtSecret,\r\n        secretCorrect: jwtSecretCorrect,\r\n        disabled: jwtDisabled,\r\n        tokenGenerated: !!jwtToken,\r\n        tokenLength: jwtToken ? jwtToken.length : 0\r\n      },\r\n      testConfig: testConfig ? {\r\n        documentType: testConfig.documentType,\r\n        documentKey: testConfig.document.key,\r\n        fileUrl: testConfig.document.url,\r\n        callbackUrl: testConfig.editorConfig.callbackUrl\r\n      } : null,\r\n      issues,\r\n      fixes,\r\n      status: issues.length === 0 ? 'READY' : 'NEEDS_FIXES'\r\n    };\r\n    \r\n    return NextResponse.json({\r\n      success: true,\r\n      debug: debugInfo,\r\n      summary: {\r\n        totalIssues: issues.length,\r\n        status: issues.length === 0 ? 'Configuration is correct' : 'Configuration needs fixes',\r\n        criticalIssues: issues.filter(issue => \r\n          issue.includes('JWT') || \r\n          issue.includes('port') || \r\n          issue.includes('localhost')\r\n        ).length\r\n      }\r\n    });\r\n    \r\n  } catch (error) {\r\n    return NextResponse.json({\r\n      success: false,\r\n      error: error instanceof Error ? error.message : 'Unknown error',\r\n      debug: {\r\n        timestamp: new Date().toISOString(),\r\n        status: 'ERROR'\r\n      }\r\n    }, { status: 500 });\r\n  }\r\n} "], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,4BAA4B;QAC5B,MAAM,oBAAoB,IAAI,0HAAA,CAAA,oBAAiB;QAE/C,sBAAsB;QACtB,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,WAAW;QAC5C,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,wBAAwB;QAC7D,MAAM,aAAa,GAAG,SAAS,GAAG,EAAE,MAAM;QAE1C,oBAAoB;QACpB,MAAM,YAAY;YAChB,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB,IAAI;YAC5D,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB,GAAG,kBAAkB,QAAQ,GAAG,CAAC,qBAAqB,CAAC,MAAM,GAAG,MAAM;YAC9H,yBAAyB,QAAQ,GAAG,CAAC,uBAAuB,IAAI;YAChE,qBAAqB,iEAAmC;YACxD,UAAU,mDAAwB;YAClC,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI;QAC5B;QAEA,gBAAgB;QAChB,MAAM,eAAe,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;QAC3C,MAAM,eAAe;QACrB,MAAM,eAAe,iBAAiB;QAEtC,eAAe;QACf,MAAM,UAAU,iEAAmC;QACnD,MAAM,kBAAkB,GAAG,QAAQ,6BAA6B,CAAC;QACjE,MAAM,sBAAsB,GAAG,QAAQ,wBAAwB,CAAC;QAEhE,eAAe;QACf,MAAM,eAAe,CAAC,CAAC,QAAQ,GAAG,CAAC,qBAAqB;QACxD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,qBAAqB,KAAK;QAC/D,MAAM,cAAc,QAAQ,GAAG,CAAC,uBAAuB,KAAK;QAE5D,4CAA4C;QAC5C,IAAI,aAAa;QACjB,IAAI,WAAW;QACf,IAAI;YACF,aAAa,kBAAkB,YAAY,CACzC,aACA,iBACA,qBACA,cACA,cACA;YAEF,WAAW,kBAAkB,UAAU,CAAC;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QAEA,mBAAmB;QACnB,MAAM,SAAS,EAAE;QACjB,MAAM,QAAQ,EAAE;QAEhB,IAAI,CAAC,cAAc;YACjB,OAAO,IAAI,CAAC;YACZ,MAAM,IAAI,CAAC;QACb,OAAO,IAAI,CAAC,kBAAkB;YAC5B,OAAO,IAAI,CAAC;YACZ,MAAM,IAAI,CAAC;QACb;QAEA,IAAI,cAAc;YAChB,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,aAAa,WAAW,EAAE,cAAc;YAC/E,MAAM,IAAI,CAAC,CAAC,mDAAmD,EAAE,cAAc;QACjF;QAEA,IAAI,CAAC,YAAY,CAAC,aAAa;YAC7B,OAAO,IAAI,CAAC;YACZ,MAAM,IAAI,CAAC;QACb;QAEA,IAAI,QAAQ,QAAQ,CAAC,gBAAgB,QAAQ,QAAQ,CAAC,cAAc;YAClE,OAAO,IAAI,CAAC;YACZ,MAAM,IAAI,CAAC;QACb;QAEA,MAAM,YAAY;YAChB,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS;gBACP;gBACA;gBACA;gBACA;YACF;YACA,aAAa;YACb,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,UAAU;YACZ;YACA,MAAM;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;YACZ;YACA,KAAK;gBACH,WAAW;gBACX,eAAe;gBACf,UAAU;gBACV,gBAAgB,CAAC,CAAC;gBAClB,aAAa,WAAW,SAAS,MAAM,GAAG;YAC5C;YACA,YAAY,aAAa;gBACvB,cAAc,WAAW,YAAY;gBACrC,aAAa,WAAW,QAAQ,CAAC,GAAG;gBACpC,SAAS,WAAW,QAAQ,CAAC,GAAG;gBAChC,aAAa,WAAW,YAAY,CAAC,WAAW;YAClD,IAAI;YACJ;YACA;YACA,QAAQ,OAAO,MAAM,KAAK,IAAI,UAAU;QAC1C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,SAAS;gBACP,aAAa,OAAO,MAAM;gBAC1B,QAAQ,OAAO,MAAM,KAAK,IAAI,6BAA6B;gBAC3D,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAC5B,MAAM,QAAQ,CAAC,UACf,MAAM,QAAQ,CAAC,WACf,MAAM,QAAQ,CAAC,cACf,MAAM;YACV;QACF;IAEF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,OAAO;gBACL,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;YACV;QACF,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}