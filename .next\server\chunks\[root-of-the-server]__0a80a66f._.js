module.exports = {

"[project]/.next-internal/server/app/api/files/serve/[id]/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/fileUtils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FileManager": (()=>FileManager)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
;
;
;
class FileManager {
    uploadDir;
    constructor(){
        this.uploadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'uploads');
        this.ensureUploadDir();
    }
    ensureUploadDir() {
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(this.uploadDir)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(this.uploadDir, {
                recursive: true
            });
        }
    }
    async saveFile(file) {
        const fileId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(file.name);
        const filename = `${fileId}${extension}`;
        const filepath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, filename);
        // Convert File to Buffer
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        // Save file
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filepath, buffer);
        const fileInfo = {
            id: fileId,
            name: file.name || `document${extension}`,
            size: file.size,
            type: file.type,
            extension: extension.slice(1) || 'unknown',
            uploadDate: new Date(),
            lastModified: new Date(),
            url: `/uploads/${filename}`
        };
        // Save metadata
        this.saveFileMetadata(fileInfo);
        return fileInfo;
    }
    saveFileMetadata(fileInfo) {
        const metadataPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, `${fileInfo.id}.json`);
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(metadataPath, JSON.stringify(fileInfo, null, 2));
    }
    getFileMetadata(fileId) {
        try {
            const metadataPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, `${fileId}.json`);
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(metadataPath)) {
                return null;
            }
            const metadata = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(metadataPath, 'utf-8');
            return JSON.parse(metadata);
        } catch (error) {
            return null;
        }
    }
    getAllFiles() {
        try {
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(this.uploadDir);
            const metadataFiles = files.filter((file)=>file.endsWith('.json'));
            return metadataFiles.map((file)=>{
                const metadata = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, file), 'utf-8');
                return JSON.parse(metadata);
            }).sort((a, b)=>new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());
        } catch (error) {
            return [];
        }
    }
    deleteFile(fileId) {
        try {
            const fileInfo = this.getFileMetadata(fileId);
            if (!fileInfo) {
                return false;
            }
            const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(fileInfo.name);
            const filename = `${fileId}${extension}`;
            const filepath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, filename);
            const metadataPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.uploadDir, `${fileId}.json`);
            // Delete file and metadata
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filepath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(filepath);
            }
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(metadataPath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(metadataPath);
            }
            return true;
        } catch (error) {
            return false;
        }
    }
    getFileUrl(fileId) {
        const fileInfo = this.getFileMetadata(fileId);
        if (!fileInfo) {
            return null;
        }
        const baseUrl = ("TURBOPACK compile-time value", "http://*************:3001") || 'http://localhost:3000';
        return `${baseUrl}${fileInfo.url}`;
    }
    isValidFileType(filename) {
        const allowedExtensions = [
            // Word documents
            'doc',
            'docx',
            'docm',
            'dot',
            'dotx',
            'dotm',
            'odt',
            'fodt',
            'ott',
            'rtf',
            'txt',
            // Excel spreadsheets
            'xls',
            'xlsx',
            'xlsm',
            'xlt',
            'xltx',
            'xltm',
            'ods',
            'fods',
            'ots',
            'csv',
            // PowerPoint presentations
            'ppt',
            'pptx',
            'pptm',
            'pot',
            'potx',
            'potm',
            'odp',
            'fodp',
            'otp',
            // PDF
            'pdf'
        ];
        const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filename).slice(1).toLowerCase();
        return allowedExtensions.includes(extension);
    }
}
}}),
"[project]/src/app/api/files/serve/[id]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "HEAD": (()=>HEAD),
    "OPTIONS": (()=>OPTIONS)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fileUtils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/fileUtils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
async function GET(request, { params }) {
    try {
        const { id: fileId } = await params;
        console.log('File serve request for ID:', fileId);
        const fileManager = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fileUtils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FileManager"]();
        const fileInfo = fileManager.getFileMetadata(fileId);
        if (!fileInfo) {
            console.error('File metadata not found for ID:', fileId);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File not found'
            }, {
                status: 404
            });
        }
        console.log('File metadata found:', {
            id: fileId,
            name: fileInfo.name,
            type: fileInfo.type,
            size: fileInfo.size,
            extension: fileInfo.extension
        });
        // Construct file path
        const uploadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'uploads');
        // Try multiple possible file paths
        const possiblePaths = [
            // First try: file with extension from filename
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}${__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(fileInfo.name)}`),
            // Second try: file with extension from metadata
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}.${fileInfo.extension}`),
            // Third try: file without extension (as stored)
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, fileId),
            // Fourth try: file with .xlsx extension (common case)
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}.xlsx`),
            // Fifth try: file with .docx extension
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}.docx`),
            // Sixth try: file with .pptx extension
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}.pptx`)
        ];
        let filepath = '';
        for (const possiblePath of possiblePaths){
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(possiblePath)) {
                filepath = possiblePath;
                console.log('File found at path:', filepath);
                break;
            }
        }
        // Check if file exists
        if (!filepath || !__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filepath)) {
            console.error('File not found on disk. Tried paths:', possiblePaths);
            console.error('Upload directory contents:', __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(uploadDir) ? __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(uploadDir) : 'Directory does not exist');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File not found on disk'
            }, {
                status: 404
            });
        }
        // Read file
        const fileBuffer = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filepath);
        console.log('File successfully read:', {
            path: filepath,
            size: fileBuffer.length,
            type: fileInfo.type
        });
        // Set appropriate headers for OnlyOffice
        const headers = new Headers();
        headers.set('Content-Type', fileInfo.type || 'application/octet-stream');
        headers.set('Content-Length', fileBuffer.length.toString());
        headers.set('Content-Disposition', `inline; filename="${fileInfo.name}"`);
        // CORS headers for OnlyOffice - must allow all origins for external server access
        headers.set('Access-Control-Allow-Origin', '*');
        headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
        headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        headers.set('Access-Control-Allow-Credentials', 'false');
        // Cache headers
        headers.set('Cache-Control', 'public, max-age=3600');
        headers.set('ETag', `"${fileId}-${fileInfo.lastModified}"`);
        // Additional headers for OnlyOffice compatibility
        headers.set('Accept-Ranges', 'bytes');
        headers.set('X-Content-Type-Options', 'nosniff');
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](fileBuffer, {
            status: 200,
            headers
        });
    } catch (error) {
        console.error('File serve error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to serve file'
        }, {
            status: 500
        });
    }
}
async function HEAD(request, { params }) {
    try {
        const { id: fileId } = await params;
        const fileManager = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fileUtils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FileManager"]();
        const fileInfo = fileManager.getFileMetadata(fileId);
        if (!fileInfo) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
                status: 404
            });
        }
        // Construct file path
        const uploadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'uploads');
        // Try multiple possible file paths
        const possiblePaths = [
            // First try: file with extension from filename
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}${__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(fileInfo.name)}`),
            // Second try: file with extension from metadata
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}.${fileInfo.extension}`),
            // Third try: file without extension (as stored)
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, fileId),
            // Fourth try: file with .xlsx extension (common case)
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}.xlsx`),
            // Fifth try: file with .docx extension
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}.docx`),
            // Sixth try: file with .pptx extension
            __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, `${fileId}.pptx`)
        ];
        let filepath = '';
        for (const possiblePath of possiblePaths){
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(possiblePath)) {
                filepath = possiblePath;
                break;
            }
        }
        // Check if file exists
        if (!filepath || !__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filepath)) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
                status: 404
            });
        }
        // Get file stats
        const stats = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filepath);
        // Set appropriate headers
        const headers = new Headers();
        headers.set('Content-Type', fileInfo.type || 'application/octet-stream');
        headers.set('Content-Length', stats.size.toString());
        headers.set('Content-Disposition', `inline; filename="${fileInfo.name}"`);
        // CORS headers for OnlyOffice - must allow all origins for external server access
        headers.set('Access-Control-Allow-Origin', '*');
        headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
        headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        headers.set('Access-Control-Allow-Credentials', 'false');
        // Cache headers
        headers.set('Cache-Control', 'public, max-age=3600');
        headers.set('ETag', `"${fileId}-${fileInfo.lastModified}"`);
        // Additional headers for OnlyOffice compatibility
        headers.set('Accept-Ranges', 'bytes');
        headers.set('X-Content-Type-Options', 'nosniff');
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
            status: 200,
            headers
        });
    } catch (error) {
        console.error('File head error:', error);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
            status: 500
        });
    }
}
async function OPTIONS() {
    const headers = new Headers();
    headers.set('Access-Control-Allow-Origin', '*');
    headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    headers.set('Access-Control-Allow-Credentials', 'false');
    headers.set('Access-Control-Max-Age', '86400');
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__0a80a66f._.js.map