// OnlyOffice Document Server API Types

export interface OnlyOfficeDocumentPermissions {
  comment: boolean;
  copy: boolean;
  download: boolean;
  edit: boolean;
  fillForms: boolean;
  modifyFilter: boolean;
  modifyContentControl: boolean;
  review: boolean;
  print: boolean;
}

export interface OnlyOfficeDocument {
  fileType: string;
  key: string;
  title: string;
  url: string;
  permissions: OnlyOfficeDocumentPermissions;
}

export interface OnlyOfficeUser {
  id: string;
  name: string;
}

export interface OnlyOfficeCustomer {
  address: string;
  info: string;
  logo: string;
  mail: string;
  name: string;
  phone: string;
  www: string;
}

export interface OnlyOfficeFeedback {
  url: string;
  visible: boolean;
}

export interface OnlyOfficeGoback {
  url: string;
  text: string;
}

export interface OnlyOfficeLogo {
  image: string;
  imageEmbedded: string;
  url: string;
}

export interface OnlyOfficeCustomization {
  autosave: boolean;
  forcesave: boolean;
  comments: boolean;
  compactHeader: boolean;
  compactToolbar: boolean;
  compatibleFeatures: boolean;
  customer: OnlyOfficeCustomer;
  feedback: OnlyOfficeFeedback;
  goback: OnlyOfficeGoback;
  logo: OnlyOfficeLogo;
  reviewDisplay: string;
  showReviewChanges: boolean;
  spellcheck: boolean;
  toolbarNoTabs: boolean;
  unit: string;
  zoom: number;
}

export interface OnlyOfficeEditorConfig {
  mode: 'edit' | 'view';
  lang: string;
  callbackUrl: string;
  user: OnlyOfficeUser;
  customization: OnlyOfficeCustomization;
}

export interface OnlyOfficeConfig {
  documentType: 'word' | 'cell' | 'slide';
  document: OnlyOfficeDocument;
  editorConfig: OnlyOfficeEditorConfig;
  width: string;
  height: string;
  token?: string;
  events?: OnlyOfficeEvents;
}

export interface OnlyOfficeEvents {
  onDocumentReady?: () => void;
  onError?: (event: OnlyOfficeErrorEvent) => void;
  onWarning?: (event: OnlyOfficeWarningEvent) => void;
  onInfo?: (event: OnlyOfficeInfoEvent) => void;
  onRequestSaveAs?: (event: OnlyOfficeSaveAsEvent) => void;
  onRequestInsertImage?: (event: OnlyOfficeInsertImageEvent) => void;
  onRequestMailMergeRecipients?: (event: OnlyOfficeMailMergeEvent) => void;
  onRequestCompareFile?: (event: OnlyOfficeCompareFileEvent) => void;
  onRequestEditRights?: () => void;
  onRequestHistory?: () => void;
  onRequestHistoryClose?: () => void;
  onRequestHistoryData?: (event: OnlyOfficeHistoryDataEvent) => void;
  onRequestRestore?: (event: OnlyOfficeRestoreEvent) => void;
}

export interface OnlyOfficeErrorEvent {
  data: string;
}

export interface OnlyOfficeWarningEvent {
  data: string;
}

export interface OnlyOfficeInfoEvent {
  data: string;
}

export interface OnlyOfficeSaveAsEvent {
  data: {
    title: string;
    url: string;
  };
}

export interface OnlyOfficeInsertImageEvent {
  data: {
    c: string;
    token?: string;
  };
}

export interface OnlyOfficeMailMergeEvent {
  data: {
    c: string;
    token?: string;
  };
}

export interface OnlyOfficeCompareFileEvent {
  data: {
    c: string;
    token?: string;
  };
}

export interface OnlyOfficeHistoryDataEvent {
  data: {
    key: string;
    version: number;
  };
}

export interface OnlyOfficeRestoreEvent {
  data: {
    key: string;
    version: number;
  };
}

// Callback API Types
export interface OnlyOfficeCallbackData {
  key: string;
  status: OnlyOfficeCallbackStatus;
  url?: string;
  users?: string[];
  actions?: OnlyOfficeAction[];
  lastsave?: string;
  notmodified?: boolean;
  forcesavetype?: number;
}

export enum OnlyOfficeCallbackStatus {
  NotFound = 0,
  Editing = 1,
  MustSave = 2,
  Corrupted = 3,
  Closed = 4,
  MustForceSave = 6,
  CorruptedForceSave = 7
}

export interface OnlyOfficeAction {
  type: number;
  userid: string;
}

// File Management Types
export interface FileMetadata {
  id: string;
  name: string;
  size: number;
  type: string;
  extension: string;
  uploadDate: Date;
  lastModified: Date;
  url: string;
}

export interface UploadResponse {
  success: boolean;
  file?: FileMetadata;
  error?: string;
}

export interface FilesResponse {
  success: boolean;
  files?: FileMetadata[];
  error?: string;
}

export interface DeleteResponse {
  success: boolean;
  message?: string;
  error?: string;
}

export interface ConfigResponse {
  success: boolean;
  config?: OnlyOfficeConfig;
  token?: string;
  serverUrl?: string;
  error?: string;
}

// Error Types
export interface AppError {
  message: string;
  code?: string;
  details?: any;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: any;
}

// Component Props Types
export interface FileUploadProps {
  onUploadSuccess: (file: FileMetadata) => void;
  onUploadError: (error: string) => void;
}

export interface FileCardProps {
  file: FileMetadata;
  onDelete: (fileId: string) => void;
}

export interface FileListProps {
  onRefresh?: () => void;
  refreshTrigger?: number;
}

export interface OnlyOfficeEditorProps {
  fileId: string;
  mode?: 'edit' | 'view';
  userId?: string;
  userName?: string;
  onError?: (error: string) => void;
  onDocumentReady?: () => void;
}

// Utility Types
export type DocumentType = 'word' | 'cell' | 'slide';
export type EditorMode = 'edit' | 'view';
export type SortBy = 'name' | 'date' | 'size';
export type SortOrder = 'asc' | 'desc';
export type FilterType = 'all' | 'word' | 'excel' | 'powerpoint' | 'pdf';
export type NotificationType = 'success' | 'error' | 'warning' | 'info';
