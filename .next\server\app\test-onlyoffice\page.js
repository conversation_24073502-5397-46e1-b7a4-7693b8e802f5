(()=>{var e={};e.id=416,e.ids=[416],e.modules={297:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},972:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var o=r(687),s=r(3210);function i(){let[e,t]=(0,s.useState)([]),[r,i]=(0,s.useState)("Not loaded");return(0,o.jsxs)("div",{className:"container mx-auto p-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"OnlyOffice API Test"}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Status"}),(0,o.jsx)("div",{className:`p-3 rounded ${"Available and ready"===r?"bg-green-100 text-green-800":"Failed to load"===r||"Script loading failed"===r?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"}`,children:r})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Debug Logs"}),(0,o.jsx)("div",{className:"bg-gray-100 p-4 rounded max-h-96 overflow-y-auto",children:e.map((e,t)=>(0,o.jsx)("div",{className:"text-sm font-mono mb-1",children:e},t))})]}),(0,o.jsx)("div",{className:"mt-6",children:(0,o.jsx)("button",{onClick:()=>{t([]),i("Not loaded"),window.location.reload()},className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",children:"Reload Test"})})]})}},1135:()=>{},2153:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l,viewport:()=>d});var o=r(7413),s=r(2376),i=r.n(s),n=r(8726),a=r.n(n);r(1135);let l={title:"OnlyOffice Demo - Document Collaboration",description:"A professional demo application showcasing OnlyOffice Document Server integration with Next.js for collaborative document editing.",keywords:"OnlyOffice, document editing, collaboration, Next.js, TypeScript",authors:[{name:"OnlyOffice Demo Team"}]},d={width:"device-width",initialScale:1};function c({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:e})})}},4611:()=>{},4859:()=>{},7834:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\xampp\\\\htdocs\\\\ai\\\\onlyofice\\\\src\\\\app\\\\test-onlyoffice\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\test-onlyoffice\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9143:(e,t,r)=>{Promise.resolve().then(r.bind(r,7834))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9391:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var o=r(5239),s=r(8088),i=r(8170),n=r.n(i),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["test-onlyoffice",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7834)),"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\test-onlyoffice\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\test-onlyoffice\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/test-onlyoffice/page",pathname:"/test-onlyoffice",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9407:(e,t,r)=>{Promise.resolve().then(r.bind(r,972))},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,145,658],()=>r(9391));module.exports=o})();