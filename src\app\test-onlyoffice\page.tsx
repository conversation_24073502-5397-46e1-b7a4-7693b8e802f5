'use client';

import { useEffect, useState } from 'react';

export default function TestOnlyOfficePage() {
  const [logs, setLogs] = useState<string[]>([]);
  const [docsAPIStatus, setDocsAPIStatus] = useState<string>('Not loaded');

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    addLog('Starting OnlyOffice API test...');
    
    // Check if DocsAPI is already available
    if (window.DocsAPI) {
      addLog('DocsAPI already available on window');
      setDocsAPIStatus('Already available');
      return;
    }

    addLog('DocsAPI not available, loading script...');
    
    const script = document.createElement('script');
    script.src = 'https://only.34sy.org/web-apps/apps/api/documents/api.js';
    script.async = true;
    script.crossOrigin = 'anonymous';
    
    script.onload = () => {
      addLog('Script loaded successfully');
      
      const checkDocsAPI = (attempts = 0) => {
        addLog(`Checking for DocsAPI, attempt ${attempts + 1}/10`);
        addLog(`window.DocsAPI: ${typeof window.DocsAPI}`);
        
        if (window.DocsAPI) {
          addLog(`DocsAPI found! Type: ${typeof window.DocsAPI}`);
          addLog(`DocsAPI.DocEditor: ${typeof window.DocsAPI.DocEditor}`);
          
          if (typeof window.DocsAPI.DocEditor === 'function') {
            addLog('DocsAPI.DocEditor is a function - SUCCESS!');
            setDocsAPIStatus('Available and ready');
          } else {
            addLog('DocsAPI.DocEditor is not a function');
            setDocsAPIStatus('Available but DocEditor missing');
          }
        } else if (attempts < 10) {
          addLog(`DocsAPI not yet available, retrying in 100ms...`);
          setTimeout(() => checkDocsAPI(attempts + 1), 100);
        } else {
          addLog('DocsAPI not available after 10 attempts');
          setDocsAPIStatus('Failed to load');
          
          // Log all available window properties that might be related
          const windowProps = Object.keys(window).filter(key => 
            key.toLowerCase().includes('docs') || 
            key.toLowerCase().includes('only') ||
            key.toLowerCase().includes('office')
          );
          addLog(`Related window properties: ${windowProps.join(', ')}`);
        }
      };
      
      checkDocsAPI();
    };
    
    script.onerror = (error) => {
      addLog(`Script loading error: ${error}`);
      setDocsAPIStatus('Script loading failed');
    };
    
    document.head.appendChild(script);
    
    return () => {
      // Cleanup
      const scripts = document.querySelectorAll('script[src*="only.34sy.org"]');
      scripts.forEach(s => s.remove());
    };
  }, []);

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">OnlyOffice API Test</h1>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Status</h2>
        <div className={`p-3 rounded ${
          docsAPIStatus === 'Available and ready' ? 'bg-green-100 text-green-800' :
          docsAPIStatus === 'Failed to load' || docsAPIStatus === 'Script loading failed' ? 'bg-red-100 text-red-800' :
          'bg-yellow-100 text-yellow-800'
        }`}>
          {docsAPIStatus}
        </div>
      </div>
      
      <div>
        <h2 className="text-lg font-semibold mb-2">Debug Logs</h2>
        <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index} className="text-sm font-mono mb-1">
              {log}
            </div>
          ))}
        </div>
      </div>
      
      <div className="mt-6">
        <button 
          onClick={() => {
            setLogs([]);
            setDocsAPIStatus('Not loaded');
            window.location.reload();
          }}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Reload Test
        </button>
      </div>
    </div>
  );
}
