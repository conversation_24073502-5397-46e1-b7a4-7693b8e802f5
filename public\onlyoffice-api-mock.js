// OnlyOffice API Mock for Testing
// This is a simplified mock of the OnlyOffice Document Server API
// Use only when the actual OnlyOffice server is not accessible

(function() {
  'use strict';

  // Mock DocsAPI object
  window.DocsAPI = {
    DocEditor: function(containerId, config) {
      console.log('OnlyOffice Mock: Initializing editor with config:', config);
      
      this.containerId = containerId;
      this.config = config;
      
      // Get the container element
      const container = document.getElementById(containerId);
      if (!container) {
        console.error('OnlyOffice Mock: Container not found:', containerId);
        return;
      }

      // Create mock editor interface
      this.createMockEditor(container);
      
      // Simulate document ready event
      setTimeout(() => {
        if (config.events && config.events.onDocumentReady) {
          console.log('OnlyOffice Mock: Triggering onDocumentReady');
          config.events.onDocumentReady();
        }
      }, 1000);

      return this;
    }
  };

  // Add methods to DocEditor prototype
  window.DocsAPI.DocEditor.prototype = {
    createMockEditor: function(container) {
      // Clear container
      container.innerHTML = '';
      
      // Create mock editor UI
      const mockEditor = document.createElement('div');
      mockEditor.style.cssText = `
        width: 100%;
        height: 100%;
        min-height: 600px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-family: Arial, sans-serif;
        text-align: center;
        position: relative;
      `;

      // Add mock content
      mockEditor.innerHTML = `
        <div style="max-width: 500px; padding: 40px;">
          <div style="font-size: 48px; margin-bottom: 20px;">📄</div>
          <h2 style="margin: 0 0 20px 0; font-size: 28px;">OnlyOffice Mock Editor</h2>
          <p style="margin: 0 0 20px 0; font-size: 16px; opacity: 0.9;">
            This is a mock editor interface for testing purposes.
          </p>
          <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h3 style="margin: 0 0 10px 0;">Document Info:</h3>
            <p style="margin: 5px 0; font-size: 14px;">File: ${this.config.document?.title || 'Unknown'}</p>
            <p style="margin: 5px 0; font-size: 14px;">Type: ${this.config.documentType || 'Unknown'}</p>
            <p style="margin: 5px 0; font-size: 14px;">Mode: ${this.config.editorConfig?.mode || 'Unknown'}</p>
            <p style="margin: 5px 0; font-size: 14px;">Key: ${this.config.document?.key || 'Unknown'}</p>
          </div>
          <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 20px 0;">
            <p style="margin: 0; font-size: 14px; opacity: 0.8;">
              ✅ Configuration loaded successfully<br>
              ✅ JWT token validated<br>
              ✅ Document accessible<br>
              ✅ Editor initialized
            </p>
          </div>
          <button onclick="window.location.href='/'" style="
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
          ">Back to Documents</button>
        </div>
      `;

      container.appendChild(mockEditor);
    },

    destroyEditor: function() {
      console.log('OnlyOffice Mock: Destroying editor');
      const container = document.getElementById(this.containerId);
      if (container) {
        container.innerHTML = '';
      }
    }
  };

  console.log('OnlyOffice Mock API loaded successfully');
})();
