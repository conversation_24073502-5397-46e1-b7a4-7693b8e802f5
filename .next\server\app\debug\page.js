(()=>{var e={};e.id=302,e.ids=[302],e.modules={297:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2153:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3725:(e,t,s)=>{Promise.resolve().then(s.bind(s,8286))},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>l,viewport:()=>d});var r=s(7413),i=s(2376),n=s.n(i),o=s(8726),a=s.n(o);s(1135);let l={title:"OnlyOffice Demo - Document Collaboration",description:"A professional demo application showcasing OnlyOffice Document Server integration with Next.js for collaborative document editing.",keywords:"OnlyOffice, document editing, collaboration, Next.js, TypeScript",authors:[{name:"OnlyOffice Demo Team"}]},d={width:"device-width",initialScale:1};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${a().variable} antialiased`,children:e})})}},4611:()=>{},4859:()=>{},8024:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\xampp\\\\htdocs\\\\ai\\\\onlyofice\\\\src\\\\app\\\\debug\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\debug\\page.tsx","default")},8286:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(687),i=s(3210);function n(){let[e,t]=(0,i.useState)(!1),[s,n]=(0,i.useState)(!1),[o,a]=(0,i.useState)(null),l=async()=>{try{let e=await fetch("/api/files/serve/79e10043-01ae-449b-930a-c81125abde3b",{method:"HEAD"});console.log("File serving test:",e.status,e.headers)}catch(e){console.error("File serving test failed:",e)}},d=async()=>{try{let e=await fetch("/api/onlyoffice/config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fileId:"79e10043-01ae-449b-930a-c81125abde3b",mode:"edit",userId:"debug-user",userName:"Debug User"})}),t=await e.json();console.log("Config test:",t)}catch(e){console.error("Config test failed:",e)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"OnlyOffice Debug Page"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Script Loading Status"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:`flex items-center ${e?"text-green-600":"text-red-600"}`,children:[(0,r.jsx)("span",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e?"green":"red"}}),"Script Loaded: ",e?"Yes":"No"]}),(0,r.jsxs)("div",{className:`flex items-center ${s?"text-green-600":"text-red-600"}`,children:[(0,r.jsx)("span",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:s?"green":"red"}}),"DocsAPI Available: ",s?"Yes":"No"]}),o&&(0,r.jsxs)("div",{className:"text-red-600 mt-2",children:["Error: ",o]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Functions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{onClick:l,className:"w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Test File Serving"}),(0,r.jsx)("button",{onClick:d,className:"w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Test Config Generation"}),(0,r.jsx)("button",{onClick:()=>window.open("/api/files/serve/79e10043-01ae-449b-930a-c81125abde3b","_blank"),className:"w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700",children:"Open File Direct"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment Info"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:["User Agent: ","undefined"!=typeof navigator?navigator.userAgent:"N/A"]}),(0,r.jsxs)("div",{children:["Current URL: ","N/A"]}),(0,r.jsxs)("div",{children:["Protocol: ","N/A"]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Instructions"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{children:"1. Check the browser console for detailed logs"}),(0,r.jsx)("p",{children:"2. Test file serving to ensure CORS is working"}),(0,r.jsx)("p",{children:"3. Test config generation to verify API"}),(0,r.jsx)("p",{children:"4. Check if OnlyOffice script loads from only.34sy.org"})]})]})]})]})})}},8813:(e,t,s)=>{Promise.resolve().then(s.bind(s,8024))},9119:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(5239),i=s(8088),n=s(8170),o=s.n(n),a=s(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(t,l);let d={children:["",{children:["debug",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8024)),"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\debug\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\debug\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/debug/page",pathname:"/debug",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,145,658],()=>s(9119));module.exports=r})();