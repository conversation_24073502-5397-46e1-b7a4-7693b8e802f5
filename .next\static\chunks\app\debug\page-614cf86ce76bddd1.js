(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[302],{1403:(e,s,o)=>{Promise.resolve().then(o.bind(o,7152))},7152:(e,s,o)=>{"use strict";o.r(s),o.d(s,{default:()=>a});var l=o(5155),n=o(2115);function a(){let[e,s]=(0,n.useState)(!1),[o,a]=(0,n.useState)(!1),[c,t]=(0,n.useState)(null);(0,n.useEffect)(()=>{(async()=>{try{if(window.DocsAPI){a(!0),s(!0);return}let e=["https://only.34sy.org/web-apps/apps/api/documents/api.js","https://documentserver.onlyoffice.com/web-apps/apps/api/documents/api.js","https://api.onlyoffice.com/editors/api.js"],o=0,l=()=>{if(o>=e.length){console.log("Trying OnlyOffice mock API as fallback...");let e=document.createElement("script");e.src="/onlyoffice-api-mock.js",e.async=!0,e.onload=()=>{console.log("OnlyOffice Mock API loaded successfully"),s(!0),window.DocsAPI?(a(!0),console.log("DocsAPI is available (Mock):",window.DocsAPI)):t("DocsAPI not available even with mock")},e.onerror=()=>{t("Failed to load OnlyOffice API from all available servers and mock")},document.head.appendChild(e);return}let n=document.createElement("script");n.src=e[o],n.async=!0,n.crossOrigin="anonymous",n.onload=()=>{console.log("OnlyOffice script loaded from: ".concat(e[o])),s(!0),window.DocsAPI?(a(!0),console.log("DocsAPI is available:",window.DocsAPI)):(console.warn("DocsAPI not available from: ".concat(e[o])),o++,l())},n.onerror=s=>{console.error("Failed to load OnlyOffice script from: ".concat(e[o]),s),o++,l()},console.log("Trying to load OnlyOffice script from: ".concat(e[o])),document.head.appendChild(n)};l()}catch(e){t("Error: ".concat(e))}})()},[]);let i=async()=>{try{let e=await fetch("/api/files/serve/79e10043-01ae-449b-930a-c81125abde3b",{method:"HEAD"});console.log("File serving test:",e.status,e.headers)}catch(e){console.error("File serving test failed:",e)}},r=async()=>{try{let e=await fetch("/api/onlyoffice/config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fileId:"79e10043-01ae-449b-930a-c81125abde3b",mode:"edit",userId:"debug-user",userName:"Debug User"})}),s=await e.json();console.log("Config test:",s)}catch(e){console.error("Config test failed:",e)}};return(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"OnlyOffice Debug Page"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Script Loading Status"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center ".concat(e?"text-green-600":"text-red-600"),children:[(0,l.jsx)("span",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e?"green":"red"}}),"Script Loaded: ",e?"Yes":"No"]}),(0,l.jsxs)("div",{className:"flex items-center ".concat(o?"text-green-600":"text-red-600"),children:[(0,l.jsx)("span",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:o?"green":"red"}}),"DocsAPI Available: ",o?"Yes":"No"]}),c&&(0,l.jsxs)("div",{className:"text-red-600 mt-2",children:["Error: ",c]})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Functions"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{onClick:i,className:"w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Test File Serving"}),(0,l.jsx)("button",{onClick:r,className:"w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Test Config Generation"}),(0,l.jsx)("button",{onClick:()=>window.open("/api/files/serve/79e10043-01ae-449b-930a-c81125abde3b","_blank"),className:"w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700",children:"Open File Direct"})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment Info"}),(0,l.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,l.jsxs)("div",{children:["User Agent: ","undefined"!=typeof navigator?navigator.userAgent:"N/A"]}),(0,l.jsxs)("div",{children:["Current URL: ",window.location.href]}),(0,l.jsxs)("div",{children:["Protocol: ",window.location.protocol]})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Instructions"}),(0,l.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,l.jsx)("p",{children:"1. Check the browser console for detailed logs"}),(0,l.jsx)("p",{children:"2. Test file serving to ensure CORS is working"}),(0,l.jsx)("p",{children:"3. Test config generation to verify API"}),(0,l.jsx)("p",{children:"4. Check if OnlyOffice script loads from only.34sy.org"})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(1403)),_N_E=e.O()}]);