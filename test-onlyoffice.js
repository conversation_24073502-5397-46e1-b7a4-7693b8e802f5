#!/usr/bin/env node

const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  onlyofficeUrl: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',
  appUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001',
  uploadsDir: path.join(process.cwd(), 'public', 'uploads')
};

console.log('OnlyOffice Integration Test');
console.log('=========================');
console.log('Configuration:', config);
console.log('');

// Test functions
async function testOnlyOfficeServer() {
  console.log('1. Testing OnlyOffice server accessibility...');
  
  return new Promise((resolve) => {
    const url = new URL(config.onlyofficeUrl + '/healthcheck');
    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.get(url, (res) => {
      console.log(`   ✓ OnlyOffice server responding: ${res.statusCode} ${res.statusMessage}`);
      resolve(true);
    });
    
    req.on('error', (err) => {
      console.log(`   ✗ OnlyOffice server not accessible: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('   ✗ OnlyOffice server timeout');
      resolve(false);
    });
  });
}

async function testUploadsDirectory() {
  console.log('2. Testing uploads directory...');
  
  if (!fs.existsSync(config.uploadsDir)) {
    console.log('   ✗ Uploads directory does not exist:', config.uploadsDir);
    return false;
  }
  
  const files = fs.readdirSync(config.uploadsDir);
  console.log(`   ✓ Uploads directory exists with ${files.length} files`);
  
  if (files.length === 0) {
    console.log('   ⚠ No files in uploads directory');
    return false;
  }
  
  return true;
}

async function testFileServing() {
  console.log('3. Testing file serving endpoint...');
  
  // Get first file from uploads
  const files = fs.readdirSync(config.uploadsDir);
  const jsonFiles = files.filter(f => f.endsWith('.json'));
  
  if (jsonFiles.length === 0) {
    console.log('   ✗ No metadata files found');
    return false;
  }
  
  const metadataFile = jsonFiles[0];
  const fileId = metadataFile.replace('.json', '');
  
  console.log(`   Testing with file ID: ${fileId}`);
  
  return new Promise((resolve) => {
    const url = new URL(`${config.appUrl}/api/files/serve/${fileId}`);
    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request(url, { method: 'HEAD' }, (res) => {
      if (res.statusCode === 200) {
        console.log('   ✓ File serving endpoint working');
        console.log(`   Content-Type: ${res.headers['content-type']}`);
        console.log(`   Content-Length: ${res.headers['content-length']}`);
        resolve(true);
      } else {
        console.log(`   ✗ File serving failed: ${res.statusCode} ${res.statusMessage}`);
        resolve(false);
      }
    });
    
    req.on('error', (err) => {
      console.log(`   ✗ File serving error: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('   ✗ File serving timeout');
      resolve(false);
    });
    
    req.end();
  });
}

async function testConfigEndpoint() {
  console.log('4. Testing config endpoint...');
  
  // Get first file from uploads
  const files = fs.readdirSync(config.uploadsDir);
  const jsonFiles = files.filter(f => f.endsWith('.json'));
  
  if (jsonFiles.length === 0) {
    console.log('   ✗ No metadata files found');
    return false;
  }
  
  const metadataFile = jsonFiles[0];
  const fileId = metadataFile.replace('.json', '');
  
  return new Promise((resolve) => {
    const url = new URL(`${config.appUrl}/api/onlyoffice/config`);
    const client = url.protocol === 'https:' ? https : http;
    
    const postData = JSON.stringify({
      fileId,
      mode: 'edit',
      userId: 'test-user',
      userName: 'Test User'
    });
    
    const req = client.request(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.success) {
            console.log('   ✓ Config endpoint working');
            console.log(`   Document key: ${result.config?.document?.key}`);
            console.log(`   Document URL: ${result.config?.document?.url}`);
            resolve(true);
          } else {
            console.log('   ✗ Config endpoint failed:', result.error);
            resolve(false);
          }
        } catch (err) {
          console.log('   ✗ Config endpoint response parse error:', err.message);
          resolve(false);
        }
      });
    });
    
    req.on('error', (err) => {
      console.log(`   ✗ Config endpoint error: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('   ✗ Config endpoint timeout');
      resolve(false);
    });
    
    req.write(postData);
    req.end();
  });
}

// Run all tests
async function runTests() {
  const results = [];
  
  results.push(await testOnlyOfficeServer());
  results.push(await testUploadsDirectory());
  results.push(await testFileServing());
  results.push(await testConfigEndpoint());
  
  console.log('');
  console.log('Test Results:');
  console.log('=============');
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('✓ All tests passed! OnlyOffice integration should work.');
  } else {
    console.log('✗ Some tests failed. Check the issues above.');
    console.log('');
    console.log('Common fixes:');
    console.log('- Make sure OnlyOffice Docker container is running');
    console.log('- Check environment variables are set correctly');
    console.log('- Verify NEXT_PUBLIC_APP_URL is accessible from OnlyOffice server');
    console.log('- Upload some test files to public/uploads/');
  }
}

// Run the tests
runTests().catch(console.error); 