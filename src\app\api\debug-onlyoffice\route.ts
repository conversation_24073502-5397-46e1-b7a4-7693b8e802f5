import { NextRequest, NextResponse } from 'next/server';
import { FileManager } from '@/lib/fileUtils';
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  const debugInfo = {
    timestamp: new Date().toISOString(),
    environment: {
      ONLYOFFICE_SERVER_URL: process.env.ONLYOFFICE_SERVER_URL || 'NOT SET',
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'NOT SET',
      ONLYOFFICE_JWT_SECRET: process.env.ONLYOFFICE_JWT_SECRET ? 'SET' : 'NOT SET',
      NODE_ENV: process.env.NODE_ENV || 'NOT SET',
      PORT: process.env.PORT || 'NOT SET'
    },
    files: {
      uploadDir: path.join(process.cwd(), 'public', 'uploads'),
      exists: false,
      contents: [] as string[]
    },
    onlyofficeServer: {
      url: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',
      accessible: false,
      response: null as any
    },
    sampleFile: {
      exists: false,
      accessible: false,
      fileId: null as string | null,
      metadata: null as any
    }
  };

  try {
    // Check uploads directory
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    if (fs.existsSync(uploadDir)) {
      debugInfo.files.exists = true;
      debugInfo.files.contents = fs.readdirSync(uploadDir).filter(f => !f.startsWith('.'));
    }

    // Check if we have any files
    const fileManager = new FileManager();
    const allFiles = fileManager.getAllFiles();
    if (allFiles.length > 0) {
      const sampleFile = allFiles[0];
      debugInfo.sampleFile.exists = true;
      debugInfo.sampleFile.fileId = sampleFile.id;
      debugInfo.sampleFile.metadata = sampleFile;

      // Check if file is accessible
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001';
      const fileUrl = `${baseUrl}/api/files/serve/${sampleFile.id}`;
      
      try {
        const fileResponse = await fetch(fileUrl, { method: 'HEAD' });
        debugInfo.sampleFile.accessible = fileResponse.ok;
      } catch (error) {
        debugInfo.sampleFile.accessible = false;
      }
    }

    // Test OnlyOffice server accessibility
    const onlyofficeUrl = process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080';
    try {
      const response = await fetch(`${onlyofficeUrl}/healthcheck`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      debugInfo.onlyofficeServer.accessible = response.ok;
      debugInfo.onlyofficeServer.response = {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      };
    } catch (error) {
      debugInfo.onlyofficeServer.accessible = false;
      debugInfo.onlyofficeServer.response = {
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    return NextResponse.json({
      success: true,
      debug: debugInfo,
      recommendations: generateRecommendations(debugInfo)
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      debug: debugInfo
    });
  }
}

function generateRecommendations(debugInfo: any): string[] {
  const recommendations: string[] = [];

  if (debugInfo.environment.ONLYOFFICE_SERVER_URL === 'NOT SET') {
    recommendations.push('Set ONLYOFFICE_SERVER_URL environment variable');
  }

  if (debugInfo.environment.NEXT_PUBLIC_APP_URL === 'NOT SET') {
    recommendations.push('Set NEXT_PUBLIC_APP_URL environment variable');
  }

  if (debugInfo.environment.NEXT_PUBLIC_APP_URL.includes('localhost')) {
    recommendations.push('Consider using your machine\'s IP address instead of localhost for Docker integration');
  }

  if (!debugInfo.files.exists) {
    recommendations.push('Create uploads directory: public/uploads');
  }

  if (debugInfo.files.contents.length === 0) {
    recommendations.push('Upload some test files to test the integration');
  }

  if (!debugInfo.onlyofficeServer.accessible) {
    recommendations.push('Start OnlyOffice Docker container: docker run -d --name onlyoffice -p 8080:80 onlyoffice/documentserver');
  }

  if (debugInfo.sampleFile.exists && !debugInfo.sampleFile.accessible) {
    recommendations.push('Check file serving endpoint - files may not be accessible from OnlyOffice server');
  }

  return recommendations;
} 