{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef } from 'react';\n\ninterface FileUploadProps {\n  onUploadSuccess: (file: any) => void;\n  onUploadError: (error: string) => void;\n}\n\nexport default function FileUpload({ onUploadSuccess, onUploadError }: FileUploadProps) {\n  const [isUploading, setIsUploading] = useState(false);\n  const [dragActive, setDragActive] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFiles = async (files: FileList) => {\n    if (files.length === 0) return;\n\n    const file = files[0];\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        onUploadSuccess(result.file);\n      } else {\n        onUploadError(result.error || 'Upload failed');\n      }\n    } catch (error) {\n      onUploadError('Upload failed. Please try again.');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleDrag = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleFiles(e.dataTransfer.files);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    e.preventDefault();\n    if (e.target.files && e.target.files[0]) {\n      handleFiles(e.target.files);\n    }\n  };\n\n  const onButtonClick = () => {\n    fileInputRef.current?.click();\n  };\n\n  return (\n    <div className=\"w-full\">\n      <div\n        className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${\n          dragActive\n            ? 'border-blue-400 bg-blue-50'\n            : 'border-gray-300 hover:border-gray-400'\n        } ${isUploading ? 'opacity-50 pointer-events-none' : ''}`}\n        onDragEnter={handleDrag}\n        onDragLeave={handleDrag}\n        onDragOver={handleDrag}\n        onDrop={handleDrop}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          className=\"hidden\"\n          accept=\".doc,.docx,.docm,.dot,.dotx,.dotm,.odt,.fodt,.ott,.rtf,.txt,.xls,.xlsx,.xlsm,.xlt,.xltx,.xltm,.ods,.fods,.ots,.csv,.ppt,.pptx,.pptm,.pot,.potx,.potm,.odp,.fodp,.otp,.pdf\"\n          onChange={handleChange}\n          disabled={isUploading}\n        />\n\n        <div className=\"text-center\">\n          {isUploading ? (\n            <div className=\"flex flex-col items-center\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4\"></div>\n              <p className=\"text-sm text-gray-600\">Uploading...</p>\n            </div>\n          ) : (\n            <>\n              <svg\n                className=\"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                stroke=\"currentColor\"\n                fill=\"none\"\n                viewBox=\"0 0 48 48\"\n              >\n                <path\n                  d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\n                  strokeWidth={2}\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                />\n              </svg>\n              <div className=\"flex text-sm text-gray-600\">\n                <label className=\"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500\">\n                  <span>Upload a file</span>\n                </label>\n                <p className=\"pl-1\">or drag and drop</p>\n              </div>\n              <p className=\"text-xs text-gray-500 mt-2\">\n                Word, Excel, PowerPoint, or PDF files up to 50MB\n              </p>\n              <button\n                type=\"button\"\n                onClick={onButtonClick}\n                className=\"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Choose File\n              </button>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,WAAW,EAAE,eAAe,EAAE,aAAa,EAAmB;;IACpF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,cAAc,OAAO;QACzB,IAAI,MAAM,MAAM,KAAK,GAAG;QAExB,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,gBAAgB,OAAO,IAAI;YAC7B,OAAO;gBACL,cAAc,OAAO,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,cAAc;QAChB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,YAAY,EAAE,YAAY,CAAC,KAAK;QAClC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,YAAY,EAAE,MAAM,CAAC,KAAK;QAC5B;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa,OAAO,EAAE;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAW,CAAC,iEAAiE,EAC3E,aACI,+BACA,wCACL,CAAC,EAAE,cAAc,mCAAmC,IAAI;YACzD,aAAa;YACb,aAAa;YACb,YAAY;YACZ,QAAQ;;8BAER,6LAAC;oBACC,KAAK;oBACL,MAAK;oBACL,WAAU;oBACV,QAAO;oBACP,UAAU;oBACV,UAAU;;;;;;8BAGZ,6LAAC;oBAAI,WAAU;8BACZ,4BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;6CAGvC;;0CACE,6LAAC;gCACC,WAAU;gCACV,QAAO;gCACP,MAAK;gCACL,SAAQ;0CAER,cAAA,6LAAC;oCACC,GAAE;oCACF,aAAa;oCACb,eAAc;oCACd,gBAAe;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;sDAAK;;;;;;;;;;;kDAER,6LAAC;wCAAE,WAAU;kDAAO;;;;;;;;;;;;0CAEtB,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAlIwB;KAAA", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/components/FileCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface FileInfo {\n  id: string;\n  name: string;\n  size: number;\n  type: string;\n  extension: string;\n  uploadDate: string;\n  lastModified: string;\n  url: string;\n}\n\ninterface FileCardProps {\n  file: FileInfo;\n  onDelete: (fileId: string) => void;\n}\n\nexport default function FileCard({ file, onDelete }: FileCardProps) {\n  const router = useRouter();\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n\n  const getFileIcon = (extension: string) => {\n    const ext = extension.toLowerCase();\n    \n    if (['doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt'].includes(ext)) {\n      return (\n        <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n          <svg className=\"w-6 h-6 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n      );\n    } else if (['xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv'].includes(ext)) {\n      return (\n        <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n          <svg className=\"w-6 h-6 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n      );\n    } else if (['ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp'].includes(ext)) {\n      return (\n        <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n          <svg className=\"w-6 h-6 text-orange-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n      );\n    } else if (ext === 'pdf') {\n      return (\n        <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n          <svg className=\"w-6 h-6 text-red-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center\">\n        <svg className=\"w-6 h-6 text-gray-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z\" clipRule=\"evenodd\" />\n        </svg>\n      </div>\n    );\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const handleEdit = () => {\n    router.push(`/editor/${file.id}`);\n  };\n\n  const handleView = () => {\n    router.push(`/editor/${file.id}?mode=view`);\n  };\n\n  const handleDeleteClick = () => {\n    setShowDeleteConfirm(true);\n  };\n\n  const handleDeleteConfirm = async () => {\n    setIsDeleting(true);\n    try {\n      const response = await fetch(`/api/files/${file.id}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        onDelete(file.id);\n      } else {\n        console.error('Failed to delete file');\n      }\n    } catch (error) {\n      console.error('Error deleting file:', error);\n    } finally {\n      setIsDeleting(false);\n      setShowDeleteConfirm(false);\n    }\n  };\n\n  const handleDeleteCancel = () => {\n    setShowDeleteConfirm(false);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow\">\n      <div className=\"flex items-start space-x-3\">\n        {getFileIcon(file.extension)}\n        <div className=\"flex-1 min-w-0\">\n          <h3 className=\"text-sm font-medium text-gray-900 truncate\" title={file.name}>\n            {file.name}\n          </h3>\n          <p className=\"text-xs text-gray-500 mt-1\">\n            {formatFileSize(file.size)} • {file.extension.toUpperCase()}\n          </p>\n          <p className=\"text-xs text-gray-400 mt-1\">\n            Modified: {new Date(file.lastModified).toLocaleDateString()}\n          </p>\n        </div>\n        <div className=\"flex-shrink-0\">\n          <div className=\"flex items-center space-x-1\">\n            <button\n              onClick={handleEdit}\n              className=\"p-1 text-gray-400 hover:text-blue-600 transition-colors\"\n              title=\"Edit document\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n              </svg>\n            </button>\n            <button\n              onClick={handleView}\n              className=\"p-1 text-gray-400 hover:text-green-600 transition-colors\"\n              title=\"View document\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n              </svg>\n            </button>\n            <button\n              onClick={handleDeleteClick}\n              disabled={isDeleting}\n              className=\"p-1 text-gray-400 hover:text-red-600 transition-colors disabled:opacity-50\"\n              title=\"Delete document\"\n            >\n              {isDeleting ? (\n                <div className=\"w-4 h-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent\"></div>\n              ) : (\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Delete Confirmation Modal */}\n      {showDeleteConfirm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Delete Document</h3>\n            <p className=\"text-sm text-gray-600 mb-4\">\n              Are you sure you want to delete \"{file.name}\"? This action cannot be undone.\n            </p>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={handleDeleteConfirm}\n                disabled={isDeleting}\n                className=\"flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors\"\n              >\n                {isDeleting ? 'Deleting...' : 'Delete'}\n              </button>\n              <button\n                onClick={handleDeleteCancel}\n                disabled={isDeleting}\n                className=\"flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 disabled:opacity-50 transition-colors\"\n              >\n                Cancel\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAqBe,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAiB;;IAChE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,cAAc,CAAC;QACnB,MAAM,MAAM,UAAU,WAAW;QAEjC,IAAI;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YAAO;SAAM,CAAC,QAAQ,CAAC,MAAM;YACpG,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAwB,MAAK;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAqL,UAAS;;;;;;;;;;;;;;;;QAIjO,OAAO,IAAI;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;SAAM,CAAC,QAAQ,CAAC,MAAM;YACpG,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAyB,MAAK;oBAAe,SAAQ;8BAClE,cAAA,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAsM,UAAS;;;;;;;;;;;;;;;;QAIlP,OAAO,IAAI;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;SAAM,CAAC,QAAQ,CAAC,MAAM;YAC7F,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAA0B,MAAK;oBAAe,SAAQ;8BACnE,cAAA,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAA6F,UAAS;;;;;;;;;;;;;;;;QAIzI,OAAO,IAAI,QAAQ,OAAO;YACxB,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAuB,MAAK;oBAAe,SAAQ;8BAChE,cAAA,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAsG,UAAS;;;;;;;;;;;;;;;;QAIlJ;QAEA,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAsG,UAAS;;;;;;;;;;;;;;;;IAIlJ;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;IAClC;IAEA,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC;IAC5C;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,MAAM,sBAAsB;QAC1B,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE;gBACpD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,KAAK,EAAE;YAClB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;YACd,qBAAqB;QACvB;IACF;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBACZ,YAAY,KAAK,SAAS;kCAC3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;gCAA6C,OAAO,KAAK,IAAI;0CACxE,KAAK,IAAI;;;;;;0CAEZ,6LAAC;gCAAE,WAAU;;oCACV,eAAe,KAAK,IAAI;oCAAE;oCAAI,KAAK,SAAS,CAAC,WAAW;;;;;;;0CAE3D,6LAAC;gCAAE,WAAU;;oCAA6B;oCAC7B,IAAI,KAAK,KAAK,YAAY,EAAE,kBAAkB;;;;;;;;;;;;;kCAG7D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;;0DACjE,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;0DACrE,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;oCACV,OAAM;8CAEL,2BACC,6LAAC;wCAAI,WAAU;;;;;6DAEf,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAShF,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;;gCAA6B;gCACN,KAAK,IAAI;gCAAC;;;;;;;sCAE9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,aAAa,gBAAgB;;;;;;8CAEhC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAlLwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/components/FileList.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport FileCard from './FileCard';\n\ninterface FileInfo {\n  id: string;\n  name: string;\n  size: number;\n  type: string;\n  extension: string;\n  uploadDate: string;\n  lastModified: string;\n  url: string;\n}\n\ninterface FileListProps {\n  onRefresh?: () => void;\n  refreshTrigger?: number;\n}\n\nexport default function FileList({ onRefresh, refreshTrigger }: FileListProps) {\n  const [files, setFiles] = useState<FileInfo[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('date');\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');\n  const [filterType, setFilterType] = useState<'all' | 'word' | 'excel' | 'powerpoint' | 'pdf'>('all');\n\n  const fetchFiles = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/upload');\n      const result = await response.json();\n\n      if (result.success) {\n        setFiles(result.files);\n      } else {\n        setError(result.error || 'Failed to load files');\n      }\n    } catch (err) {\n      setError('Failed to load files');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchFiles();\n  }, [refreshTrigger]);\n\n  const handleFileDelete = (fileId: string) => {\n    setFiles(files.filter(file => file.id !== fileId));\n    onRefresh?.();\n  };\n\n  const getFilteredFiles = () => {\n    let filtered = files;\n\n    // Apply type filter\n    if (filterType !== 'all') {\n      filtered = files.filter(file => {\n        const ext = file.extension.toLowerCase();\n        switch (filterType) {\n          case 'word':\n            return ['doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt'].includes(ext);\n          case 'excel':\n            return ['xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv'].includes(ext);\n          case 'powerpoint':\n            return ['ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp'].includes(ext);\n          case 'pdf':\n            return ext === 'pdf';\n          default:\n            return true;\n        }\n      });\n    }\n\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let comparison = 0;\n      \n      switch (sortBy) {\n        case 'name':\n          comparison = a.name.localeCompare(b.name);\n          break;\n        case 'date':\n          comparison = new Date(a.lastModified).getTime() - new Date(b.lastModified).getTime();\n          break;\n        case 'size':\n          comparison = a.size - b.size;\n          break;\n      }\n\n      return sortOrder === 'asc' ? comparison : -comparison;\n    });\n\n    return filtered;\n  };\n\n  const handleSort = (newSortBy: 'name' | 'date' | 'size') => {\n    if (sortBy === newSortBy) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(newSortBy);\n      setSortOrder('desc');\n    }\n  };\n\n  const filteredFiles = getFilteredFiles();\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading documents...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-red-600 mb-4\">\n          <svg className=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Error Loading Documents</h3>\n        <p className=\"text-gray-600 mb-4\">{error}</p>\n        <button\n          onClick={fetchFiles}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n        >\n          Try Again\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Controls */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2\">\n            <label className=\"text-sm font-medium text-gray-700\">Filter:</label>\n            <select\n              value={filterType}\n              onChange={(e) => setFilterType(e.target.value as any)}\n              className=\"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"all\">All Documents</option>\n              <option value=\"word\">Word Documents</option>\n              <option value=\"excel\">Excel Spreadsheets</option>\n              <option value=\"powerpoint\">PowerPoint Presentations</option>\n              <option value=\"pdf\">PDF Files</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          <label className=\"text-sm font-medium text-gray-700\">Sort by:</label>\n          <button\n            onClick={() => handleSort('name')}\n            className={`px-3 py-1 text-sm rounded-md transition-colors ${\n              sortBy === 'name' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'\n            }`}\n          >\n            Name {sortBy === 'name' && (sortOrder === 'asc' ? '↑' : '↓')}\n          </button>\n          <button\n            onClick={() => handleSort('date')}\n            className={`px-3 py-1 text-sm rounded-md transition-colors ${\n              sortBy === 'date' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'\n            }`}\n          >\n            Date {sortBy === 'date' && (sortOrder === 'asc' ? '↑' : '↓')}\n          </button>\n          <button\n            onClick={() => handleSort('size')}\n            className={`px-3 py-1 text-sm rounded-md transition-colors ${\n              sortBy === 'size' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'\n            }`}\n          >\n            Size {sortBy === 'size' && (sortOrder === 'asc' ? '↑' : '↓')}\n          </button>\n        </div>\n      </div>\n\n      {/* File Grid */}\n      {filteredFiles.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <svg className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n          </svg>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Documents Found</h3>\n          <p className=\"text-gray-600\">\n            {filterType === 'all' \n              ? 'Upload your first document to get started.' \n              : `No ${filterType} documents found. Try changing the filter or upload a new document.`\n            }\n          </p>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n          {filteredFiles.map((file) => (\n            <FileCard\n              key={file.id}\n              file={file}\n              onDelete={handleFileDelete}\n            />\n          ))}\n        </div>\n      )}\n\n      {/* File Count */}\n      {filteredFiles.length > 0 && (\n        <div className=\"text-center text-sm text-gray-500 pt-4\">\n          Showing {filteredFiles.length} of {files.length} documents\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAqBe,SAAS,SAAS,EAAE,SAAS,EAAE,cAAc,EAAiB;;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmD;IAE9F,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,OAAO,KAAK;YACvB,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG;QAAC;KAAe;IAEnB,MAAM,mBAAmB,CAAC;QACxB,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW;QAEf,oBAAoB;QACpB,IAAI,eAAe,OAAO;YACxB,WAAW,MAAM,MAAM,CAAC,CAAA;gBACtB,MAAM,MAAM,KAAK,SAAS,CAAC,WAAW;gBACtC,OAAQ;oBACN,KAAK;wBACH,OAAO;4BAAC;4BAAO;4BAAQ;4BAAQ;4BAAO;4BAAQ;4BAAQ;4BAAO;4BAAQ;4BAAO;4BAAO;yBAAM,CAAC,QAAQ,CAAC;oBACrG,KAAK;wBACH,OAAO;4BAAC;4BAAO;4BAAQ;4BAAQ;4BAAO;4BAAQ;4BAAQ;4BAAO;4BAAQ;4BAAO;yBAAM,CAAC,QAAQ,CAAC;oBAC9F,KAAK;wBACH,OAAO;4BAAC;4BAAO;4BAAQ;4BAAQ;4BAAO;4BAAQ;4BAAQ;4BAAO;4BAAQ;yBAAM,CAAC,QAAQ,CAAC;oBACvF,KAAK;wBACH,OAAO,QAAQ;oBACjB;wBACE,OAAO;gBACX;YACF;QACF;QAEA,gBAAgB;QAChB,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,IAAI,aAAa;YAEjB,OAAQ;gBACN,KAAK;oBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;oBACxC;gBACF,KAAK;oBACH,aAAa,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO;oBAClF;gBACF,KAAK;oBACH,aAAa,EAAE,IAAI,GAAG,EAAE,IAAI;oBAC5B;YACJ;YAEA,OAAO,cAAc,QAAQ,aAAa,CAAC;QAC7C;QAEA,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,WAAW;YACxB,aAAa,cAAc,QAAQ,SAAS;QAC9C,OAAO;YACL,UAAU;YACV,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;IAEtB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAoB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC3E,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAoC;;;;;;8CACrD,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAAoC;;;;;;0CACrD,6LAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAW,CAAC,+CAA+C,EACzD,WAAW,SAAS,8BAA8B,mCAClD;;oCACH;oCACO,WAAW,UAAU,CAAC,cAAc,QAAQ,MAAM,GAAG;;;;;;;0CAE7D,6LAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAW,CAAC,+CAA+C,EACzD,WAAW,SAAS,8BAA8B,mCAClD;;oCACH;oCACO,WAAW,UAAU,CAAC,cAAc,QAAQ,MAAM,GAAG;;;;;;;0CAE7D,6LAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAW,CAAC,+CAA+C,EACzD,WAAW,SAAS,8BAA8B,mCAClD;;oCACH;oCACO,WAAW,UAAU,CAAC,cAAc,QAAQ,MAAM,GAAG;;;;;;;;;;;;;;;;;;;YAMhE,cAAc,MAAM,KAAK,kBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAAuC,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC9F,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,eAAe,QACZ,+CACA,CAAC,GAAG,EAAE,WAAW,mEAAmE,CAAC;;;;;;;;;;;qCAK7F,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,iIAAA,CAAA,UAAQ;wBAEP,MAAM;wBACN,UAAU;uBAFL,KAAK,EAAE;;;;;;;;;;YASnB,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAU;;oBAAyC;oBAC7C,cAAc,MAAM;oBAAC;oBAAK,MAAM,MAAM;oBAAC;;;;;;;;;;;;;AAK1D;GA/MwB;KAAA", "debugId": null}}, {"offset": {"line": 1164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ReactNode } from 'react';\nimport { ErrorBoundaryState } from '@/types/onlyoffice';\n\ninterface ErrorBoundaryProps {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: any) => void;\n}\n\nclass ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    this.setState({\n      hasError: true,\n      error,\n      errorInfo,\n    });\n\n    // Call the onError callback if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n  }\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default error UI\n      return (\n        <div className=\"min-h-[400px] flex items-center justify-center bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"text-center p-6 max-w-md\">\n            <div className=\"text-red-600 mb-4\">\n              <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h2 className=\"text-xl font-semibold text-red-800 mb-2\">Something went wrong</h2>\n            <p className=\"text-red-600 mb-4\">\n              {this.state.error?.message || 'An unexpected error occurred'}\n            </p>\n            <div className=\"space-y-2\">\n              <button\n                onClick={this.handleReset}\n                className=\"w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\n              >\n                Try Again\n              </button>\n              <button\n                onClick={() => window.location.reload()}\n                className=\"w-full px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors\"\n              >\n                Reload Page\n              </button>\n            </div>\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"mt-4 text-left\">\n                <summary className=\"cursor-pointer text-sm text-red-700 hover:text-red-800\">\n                  Error Details (Development)\n                </summary>\n                <pre className=\"mt-2 text-xs text-red-600 bg-red-100 p-2 rounded overflow-auto max-h-32\">\n                  {this.state.error.stack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;AA6Ea;;AA3Eb;AAFA;;;AAWA,MAAM,sBAAsB,6JAAA,CAAA,YAAS;IACnC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAc,EAAE;QAC9C,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,IAAI,CAAC,QAAQ,CAAC;YACZ,UAAU;YACV;YACA;QACF;QAEA,wCAAwC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IACF;IAEA,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;YAAW,WAAW;QAAU;IAC1E,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBAAqB;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,mBAAmB;YACnB,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC3E,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;4BAAG,WAAU;sCAA0C;;;;;;sCACxD,6LAAC;4BAAE,WAAU;sCACV,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW;;;;;;sCAEhC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAI,CAAC,WAAW;oCACzB,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;;wBAIF,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAQ,WAAU;8CAAyD;;;;;;8CAG5E,6LAAC;oCAAI,WAAU;8CACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;QAOrC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport FileUpload from '@/components/FileUpload';\nimport FileList from '@/components/FileList';\nimport ErrorBoundary from '@/components/ErrorBoundary';\nimport { FileMetadata } from '@/types/onlyoffice';\n\nexport default function Home() {\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n  const [showUpload, setShowUpload] = useState(false);\n  const [notification, setNotification] = useState<{\n    type: 'success' | 'error';\n    message: string;\n  } | null>(null);\n\n  const handleUploadSuccess = (file: FileMetadata) => {\n    setNotification({\n      type: 'success',\n      message: `Successfully uploaded \"${file.name}\"`,\n    });\n    setRefreshTrigger(prev => prev + 1);\n    setShowUpload(false);\n\n    // Clear notification after 5 seconds\n    setTimeout(() => setNotification(null), 5000);\n  };\n\n  const handleUploadError = (error: string) => {\n    setNotification({\n      type: 'error',\n      message: error,\n    });\n\n    // Clear notification after 5 seconds\n    setTimeout(() => setNotification(null), 5000);\n  };\n\n  const handleRefresh = () => {\n    setRefreshTrigger(prev => prev + 1);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">OnlyOffice Demo</h1>\n              </div>\n              <div className=\"hidden md:block ml-6\">\n                <p className=\"text-sm text-gray-600\">\n                  Collaborative document editing with OnlyOffice Document Server\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={handleRefresh}\n                className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                </svg>\n                Refresh\n              </button>\n              <button\n                onClick={() => setShowUpload(!showUpload)}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                </svg>\n                Upload Document\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Notification */}\n      {notification && (\n        <div className={`fixed top-4 right-4 z-50 max-w-sm w-full ${\n          notification.type === 'success' ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700'\n        } border rounded-md p-4 shadow-lg`}>\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              {notification.type === 'success' ? (\n                <svg className=\"h-5 w-5 text-green-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n              ) : (\n                <svg className=\"h-5 w-5 text-red-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              )}\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium\">{notification.message}</p>\n            </div>\n            <div className=\"ml-auto pl-3\">\n              <button\n                onClick={() => setNotification(null)}\n                className=\"inline-flex text-gray-400 hover:text-gray-600\"\n              >\n                <svg className=\"h-4 w-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Upload Section */}\n        {showUpload && (\n          <div className=\"mb-8\">\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-lg font-medium text-gray-900\">Upload New Document</h2>\n                <button\n                  onClick={() => setShowUpload(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <ErrorBoundary>\n                <FileUpload\n                  onUploadSuccess={handleUploadSuccess}\n                  onUploadError={handleUploadError}\n                />\n              </ErrorBoundary>\n            </div>\n          </div>\n        )}\n\n        {/* Documents Section */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-lg font-medium text-gray-900\">Your Documents</h2>\n            <div className=\"text-sm text-gray-500\">\n              Click on a document to edit or view it\n            </div>\n          </div>\n          <ErrorBoundary>\n            <FileList\n              onRefresh={handleRefresh}\n              refreshTrigger={refreshTrigger}\n            />\n          </ErrorBoundary>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"text-sm text-gray-600\">\n              <p>OnlyOffice Document Server Demo - Built with Next.js</p>\n            </div>\n            <div className=\"mt-4 md:mt-0 flex space-x-6\">\n              <a\n                href=\"https://www.onlyoffice.com/\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-sm text-gray-600 hover:text-gray-900\"\n              >\n                OnlyOffice\n              </a>\n              <a\n                href=\"https://nextjs.org/\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-sm text-gray-600 hover:text-gray-900\"\n              >\n                Next.js\n              </a>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGrC;IAEV,MAAM,sBAAsB,CAAC;QAC3B,gBAAgB;YACd,MAAM;YACN,SAAS,CAAC,uBAAuB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;QACjD;QACA,kBAAkB,CAAA,OAAQ,OAAO;QACjC,cAAc;QAEd,qCAAqC;QACrC,WAAW,IAAM,gBAAgB,OAAO;IAC1C;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;YACd,MAAM;YACN,SAAS;QACX;QAEA,qCAAqC;QACrC,WAAW,IAAM,gBAAgB,OAAO;IAC1C;IAEA,MAAM,gBAAgB;QACpB,kBAAkB,CAAA,OAAQ,OAAO;IACnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;kDAEnD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAKzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASf,8BACC,6LAAC;gBAAI,WAAW,CAAC,yCAAyC,EACxD,aAAa,IAAI,KAAK,YAAY,iDAAiD,yCACpF,gCAAgC,CAAC;0BAChC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,aAAa,IAAI,KAAK,0BACrB,6LAAC;gCAAI,WAAU;gCAAyB,MAAK;gCAAe,SAAQ;0CAClE,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAwI,UAAS;;;;;;;;;;qDAG9K,6LAAC;gCAAI,WAAU;gCAAuB,MAAK;gCAAe,SAAQ;0CAChE,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;;;;;;sCAIpQ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAuB,aAAa,OAAO;;;;;;;;;;;sCAE1D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;8CACnD,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrP,6LAAC;gBAAK,WAAU;;oBAEb,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAI3E,6LAAC,sIAAA,CAAA,UAAa;8CACZ,cAAA,6LAAC,mIAAA,CAAA,UAAU;wCACT,iBAAiB;wCACjB,eAAe;;;;;;;;;;;;;;;;;;;;;;kCAQzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAIzC,6LAAC,sIAAA,CAAA,UAAa;0CACZ,cAAA,6LAAC,iIAAA,CAAA,UAAQ;oCACP,WAAW;oCACX,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAOxB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAE;;;;;;;;;;;0CAEL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAtLwB;KAAA", "debugId": null}}]}