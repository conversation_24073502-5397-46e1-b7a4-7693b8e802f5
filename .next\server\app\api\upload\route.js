(()=>{var e={};e.id=413,e.ids=[413],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2833:(e,t,r)=>{"use strict";r.d(t,{i:()=>o,s:()=>a});var i=r(3205),s=r.n(i);class o{constructor(){this.rateLimitStore=new Map,this.jwtSecret=process.env.ONLYOFFICE_JWT_SECRET||"fzX5rJRaYYPtns6t"}validateOnlyOfficeToken(e){try{return s().verify(e,this.jwtSecret)}catch(e){throw Error("Invalid OnlyOffice JWT token")}}signOnlyOfficeData(e){return s().sign(e,this.jwtSecret,{algorithm:"HS256"})}extractTokenFromRequest(e){let t=e.headers.get("authorization");return t?t.replace("Bearer ",""):null}validateFileUpload(e){if(e.size>0x3200000)return{valid:!1,error:"File size exceeds 50MB limit"};if(!["application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.openxmlformats-officedocument.presentationml.presentation","application/msword","application/vnd.ms-excel","application/vnd.ms-powerpoint","application/pdf","text/plain","text/csv","application/vnd.oasis.opendocument.text","application/vnd.oasis.opendocument.spreadsheet","application/vnd.oasis.opendocument.presentation"].includes(e.type)&&""!==e.type){let t=e.name.split(".").pop()?.toLowerCase();if(!t||!["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt","xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv","ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp","pdf"].includes(t))return{valid:!1,error:"File type not supported"}}return this.containsSuspiciousPatterns(e.name)?{valid:!1,error:"Filename contains invalid characters"}:{valid:!0}}containsSuspiciousPatterns(e){return[/\.\./,/[<>:"|?*]/,/^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,/^\./,/\.(exe|bat|cmd|scr|vbs|js|jar|com|pif)$/i].some(t=>t.test(e))}sanitizeFilename(e){return e.replace(/[^a-zA-Z0-9._-]/g,"_").replace(/_{2,}/g,"_").replace(/^_+|_+$/g,"").substring(0,255)}generateSecureDocumentKey(e,t){let r=`${e}_${t}_${this.jwtSecret}`;return Buffer.from(r).toString("base64").replace(/[^a-zA-Z0-9]/g,"").substring(0,20)}validateOrigin(e){let t=e.headers.get("origin"),r=e.headers.get("referer"),i=["https://only.34sy.org","http://localhost:3000","http://localhost:3001","http://127.0.0.1:3000","http://127.0.0.1:3001","http://************:3001"].filter(Boolean);if(!t&&!r||t&&i.includes(t))return!0;if(r)try{let e=new URL(r),t=`${e.protocol}//${e.host}`;return i.includes(t)}catch{return!1}return!0}checkRateLimit(e,t=100,r=6e4){let i=Date.now(),s=this.rateLimitStore.get(e);return!s||i>s.resetTime?(this.rateLimitStore.set(e,{count:1,resetTime:i+r}),!0):!(s.count>=t)&&(s.count++,!0)}cleanupRateLimit(){let e=Date.now();for(let[t,r]of this.rateLimitStore.entries())e>r.resetTime&&this.rateLimitStore.delete(t)}}let a=new o},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},6751:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{GET:()=>d,POST:()=>u});var s=r(6559),o=r(8088),a=r(7719),n=r(2190),l=r(8556),p=r(2833);async function u(e){try{if(!p.s.validateOrigin(e))return n.NextResponse.json({error:"Invalid origin"},{status:403});let t=e.headers.get("x-forwarded-for")||"unknown";if(!p.s.checkRateLimit(t,10,6e4))return n.NextResponse.json({error:"Rate limit exceeded. Please try again later."},{status:429});let r=(await e.formData()).get("file");if(!r)return n.NextResponse.json({error:"No file provided"},{status:400});let i=p.s.validateFileUpload(r);if(!i.valid)return n.NextResponse.json({error:i.error},{status:400});let s=new l.a;if(!s.isValidFileType(r.name))return n.NextResponse.json({error:"Invalid file type. Please upload a supported document format."},{status:400});let o=new File([r],p.s.sanitizeFilename(r.name),{type:r.type,lastModified:r.lastModified}),a=await s.saveFile(o);return n.NextResponse.json({success:!0,file:a})}catch(e){return console.error("Upload error:",e),n.NextResponse.json({error:"Failed to upload file"},{status:500})}}async function d(){try{let e=new l.a().getAllFiles();return n.NextResponse.json({success:!0,files:e})}catch(e){return console.error("Get files error:",e),n.NextResponse.json({error:"Failed to get files"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:x}=c;function h(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},8556:(e,t,r)=>{"use strict";r.d(t,{a:()=>l});var i=r(9021),s=r.n(i),o=r(3873),a=r.n(o),n=r(3870);class l{constructor(){this.uploadDir=a().join(process.cwd(),"public","uploads"),this.ensureUploadDir()}ensureUploadDir(){s().existsSync(this.uploadDir)||s().mkdirSync(this.uploadDir,{recursive:!0})}async saveFile(e){let t=(0,n.A)(),r=a().extname(e.name),i=`${t}${r}`,o=a().join(this.uploadDir,i),l=await e.arrayBuffer(),p=Buffer.from(l);s().writeFileSync(o,p);let u={id:t,name:e.name||`document${r}`,size:e.size,type:e.type,extension:r.slice(1)||"unknown",uploadDate:new Date,lastModified:new Date,url:`/uploads/${i}`};return this.saveFileMetadata(u),u}saveFileMetadata(e){let t=a().join(this.uploadDir,`${e.id}.json`);s().writeFileSync(t,JSON.stringify(e,null,2))}getFileMetadata(e){try{let t=a().join(this.uploadDir,`${e}.json`);if(!s().existsSync(t))return null;let r=s().readFileSync(t,"utf-8");return JSON.parse(r)}catch(e){return null}}getAllFiles(){try{return s().readdirSync(this.uploadDir).filter(e=>e.endsWith(".json")).map(e=>{let t=s().readFileSync(a().join(this.uploadDir,e),"utf-8");return JSON.parse(t)}).sort((e,t)=>new Date(t.uploadDate).getTime()-new Date(e.uploadDate).getTime())}catch(e){return[]}}deleteFile(e){try{let t=this.getFileMetadata(e);if(!t)return!1;let r=a().extname(t.name),i=`${e}${r}`,o=a().join(this.uploadDir,i),n=a().join(this.uploadDir,`${e}.json`);return s().existsSync(o)&&s().unlinkSync(o),s().existsSync(n)&&s().unlinkSync(n),!0}catch(e){return!1}}getFileUrl(e){let t=this.getFileMetadata(e);return t?`http://************:3001${t.url}`:null}isValidFileType(e){return["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt","xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv","ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp","pdf"].includes(a().extname(e).slice(1).toLowerCase())}}},9021:e=>{"use strict";e.exports=require("fs")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,580,26],()=>r(6751));module.exports=i})();