import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const jwtSecret = process.env.ONLYOFFICE_JWT_SECRET;
    const serverUrl = process.env.ONLYOFFICE_SERVER_URL;
    
    return NextResponse.json({
      success: true,
      jwtSecret: jwtSecret ? `${jwtSecret.substring(0, 3)}***` : 'NOT SET',
      jwtSecretLength: jwtSecret?.length || 0,
      serverUrl,
      hasJwtSecret: !!jwtSecret,
      environment: {
        ONLYOFFICE_JWT_SECRET: process.env.ONLYOFFICE_JWT_SECRET ? 'SET' : 'NOT SET',
        ONLYOFFICE_SERVER_URL: process.env.ONLYOFFICE_SERVER_URL || 'NOT SET'
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
