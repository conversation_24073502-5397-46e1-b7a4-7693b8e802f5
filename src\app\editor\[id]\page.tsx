'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import OnlyOfficeEditor from '@/components/OnlyOfficeEditor';
import ErrorBoundary from '@/components/ErrorBoundary';
import { PageLoader } from '@/components/LoadingSpinner';
import { FileMetadata } from '@/types/onlyoffice';



export default function EditorPage() {
  const params = useParams();
  const router = useRouter();
  const fileId = params.id as string;
  
  const [fileInfo, setFileInfo] = useState<FileMetadata | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFileInfo = async () => {
      try {
        const response = await fetch(`/api/files/${fileId}`);
        const result = await response.json();

        if (result.success) {
          setFileInfo(result.file);
        } else {
          setError(result.error || 'File not found');
        }
      } catch (err) {
        setError('Failed to load file information');
      } finally {
        setLoading(false);
      }
    };

    if (fileId) {
      fetchFileInfo();
    }
  }, [fileId]);

  const handleEditorError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleDocumentReady = () => {
    console.log('Document is ready for editing');
  };

  const handleGoBack = () => {
    router.push('/');
  };

  if (loading) {
    return <PageLoader text="Loading document..." />;
  }

  if (error || !fileInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-6">
          <div className="text-red-600 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Document Not Found</h1>
          <p className="text-gray-600 mb-6">{error || 'The requested document could not be found.'}</p>
          <button
            onClick={handleGoBack}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Documents
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleGoBack}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Documents
            </button>
            <div className="h-6 border-l border-gray-300"></div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">{fileInfo.name}</h1>
              <p className="text-sm text-gray-500">
                Last modified: {new Date(fileInfo.lastModified).toLocaleString()}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {fileInfo.extension.toUpperCase()}
            </span>
            <span className="text-sm text-gray-500">
              {(fileInfo.size / 1024 / 1024).toFixed(2)} MB
            </span>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="h-[calc(100vh-80px)]">
        <ErrorBoundary
          onError={(error, errorInfo) => {
            console.error('Editor Error Boundary:', error, errorInfo);
            setError(`Editor Error: ${error.message}`);
          }}
        >
          <OnlyOfficeEditor
            fileId={fileId}
            mode="edit"
            userId="user1"
            userName="Demo User"
            onError={handleEditorError}
            onDocumentReady={handleDocumentReady}
          />
        </ErrorBoundary>
      </div>
    </div>
  );
}
