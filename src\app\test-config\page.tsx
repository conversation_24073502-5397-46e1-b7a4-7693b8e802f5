'use client';

import React, { useState } from 'react';

export default function TestConfigPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testConfig = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/onlyoffice/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileId: '2d8319a9-57c1-4758-af8f-6a7dbe0d612c',
          mode: 'edit',
          userId: 'test-user',
          userName: 'Test User'
        }),
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(data);
        console.log('Config response:', data);
      } else {
        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Network error');
    } finally {
      setLoading(false);
    }
  };

  const testJwtEnabled = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/test-jwt-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileId: '2d8319a9-57c1-4758-af8f-6a7dbe0d612c',
          mode: 'edit',
          userId: 'test-user',
          userName: 'Test User'
        }),
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(data);
        console.log('JWT-enabled response:', data);
      } else {
        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Network error');
    } finally {
      setLoading(false);
    }
  };

  const testJwtDisabled = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/test-jwt-disabled', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileId: '2d8319a9-57c1-4758-af8f-6a7dbe0d612c',
          mode: 'edit',
          userId: 'test-user',
          userName: 'Test User'
        }),
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(data);
        console.log('JWT-disabled response:', data);
      } else {
        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Network error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">OnlyOffice Config Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Configuration Endpoints</h2>
          
          <div className="space-y-4">
            <div>
              <button
                onClick={testConfig}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 mr-4"
              >
                {loading ? 'Testing...' : 'Test Regular Config'}
              </button>
              
              <button
                onClick={testJwtEnabled}
                disabled={loading}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 mr-4"
              >
                {loading ? 'Testing...' : 'Test JWT-Enabled Config'}
              </button>
              
              <button
                onClick={testJwtDisabled}
                disabled={loading}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                {loading ? 'Testing...' : 'Test JWT-Disabled Config'}
              </button>
            </div>

            <div className="text-sm text-gray-600">
              <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'Loading...'}</p>
              <p><strong>Test File ID:</strong> 2d8319a9-57c1-4758-af8f-6a7dbe0d612c</p>
              <p><strong>Expected JWT Secret:</strong> fzX5rJRaYYPtns6t</p>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {result && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-green-800 mb-2">Success</h3>
            <div className="space-y-2">
              <p><strong>Success:</strong> {result.success ? 'Yes' : 'No'}</p>
              <p><strong>JWT Enabled:</strong> {result.jwtEnabled ? 'Yes' : 'No'}</p>
              <p><strong>Server URL:</strong> {result.serverUrl}</p>
              <p><strong>Message:</strong> {result.message}</p>
              {result.debug && (
                <>
                  <p><strong>JWT Secret:</strong> {result.debug.jwtSecret}</p>
                  <p><strong>Token Generated:</strong> {result.debug.tokenGenerated ? 'Yes' : 'No'}</p>
                  <p><strong>Token Length:</strong> {result.debug.tokenLength}</p>
                </>
              )}
              {result.config && (
                <>
                  <p><strong>Document Key:</strong> {result.config.document?.key}</p>
                  <p><strong>Document URL:</strong> {result.config.document?.url}</p>
                  <p><strong>Callback URL:</strong> {result.config.editorConfig?.callbackUrl}</p>
                </>
              )}
            </div>
          </div>
        )}

        {result && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Full Response</h3>
            <pre className="text-sm text-gray-600 overflow-x-auto whitespace-pre-wrap">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
} 