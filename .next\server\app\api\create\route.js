(()=>{var e={};e.id=18,e.ids=[18],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2359:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>f,serverHooks:()=>v,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var i={};r.r(i),r.d(i,{POST:()=>x});var s=r(6559),n=r(8088),o=r(7719),a=r(2190),l=r(8556),p=r(2833),c=r(9021),u=r.n(c),d=r(3873),m=r.n(d);async function x(e){try{let{fileType:t,fileName:r}=await e.json();if(!t||!r)return a.NextResponse.json({error:"File type and name are required"},{status:400});if(!["excel","word","powerpoint"].includes(t))return a.NextResponse.json({error:"Invalid file type. Supported types: excel, word, powerpoint"},{status:400});let i=new p.i().sanitizeFilename(r),s=function(e){switch(e){case"excel":return{templateFile:"template.xlsx",extension:".xlsx",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"};case"word":return{templateFile:"template.docx",extension:".docx",mimeType:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"};case"powerpoint":return{templateFile:"template.pptx",extension:".pptx",mimeType:"application/vnd.openxmlformats-officedocument.presentationml.presentation"};default:throw Error("Invalid file type")}}(t),n=m().join(process.cwd(),"templates",s.templateFile);if(!u().existsSync(n))return a.NextResponse.json({error:"Template file not found"},{status:500});let o=u().readFileSync(n),c=`${i}${s.extension}`,d=new File([o],c,{type:s.mimeType,lastModified:Date.now()}),x=new l.a,f=await x.saveFile(d);return a.NextResponse.json({success:!0,file:f})}catch(e){return console.error("Create file error:",e),a.NextResponse.json({error:"Failed to create file"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/create/route",pathname:"/api/create",filename:"route",bundlePath:"app/api/create/route"},resolvedPagePath:"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\create\\route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:v}=f;function w(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},2833:(e,t,r)=>{"use strict";r.d(t,{i:()=>n,s:()=>o});var i=r(3205),s=r.n(i);class n{constructor(){this.rateLimitStore=new Map,this.jwtSecret=process.env.ONLYOFFICE_JWT_SECRET||"fzX5rJRaYYPtns6t"}validateOnlyOfficeToken(e){try{return s().verify(e,this.jwtSecret)}catch(e){throw Error("Invalid OnlyOffice JWT token")}}signOnlyOfficeData(e){return s().sign(e,this.jwtSecret,{algorithm:"HS256"})}extractTokenFromRequest(e){let t=e.headers.get("authorization");return t?t.replace("Bearer ",""):null}validateFileUpload(e){if(e.size>0x3200000)return{valid:!1,error:"File size exceeds 50MB limit"};if(!["application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.openxmlformats-officedocument.presentationml.presentation","application/msword","application/vnd.ms-excel","application/vnd.ms-powerpoint","application/pdf","text/plain","text/csv","application/vnd.oasis.opendocument.text","application/vnd.oasis.opendocument.spreadsheet","application/vnd.oasis.opendocument.presentation"].includes(e.type)&&""!==e.type){let t=e.name.split(".").pop()?.toLowerCase();if(!t||!["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt","xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv","ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp","pdf"].includes(t))return{valid:!1,error:"File type not supported"}}return this.containsSuspiciousPatterns(e.name)?{valid:!1,error:"Filename contains invalid characters"}:{valid:!0}}containsSuspiciousPatterns(e){return[/\.\./,/[<>:"|?*]/,/^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,/^\./,/\.(exe|bat|cmd|scr|vbs|js|jar|com|pif)$/i].some(t=>t.test(e))}sanitizeFilename(e){return e.replace(/[^a-zA-Z0-9._-]/g,"_").replace(/_{2,}/g,"_").replace(/^_+|_+$/g,"").substring(0,255)}generateSecureDocumentKey(e,t){let r=`${e}_${t}_${this.jwtSecret}`;return Buffer.from(r).toString("base64").replace(/[^a-zA-Z0-9]/g,"").substring(0,20)}validateOrigin(e){let t=e.headers.get("origin"),r=e.headers.get("referer"),i=["https://only.34sy.org","http://localhost:3000","http://localhost:3001","http://127.0.0.1:3000","http://127.0.0.1:3001","http://************:3001"].filter(Boolean);if(!t&&!r||t&&i.includes(t))return!0;if(r)try{let e=new URL(r),t=`${e.protocol}//${e.host}`;return i.includes(t)}catch{return!1}return!0}checkRateLimit(e,t=100,r=6e4){let i=Date.now(),s=this.rateLimitStore.get(e);return!s||i>s.resetTime?(this.rateLimitStore.set(e,{count:1,resetTime:i+r}),!0):!(s.count>=t)&&(s.count++,!0)}cleanupRateLimit(){let e=Date.now();for(let[t,r]of this.rateLimitStore.entries())e>r.resetTime&&this.rateLimitStore.delete(t)}}let o=new n},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},8556:(e,t,r)=>{"use strict";r.d(t,{a:()=>l});var i=r(9021),s=r.n(i),n=r(3873),o=r.n(n),a=r(3870);class l{constructor(){this.uploadDir=o().join(process.cwd(),"public","uploads"),this.ensureUploadDir()}ensureUploadDir(){s().existsSync(this.uploadDir)||s().mkdirSync(this.uploadDir,{recursive:!0})}async saveFile(e){let t=(0,a.A)(),r=o().extname(e.name),i=`${t}${r}`,n=o().join(this.uploadDir,i),l=await e.arrayBuffer(),p=Buffer.from(l);s().writeFileSync(n,p);let c={id:t,name:e.name||`document${r}`,size:e.size,type:e.type,extension:r.slice(1)||"unknown",uploadDate:new Date,lastModified:new Date,url:`/uploads/${i}`};return this.saveFileMetadata(c),c}saveFileMetadata(e){let t=o().join(this.uploadDir,`${e.id}.json`);s().writeFileSync(t,JSON.stringify(e,null,2))}getFileMetadata(e){try{let t=o().join(this.uploadDir,`${e}.json`);if(!s().existsSync(t))return null;let r=s().readFileSync(t,"utf-8");return JSON.parse(r)}catch(e){return null}}getAllFiles(){try{return s().readdirSync(this.uploadDir).filter(e=>e.endsWith(".json")).map(e=>{let t=s().readFileSync(o().join(this.uploadDir,e),"utf-8");return JSON.parse(t)}).sort((e,t)=>new Date(t.uploadDate).getTime()-new Date(e.uploadDate).getTime())}catch(e){return[]}}deleteFile(e){try{let t=this.getFileMetadata(e);if(!t)return!1;let r=o().extname(t.name),i=`${e}${r}`,n=o().join(this.uploadDir,i),a=o().join(this.uploadDir,`${e}.json`);return s().existsSync(n)&&s().unlinkSync(n),s().existsSync(a)&&s().unlinkSync(a),!0}catch(e){return!1}}getFileUrl(e){let t=this.getFileMetadata(e);return t?`http://************:3001${t.url}`:null}isValidFileType(e){return["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt","xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv","ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp","pdf"].includes(o().extname(e).slice(1).toLowerCase())}}},9021:e=>{"use strict";e.exports=require("fs")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,580,26],()=>r(2359));module.exports=i})();