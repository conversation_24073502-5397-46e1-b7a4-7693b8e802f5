{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/editor(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/editor/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/uploads(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/uploads/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FHbgEg/LvL83bAMQlaTJWLma3/iCdTl+r2H1vdHGiqc=", "__NEXT_PREVIEW_MODE_ID": "f2dcb225f427f1867fc46ed9e8663c9b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "287240242b4449bb81b90c08d09875c57c50298e04afac9163c8b8d1c82db8af", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "85a3fa46e2f826dd303d3eca883f116b36b771e09c342927d8f38c0c4f06d219"}}}, "sortedMiddleware": ["/"], "functions": {}}