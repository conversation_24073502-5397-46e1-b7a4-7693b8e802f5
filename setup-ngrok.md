# Setting Up Public URL for OnlyOffice Integration

## The Problem
OnlyOffice Document Server at `only.34sy.org` cannot access files from `localhost:3001` because localhost is not publicly accessible from external servers.

## Solution: Use ngrok for Public URL

### Step 1: Install ngrok
```bash
npm install -g ngrok
```

### Step 2: Start your Next.js application
```bash
npm run dev
```
(This should start on port 3001)

### Step 3: In a new terminal, start ngrok
```bash
ngrok http 3001
```

### Step 4: Copy the public URL
ngrok will display something like:
```
Forwarding    https://abc123.ngrok.io -> http://localhost:3001
```

### Step 5: Update environment variable
Update `.env.local`:
```env
NEXT_PUBLIC_APP_URL=https://abc123.ngrok.io
```

### Step 6: Restart your Next.js application
```bash
# Stop the dev server (Ctrl+C) and restart
npm run dev
```

## Alternative Solutions

### Option 1: Use a Cloud Service
Deploy your application to:
- Vercel
- Netlify
- Railway
- Heroku

### Option 2: Use a VPS/Server
Deploy to a server with a public IP address.

### Option 3: Use LocalTunnel (Alternative to ngrok)
```bash
npm install -g localtunnel
lt --port 3001
```

## Testing the Setup

1. After setting up the public URL, test the file serving endpoint:
   ```bash
   curl -I https://your-ngrok-url.ngrok.io/api/files/serve/be41a87e-55be-4188-8aa9-cfdbd4f58e49
   ```

2. You should see headers like:
   ```
   HTTP/1.1 200 OK
   Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
   Access-Control-Allow-Origin: *
   ```

3. Try opening the document editor again:
   ```
   https://your-ngrok-url.ngrok.io/editor/be41a87e-55be-4188-8aa9-cfdbd4f58e49
   ```

## Important Notes

- **ngrok URLs change** every time you restart ngrok (unless you have a paid account)
- **Update the environment variable** each time you get a new ngrok URL
- **Restart the Next.js server** after updating the environment variable
- **Keep ngrok running** while testing the OnlyOffice integration

## Security Considerations

- ngrok exposes your local server to the internet
- Only use for development/testing
- For production, use proper hosting with HTTPS
- Consider using ngrok's authentication features for added security
