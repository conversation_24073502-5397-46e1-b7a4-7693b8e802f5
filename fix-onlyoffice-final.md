# Final OnlyOffice Docker-JWT Configuration Fix

## Current Issues Identified

From your logs, these issues need to be fixed:

1. **JWT Token Still Disabled**: Despite having a secret, JWT signing is disabled
2. **Port Mismatch**: App runs on 3002, but URLs use 3001
3. **Environment Variables**: May not be properly loaded
4. **OnlyOffice Docker Config**: Needs to match app JWT settings

## Step-by-Step Fix

### Step 1: Stop Current Services
```bash
# Stop Next.js app (Ctrl+C in terminal)
# Stop OnlyOffice container
docker stop onlyoffice
docker rm onlyoffice
```

### Step 2: Create Environment File
Create `.env.local` in your project root:
```env
# OnlyOffice Server Configuration
ONLYOFFICE_SERVER_URL=http://localhost:8080
ONLYOFFICE_JWT_SECRET=fzX5rJRaYYPtns6t

# App URL - Use your machine's IP with correct port
NEXT_PUBLIC_APP_URL=http://*************:3002

# Development settings
NODE_ENV=development
PORT=3002

# Ensure JWT is NOT disabled
# ONLYOFFICE_JWT_DISABLED=false
```

### Step 3: Start OnlyOffice with Correct JWT Configuration
```bash
docker run -d --name onlyoffice \
  -p 8080:80 \
  -e JWT_ENABLED=true \
  -e JWT_SECRET=fzX5rJRaYYPtns6t \
  -e JWT_HEADER=Authorization \
  -e JWT_IN_BODY=true \
  onlyoffice/documentserver
```

### Step 4: Verify OnlyOffice is Running
```bash
# Check container status
docker ps | grep onlyoffice

# Check health
curl http://localhost:8080/healthcheck
```

### Step 5: Restart Next.js App
```bash
# Make sure you're in the project directory
npm run dev
```

### Step 6: Test Configuration
Visit these URLs to verify everything is working:

1. **Debug Configuration**: `http://localhost:3002/api/debug-final-config`
   - Should show status: "Configuration is correct"
   - Should show JWT enabled and token generated

2. **Test JWT Config**: `http://localhost:3002/test-config`
   - Test all three configuration types
   - JWT-enabled should show token generated

3. **Test Document Editor**: `http://localhost:3002/editor/2d8319a9-57c1-4758-af8f-6a7dbe0d612c`
   - Should load without JWT errors

## Expected Results

### Console Logs Should Show:
```
OnlyOffice Service Configuration: {
  serverUrl: 'http://localhost:8080',
  hasJwtSecret: true,
  jwtEnabled: true,
  jwtSecret: 'Using production secret'
}

Creating OnlyOffice config for: {
  fileUrl: 'http://*************:3002/api/files/serve/...',
  callbackUrl: 'http://*************:3002/api/onlyoffice/callback'
}

JWT token generated successfully, length: 200+
```

### Debug Endpoint Should Return:
```json
{
  "success": true,
  "summary": {
    "status": "Configuration is correct",
    "totalIssues": 0
  },
  "debug": {
    "jwt": {
      "hasSecret": true,
      "secretCorrect": true,
      "tokenGenerated": true
    },
    "ports": {
      "detected": "3002",
      "mismatch": false
    }
  }
}
```

## Troubleshooting

### If JWT Still Disabled:
1. Check `.env.local` file exists and has correct values
2. Restart Next.js app completely
3. Check for any `ONLYOFFICE_JWT_DISABLED=true` in environment

### If Port Still Wrong:
1. Verify `NEXT_PUBLIC_APP_URL` matches actual running port
2. Check Next.js is actually running on port 3002
3. Update IP address if needed

### If OnlyOffice Rejects JWT:
1. Verify Docker container has same JWT_SECRET
2. Check Docker logs: `docker logs onlyoffice`
3. Restart OnlyOffice container with correct environment

### If Documents Still Won't Load:
1. Check file serving endpoint works: `curl -I http://*************:3002/api/files/serve/test-id`
2. Verify OnlyOffice can reach your machine IP
3. Consider using ngrok for external access

## Commands Summary
```bash
# Stop everything
docker stop onlyoffice && docker rm onlyoffice

# Create .env.local with correct settings (see Step 2)

# Start OnlyOffice with JWT
docker run -d --name onlyoffice -p 8080:80 -e JWT_ENABLED=true -e JWT_SECRET=fzX5rJRaYYPtns6t onlyoffice/documentserver

# Start Next.js
npm run dev

# Test configuration
curl http://localhost:3002/api/debug-final-config
```

This should resolve all the JWT token and configuration issues! 