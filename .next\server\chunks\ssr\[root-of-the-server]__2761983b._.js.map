{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/test-onlyoffice/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\nexport default function TestOnlyOfficePage() {\n  const [logs, setLogs] = useState<string[]>([]);\n  const [docsAPIStatus, setDocsAPIStatus] = useState<string>('Not loaded');\n\n  const addLog = (message: string) => {\n    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);\n  };\n\n  useEffect(() => {\n    addLog('Starting OnlyOffice API test...');\n    \n    // Check if DocsAPI is already available\n    if (window.DocsAPI) {\n      addLog('DocsAPI already available on window');\n      setDocsAPIStatus('Already available');\n      return;\n    }\n\n    addLog('DocsAPI not available, loading script...');\n    \n    const script = document.createElement('script');\n    script.src = 'https://only.34sy.org/web-apps/apps/api/documents/api.js';\n    script.async = true;\n    script.crossOrigin = 'anonymous';\n    \n    script.onload = () => {\n      addLog('Script loaded successfully');\n      \n      const checkDocsAPI = (attempts = 0) => {\n        addLog(`Checking for DocsAPI, attempt ${attempts + 1}/10`);\n        addLog(`window.DocsAPI: ${typeof window.DocsAPI}`);\n        \n        if (window.DocsAPI) {\n          addLog(`DocsAPI found! Type: ${typeof window.DocsAPI}`);\n          addLog(`DocsAPI.DocEditor: ${typeof window.DocsAPI.DocEditor}`);\n          \n          if (typeof window.DocsAPI.DocEditor === 'function') {\n            addLog('DocsAPI.DocEditor is a function - SUCCESS!');\n            setDocsAPIStatus('Available and ready');\n          } else {\n            addLog('DocsAPI.DocEditor is not a function');\n            setDocsAPIStatus('Available but DocEditor missing');\n          }\n        } else if (attempts < 10) {\n          addLog(`DocsAPI not yet available, retrying in 100ms...`);\n          setTimeout(() => checkDocsAPI(attempts + 1), 100);\n        } else {\n          addLog('DocsAPI not available after 10 attempts');\n          setDocsAPIStatus('Failed to load');\n          \n          // Log all available window properties that might be related\n          const windowProps = Object.keys(window).filter(key => \n            key.toLowerCase().includes('docs') || \n            key.toLowerCase().includes('only') ||\n            key.toLowerCase().includes('office')\n          );\n          addLog(`Related window properties: ${windowProps.join(', ')}`);\n        }\n      };\n      \n      checkDocsAPI();\n    };\n    \n    script.onerror = (error) => {\n      addLog(`Script loading error: ${error}`);\n      setDocsAPIStatus('Script loading failed');\n    };\n    \n    document.head.appendChild(script);\n    \n    return () => {\n      // Cleanup\n      const scripts = document.querySelectorAll('script[src*=\"only.34sy.org\"]');\n      scripts.forEach(s => s.remove());\n    };\n  }, []);\n\n  return (\n    <div className=\"container mx-auto p-6\">\n      <h1 className=\"text-2xl font-bold mb-4\">OnlyOffice API Test</h1>\n      \n      <div className=\"mb-6\">\n        <h2 className=\"text-lg font-semibold mb-2\">Status</h2>\n        <div className={`p-3 rounded ${\n          docsAPIStatus === 'Available and ready' ? 'bg-green-100 text-green-800' :\n          docsAPIStatus === 'Failed to load' || docsAPIStatus === 'Script loading failed' ? 'bg-red-100 text-red-800' :\n          'bg-yellow-100 text-yellow-800'\n        }`}>\n          {docsAPIStatus}\n        </div>\n      </div>\n      \n      <div>\n        <h2 className=\"text-lg font-semibold mb-2\">Debug Logs</h2>\n        <div className=\"bg-gray-100 p-4 rounded max-h-96 overflow-y-auto\">\n          {logs.map((log, index) => (\n            <div key={index} className=\"text-sm font-mono mb-1\">\n              {log}\n            </div>\n          ))}\n        </div>\n      </div>\n      \n      <div className=\"mt-6\">\n        <button \n          onClick={() => {\n            setLogs([]);\n            setDocsAPIStatus('Not loaded');\n            window.location.reload();\n          }}\n          className=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\"\n        >\n          Reload Test\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,SAAS,CAAC;QACd,QAAQ,CAAA,OAAQ;mBAAI;gBAAM,GAAG,IAAI,OAAO,kBAAkB,GAAG,EAAE,EAAE,SAAS;aAAC;IAC7E;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;QAEP,wCAAwC;QACxC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;YACP,iBAAiB;YACjB;QACF;QAEA,OAAO;QAEP,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,GAAG,GAAG;QACb,OAAO,KAAK,GAAG;QACf,OAAO,WAAW,GAAG;QAErB,OAAO,MAAM,GAAG;YACd,OAAO;YAEP,MAAM,eAAe,CAAC,WAAW,CAAC;gBAChC,OAAO,CAAC,8BAA8B,EAAE,WAAW,EAAE,GAAG,CAAC;gBACzD,OAAO,CAAC,gBAAgB,EAAE,OAAO,OAAO,OAAO,EAAE;gBAEjD,IAAI,OAAO,OAAO,EAAE;oBAClB,OAAO,CAAC,qBAAqB,EAAE,OAAO,OAAO,OAAO,EAAE;oBACtD,OAAO,CAAC,mBAAmB,EAAE,OAAO,OAAO,OAAO,CAAC,SAAS,EAAE;oBAE9D,IAAI,OAAO,OAAO,OAAO,CAAC,SAAS,KAAK,YAAY;wBAClD,OAAO;wBACP,iBAAiB;oBACnB,OAAO;wBACL,OAAO;wBACP,iBAAiB;oBACnB;gBACF,OAAO,IAAI,WAAW,IAAI;oBACxB,OAAO,CAAC,+CAA+C,CAAC;oBACxD,WAAW,IAAM,aAAa,WAAW,IAAI;gBAC/C,OAAO;oBACL,OAAO;oBACP,iBAAiB;oBAEjB,4DAA4D;oBAC5D,MAAM,cAAc,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAA,MAC7C,IAAI,WAAW,GAAG,QAAQ,CAAC,WAC3B,IAAI,WAAW,GAAG,QAAQ,CAAC,WAC3B,IAAI,WAAW,GAAG,QAAQ,CAAC;oBAE7B,OAAO,CAAC,2BAA2B,EAAE,YAAY,IAAI,CAAC,OAAO;gBAC/D;YACF;YAEA;QACF;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,OAAO,CAAC,sBAAsB,EAAE,OAAO;YACvC,iBAAiB;QACnB;QAEA,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO;YACL,UAAU;YACV,MAAM,UAAU,SAAS,gBAAgB,CAAC;YAC1C,QAAQ,OAAO,CAAC,CAAA,IAAK,EAAE,MAAM;QAC/B;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAExC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAW,CAAC,YAAY,EAC3B,kBAAkB,wBAAwB,gCAC1C,kBAAkB,oBAAoB,kBAAkB,0BAA0B,4BAClF,iCACA;kCACC;;;;;;;;;;;;0BAIL,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC;gCAAgB,WAAU;0CACxB;+BADO;;;;;;;;;;;;;;;;0BAOhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;wBACP,QAAQ,EAAE;wBACV,iBAAiB;wBACjB,OAAO,QAAQ,CAAC,MAAM;oBACxB;oBACA,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}