{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/test-config/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\n\r\nexport default function TestConfigPage() {\r\n  const [result, setResult] = useState<any>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const testConfig = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    setResult(null);\r\n\r\n    try {\r\n      const response = await fetch('/api/onlyoffice/config', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          fileId: '2d8319a9-57c1-4758-af8f-6a7dbe0d612c',\r\n          mode: 'edit',\r\n          userId: 'test-user',\r\n          userName: 'Test User'\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n      \r\n      if (response.ok) {\r\n        setResult(data);\r\n        console.log('Config response:', data);\r\n      } else {\r\n        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Network error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const testJwtEnabled = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    setResult(null);\r\n\r\n    try {\r\n      const response = await fetch('/api/test-jwt-config', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          fileId: '2d8319a9-57c1-4758-af8f-6a7dbe0d612c',\r\n          mode: 'edit',\r\n          userId: 'test-user',\r\n          userName: 'Test User'\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n      \r\n      if (response.ok) {\r\n        setResult(data);\r\n        console.log('JWT-enabled response:', data);\r\n      } else {\r\n        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Network error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const testJwtDisabled = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    setResult(null);\r\n\r\n    try {\r\n      const response = await fetch('/api/test-jwt-disabled', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          fileId: '2d8319a9-57c1-4758-af8f-6a7dbe0d612c',\r\n          mode: 'edit',\r\n          userId: 'test-user',\r\n          userName: 'Test User'\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n      \r\n      if (response.ok) {\r\n        setResult(data);\r\n        console.log('JWT-disabled response:', data);\r\n      } else {\r\n        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Network error');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 p-8\">\r\n      <div className=\"max-w-4xl mx-auto\">\r\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">OnlyOffice Config Test</h1>\r\n        \r\n        <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\r\n          <h2 className=\"text-xl font-semibold mb-4\">Test Configuration Endpoints</h2>\r\n          \r\n          <div className=\"space-y-4\">\r\n            <div>\r\n              <button\r\n                onClick={testConfig}\r\n                disabled={loading}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 mr-4\"\r\n              >\r\n                {loading ? 'Testing...' : 'Test Regular Config'}\r\n              </button>\r\n              \r\n              <button\r\n                onClick={testJwtEnabled}\r\n                disabled={loading}\r\n                className=\"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 mr-4\"\r\n              >\r\n                {loading ? 'Testing...' : 'Test JWT-Enabled Config'}\r\n              </button>\r\n              \r\n              <button\r\n                onClick={testJwtDisabled}\r\n                disabled={loading}\r\n                className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50\"\r\n              >\r\n                {loading ? 'Testing...' : 'Test JWT-Disabled Config'}\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"text-sm text-gray-600\">\r\n              <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'Loading...'}</p>\r\n              <p><strong>Test File ID:</strong> 2d8319a9-57c1-4758-af8f-6a7dbe0d612c</p>\r\n              <p><strong>Expected JWT Secret:</strong> fzX5rJRaYYPtns6t</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {error && (\r\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\r\n            <h3 className=\"text-lg font-semibold text-red-800 mb-2\">Error</h3>\r\n            <p className=\"text-red-600\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {result && (\r\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\">\r\n            <h3 className=\"text-lg font-semibold text-green-800 mb-2\">Success</h3>\r\n            <div className=\"space-y-2\">\r\n              <p><strong>Success:</strong> {result.success ? 'Yes' : 'No'}</p>\r\n              <p><strong>JWT Enabled:</strong> {result.jwtEnabled ? 'Yes' : 'No'}</p>\r\n              <p><strong>Server URL:</strong> {result.serverUrl}</p>\r\n              <p><strong>Message:</strong> {result.message}</p>\r\n              {result.debug && (\r\n                <>\r\n                  <p><strong>JWT Secret:</strong> {result.debug.jwtSecret}</p>\r\n                  <p><strong>Token Generated:</strong> {result.debug.tokenGenerated ? 'Yes' : 'No'}</p>\r\n                  <p><strong>Token Length:</strong> {result.debug.tokenLength}</p>\r\n                </>\r\n              )}\r\n              {result.config && (\r\n                <>\r\n                  <p><strong>Document Key:</strong> {result.config.document?.key}</p>\r\n                  <p><strong>Document URL:</strong> {result.config.document?.url}</p>\r\n                  <p><strong>Callback URL:</strong> {result.config.editorConfig?.callbackUrl}</p>\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {result && (\r\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">Full Response</h3>\r\n            <pre className=\"text-sm text-gray-600 overflow-x-auto whitespace-pre-wrap\">\r\n              {JSON.stringify(result, null, 2)}\r\n            </pre>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa;QACjB,WAAW;QACX,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,UAAU;gBACZ;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU;gBACV,QAAQ,GAAG,CAAC,oBAAoB;YAClC,OAAO;gBACL,SAAS,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,KAAK,KAAK,IAAI,iBAAiB;YACtE;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,WAAW;QACX,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,UAAU;gBACZ;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU;gBACV,QAAQ,GAAG,CAAC,yBAAyB;YACvC,OAAO;gBACL,SAAS,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,KAAK,KAAK,IAAI,iBAAiB;YACtE;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,UAAU;gBACZ;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU;gBACV,QAAQ,GAAG,CAAC,0BAA0B;YACxC,OAAO;gBACL,SAAS,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,KAAK,KAAK,IAAI,iBAAiB;YACtE;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAEtD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAE3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,UAAU,eAAe;;;;;;sDAG5B,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,UAAU,eAAe;;;;;;sDAG5B,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,UAAU,eAAe;;;;;;;;;;;;8CAI9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAqB;gDAAE,6EAAuD;;;;;;;sDACzF,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAsB;;;;;;;sDACjC,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;gBAK7C,uBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0C;;;;;;sCACxD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;gBAIhC,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAiB;wCAAE,OAAO,OAAO,GAAG,QAAQ;;;;;;;8CACvD,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAqB;wCAAE,OAAO,UAAU,GAAG,QAAQ;;;;;;;8CAC9D,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAoB;wCAAE,OAAO,SAAS;;;;;;;8CACjD,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAiB;wCAAE,OAAO,OAAO;;;;;;;gCAC3C,OAAO,KAAK,kBACX;;sDACE,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAoB;gDAAE,OAAO,KAAK,CAAC,SAAS;;;;;;;sDACvD,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAyB;gDAAE,OAAO,KAAK,CAAC,cAAc,GAAG,QAAQ;;;;;;;sDAC5E,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAsB;gDAAE,OAAO,KAAK,CAAC,WAAW;;;;;;;;;gCAG9D,OAAO,MAAM,kBACZ;;sDACE,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAsB;gDAAE,OAAO,MAAM,CAAC,QAAQ,EAAE;;;;;;;sDAC3D,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAsB;gDAAE,OAAO,MAAM,CAAC,QAAQ,EAAE;;;;;;;sDAC3D,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAsB;gDAAE,OAAO,MAAM,CAAC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;gBAOxE,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAI,WAAU;sCACZ,KAAK,SAAS,CAAC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}