import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';

export class SecurityService {
  private jwtSecret: string;

  constructor() {
    this.jwtSecret = process.env.ONLYOFFICE_JWT_SECRET || 'fzX5rJRaYYPtns6t';
  }

  /**
   * Validate JWT token from OnlyOffice callback
   */
  validateOnlyOfficeToken(token: string): any {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      throw new Error('Invalid OnlyOffice JWT token');
    }
  }

  /**
   * Sign data with JWT for OnlyOffice
   */
  signOnlyOfficeData(data: any): string {
    return jwt.sign(data, this.jwtSecret, { algorithm: 'HS256' });
  }

  /**
   * Extract and validate JWT from request headers
   */
  extractTokenFromRequest(request: NextRequest): string | null {
    const authHeader = request.headers.get('authorization');
    if (!authHeader) return null;

    const token = authHeader.replace('Bearer ', '');
    return token;
  }

  /**
   * Validate file upload security
   */
  validateFileUpload(file: File): { valid: boolean; error?: string } {
    // Check file size (50MB limit)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return { valid: false, error: 'File size exceeds 50MB limit' };
    }

    // Check file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
      'application/msword', // .doc
      'application/vnd.ms-excel', // .xls
      'application/vnd.ms-powerpoint', // .ppt
      'application/pdf',
      'text/plain',
      'text/csv',
      'application/vnd.oasis.opendocument.text', // .odt
      'application/vnd.oasis.opendocument.spreadsheet', // .ods
      'application/vnd.oasis.opendocument.presentation', // .odp
    ];

    if (!allowedTypes.includes(file.type) && file.type !== '') {
      // Also check by extension if MIME type is not recognized
      const extension = file.name.split('.').pop()?.toLowerCase();
      const allowedExtensions = [
        'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt',
        'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv',
        'ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp',
        'pdf'
      ];

      if (!extension || !allowedExtensions.includes(extension)) {
        return { valid: false, error: 'File type not supported' };
      }
    }

    // Check filename for security
    if (this.containsSuspiciousPatterns(file.name)) {
      return { valid: false, error: 'Filename contains invalid characters' };
    }

    return { valid: true };
  }

  /**
   * Check for suspicious patterns in filenames
   */
  private containsSuspiciousPatterns(filename: string): boolean {
    const suspiciousPatterns = [
      /\.\./,  // Directory traversal
      /[<>:"|?*]/,  // Invalid filename characters
      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,  // Windows reserved names
      /^\./,  // Hidden files
      /\.(exe|bat|cmd|scr|vbs|js|jar|com|pif)$/i,  // Executable files
    ];

    return suspiciousPatterns.some(pattern => pattern.test(filename));
  }

  /**
   * Sanitize filename for safe storage
   */
  sanitizeFilename(filename: string): string {
    // Remove or replace unsafe characters
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '_')  // Replace unsafe chars with underscore
      .replace(/_{2,}/g, '_')  // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '')  // Remove leading/trailing underscores
      .substring(0, 255);  // Limit length
  }

  /**
   * Generate secure document key
   */
  generateSecureDocumentKey(fileId: string, timestamp: number): string {
    const data = `${fileId}_${timestamp}_${this.jwtSecret}`;
    return Buffer.from(data).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);
  }

  /**
   * Validate request origin
   */
  validateOrigin(request: NextRequest): boolean {
    const origin = request.headers.get('origin');
    const referer = request.headers.get('referer');

    const allowedOrigins = [
      'https://only.34sy.org',
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'http://localhost:3003',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001',
      'http://127.0.0.1:3002',
      'http://127.0.0.1:3003',
      process.env.NEXT_PUBLIC_APP_URL,
    ].filter(Boolean);

    // Allow requests without origin (direct API calls, same-origin requests)
    if (!origin && !referer) return true;

    // Check origin
    if (origin && allowedOrigins.includes(origin)) return true;

    // Additional check for localhost with any port (development mode)
    if (origin) {
      try {
        const url = new URL(origin);
        const isLocalhost = url.hostname === 'localhost' || url.hostname === '127.0.0.1';
        const isLocalPort = parseInt(url.port) >= 3000 && parseInt(url.port) <= 3010;
        
        if (isLocalhost && isLocalPort) {
          console.log('Origin validated for localhost development:', origin);
          return true;
        }
      } catch (error) {
        console.warn('Error parsing origin URL:', origin, error);
      }
    }

    // Check referer as fallback
    if (referer) {
      try {
        const refererUrl = new URL(referer);
        const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`;
        
        if (allowedOrigins.includes(refererOrigin)) return true;
        
        // Additional check for localhost referer
        const isLocalhost = refererUrl.hostname === 'localhost' || refererUrl.hostname === '127.0.0.1';
        const isLocalPort = parseInt(refererUrl.port) >= 3000 && parseInt(refererUrl.port) <= 3010;
        
        if (isLocalhost && isLocalPort) {
          console.log('Referer validated for localhost development:', refererOrigin);
          return true;
        }
      } catch (error) {
        console.warn('Error parsing referer URL:', referer, error);
      }
    }

    console.warn('Origin validation failed:', { origin, referer, allowedOrigins });
    return false;
  }

  /**
   * Rate limiting check (simple in-memory implementation)
   */
  private rateLimitStore = new Map<string, { count: number; resetTime: number }>();

  checkRateLimit(identifier: string, maxRequests: number = 100, windowMs: number = 60000): boolean {
    const now = Date.now();
    const record = this.rateLimitStore.get(identifier);

    if (!record || now > record.resetTime) {
      this.rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs });
      return true;
    }

    if (record.count >= maxRequests) {
      return false;
    }

    record.count++;
    return true;
  }

  /**
   * Clean up expired rate limit records
   */
  cleanupRateLimit(): void {
    const now = Date.now();
    for (const [key, record] of this.rateLimitStore.entries()) {
      if (now > record.resetTime) {
        this.rateLimitStore.delete(key);
      }
    }
  }
}

export const securityService = new SecurityService();
