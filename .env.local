# OnlyOffice Configuration
ONLYOFFICE_SERVER_URL=https://only.34sy.org
ONLYOFFICE_JWT_SECRET=fzX5rJRaYYPtns6t
ONLYOFFICE_JWT_HEADER=Authorization

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3001
# For OnlyOffice to access files, you need a public URL
# Install ngrok: npm install -g ngrok
# Run: ngrok http 3001
# Then update this with your ngrok URL:
# NEXT_PUBLIC_APP_URL=https://your-ngrok-url.ngrok.io
UPLOAD_DIR=./public/uploads

# Security
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3001
