{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/lib/fileUtils.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { v4 as uuidv4 } from 'uuid';\n\nexport interface FileInfo {\n  id: string;\n  name: string;\n  size: number;\n  type: string;\n  extension: string;\n  uploadDate: Date;\n  lastModified: Date;\n  url: string;\n}\n\nexport class FileManager {\n  private uploadDir: string;\n\n  constructor() {\n    this.uploadDir = path.join(process.cwd(), 'public', 'uploads');\n    this.ensureUploadDir();\n  }\n\n  private ensureUploadDir(): void {\n    if (!fs.existsSync(this.uploadDir)) {\n      fs.mkdirSync(this.uploadDir, { recursive: true });\n    }\n  }\n\n  async saveFile(file: File): Promise<FileInfo> {\n    const fileId = uuidv4();\n    const extension = path.extname(file.name);\n    const filename = `${fileId}${extension}`;\n    const filepath = path.join(this.uploadDir, filename);\n\n    // Convert File to Buffer\n    const arrayBuffer = await file.arrayBuffer();\n    const buffer = Buffer.from(arrayBuffer);\n\n    // Save file\n    fs.writeFileSync(filepath, buffer);\n\n    const fileInfo: FileInfo = {\n      id: fileId,\n      name: file.name || `document${extension}`, // Ensure we have a proper name\n      size: file.size,\n      type: file.type,\n      extension: extension.slice(1) || 'unknown', // Remove the dot, fallback to 'unknown'\n      uploadDate: new Date(),\n      lastModified: new Date(),\n      url: `/uploads/${filename}`,\n    };\n\n    // Save metadata\n    this.saveFileMetadata(fileInfo);\n\n    return fileInfo;\n  }\n\n  private saveFileMetadata(fileInfo: FileInfo): void {\n    const metadataPath = path.join(this.uploadDir, `${fileInfo.id}.json`);\n    fs.writeFileSync(metadataPath, JSON.stringify(fileInfo, null, 2));\n  }\n\n  getFileMetadata(fileId: string): FileInfo | null {\n    try {\n      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);\n      if (!fs.existsSync(metadataPath)) {\n        return null;\n      }\n      const metadata = fs.readFileSync(metadataPath, 'utf-8');\n      return JSON.parse(metadata);\n    } catch (error) {\n      return null;\n    }\n  }\n\n  getAllFiles(): FileInfo[] {\n    try {\n      const files = fs.readdirSync(this.uploadDir);\n      const metadataFiles = files.filter(file => file.endsWith('.json'));\n      \n      return metadataFiles.map(file => {\n        const metadata = fs.readFileSync(path.join(this.uploadDir, file), 'utf-8');\n        return JSON.parse(metadata);\n      }).sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());\n    } catch (error) {\n      return [];\n    }\n  }\n\n  deleteFile(fileId: string): boolean {\n    try {\n      const fileInfo = this.getFileMetadata(fileId);\n      if (!fileInfo) {\n        return false;\n      }\n\n      const extension = path.extname(fileInfo.name);\n      const filename = `${fileId}${extension}`;\n      const filepath = path.join(this.uploadDir, filename);\n      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);\n\n      // Delete file and metadata\n      if (fs.existsSync(filepath)) {\n        fs.unlinkSync(filepath);\n      }\n      if (fs.existsSync(metadataPath)) {\n        fs.unlinkSync(metadataPath);\n      }\n\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  getFileUrl(fileId: string): string | null {\n    const fileInfo = this.getFileMetadata(fileId);\n    if (!fileInfo) {\n      return null;\n    }\n    \n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';\n    return `${baseUrl}${fileInfo.url}`;\n  }\n\n  isValidFileType(filename: string): boolean {\n    const allowedExtensions = [\n      // Word documents\n      'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt',\n      // Excel spreadsheets\n      'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv',\n      // PowerPoint presentations\n      'ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp',\n      // PDF\n      'pdf'\n    ];\n\n    const extension = path.extname(filename).slice(1).toLowerCase();\n    return allowedExtensions.includes(extension);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAaO,MAAM;IACH,UAAkB;IAE1B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACpD,IAAI,CAAC,eAAe;IACtB;IAEQ,kBAAwB;QAC9B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,GAAG;YAClC,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE;gBAAE,WAAW;YAAK;QACjD;IACF;IAEA,MAAM,SAAS,IAAU,EAAqB;QAC5C,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QACpB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,KAAK,IAAI;QACxC,MAAM,WAAW,GAAG,SAAS,WAAW;QACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAE3C,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,YAAY;QACZ,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU;QAE3B,MAAM,WAAqB;YACzB,IAAI;YACJ,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,WAAW;YACzC,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,WAAW,UAAU,KAAK,CAAC,MAAM;YACjC,YAAY,IAAI;YAChB,cAAc,IAAI;YAClB,KAAK,CAAC,SAAS,EAAE,UAAU;QAC7B;QAEA,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,CAAC;QAEtB,OAAO;IACT;IAEQ,iBAAiB,QAAkB,EAAQ;QACjD,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC,KAAK,CAAC;QACpE,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,cAAc,KAAK,SAAS,CAAC,UAAU,MAAM;IAChE;IAEA,gBAAgB,MAAc,EAAmB;QAC/C,IAAI;YACF,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,OAAO,KAAK,CAAC;YAC/D,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;gBAChC,OAAO;YACT;YACA,MAAM,WAAW,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,cAAc;YAC/C,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,cAA0B;QACxB,IAAI;YACF,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YAC3C,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;YAEzD,OAAO,cAAc,GAAG,CAAC,CAAA;gBACvB,MAAM,WAAW,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO;gBAClE,OAAO,KAAK,KAAK,CAAC;YACpB,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QACrF,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA,WAAW,MAAc,EAAW;QAClC,IAAI;YACF,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;YACtC,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YAEA,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,SAAS,IAAI;YAC5C,MAAM,WAAW,GAAG,SAAS,WAAW;YACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC3C,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,OAAO,KAAK,CAAC;YAE/D,2BAA2B;YAC3B,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;gBAC3B,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YAChB;YACA,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;gBAC/B,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YAChB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,WAAW,MAAc,EAAiB;QACxC,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QACtC,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,MAAM,UAAU,6DAAmC;QACnD,OAAO,GAAG,UAAU,SAAS,GAAG,EAAE;IACpC;IAEA,gBAAgB,QAAgB,EAAW;QACzC,MAAM,oBAAoB;YACxB,iBAAiB;YACjB;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YAAO;YAC3E,qBAAqB;YACrB;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YACpE,2BAA2B;YAC3B;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAC7D,MAAM;YACN;SACD;QAED,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,GAAG,WAAW;QAC7D,OAAO,kBAAkB,QAAQ,CAAC;IACpC;AACF", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/lib/security.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\n\nexport class SecurityService {\n  private jwtSecret: string;\n\n  constructor() {\n    this.jwtSecret = process.env.ONLYOFFICE_JWT_SECRET || 'fzX5rJRaYYPtns6t';\n  }\n\n  /**\n   * Validate JWT token from OnlyOffice callback\n   */\n  validateOnlyOfficeToken(token: string): any {\n    try {\n      return jwt.verify(token, this.jwtSecret);\n    } catch (error) {\n      throw new Error('Invalid OnlyOffice JWT token');\n    }\n  }\n\n  /**\n   * Sign data with JWT for OnlyOffice\n   */\n  signOnlyOfficeData(data: any): string {\n    return jwt.sign(data, this.jwtSecret, { algorithm: 'HS256' });\n  }\n\n  /**\n   * Extract and validate JWT from request headers\n   */\n  extractTokenFromRequest(request: NextRequest): string | null {\n    const authHeader = request.headers.get('authorization');\n    if (!authHeader) return null;\n\n    const token = authHeader.replace('Bearer ', '');\n    return token;\n  }\n\n  /**\n   * Validate file upload security\n   */\n  validateFileUpload(file: File): { valid: boolean; error?: string } {\n    // Check file size (50MB limit)\n    const maxSize = 50 * 1024 * 1024;\n    if (file.size > maxSize) {\n      return { valid: false, error: 'File size exceeds 50MB limit' };\n    }\n\n    // Check file type\n    const allowedTypes = [\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx\n      'application/msword', // .doc\n      'application/vnd.ms-excel', // .xls\n      'application/vnd.ms-powerpoint', // .ppt\n      'application/pdf',\n      'text/plain',\n      'text/csv',\n      'application/vnd.oasis.opendocument.text', // .odt\n      'application/vnd.oasis.opendocument.spreadsheet', // .ods\n      'application/vnd.oasis.opendocument.presentation', // .odp\n    ];\n\n    if (!allowedTypes.includes(file.type) && file.type !== '') {\n      // Also check by extension if MIME type is not recognized\n      const extension = file.name.split('.').pop()?.toLowerCase();\n      const allowedExtensions = [\n        'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt',\n        'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv',\n        'ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp',\n        'pdf'\n      ];\n\n      if (!extension || !allowedExtensions.includes(extension)) {\n        return { valid: false, error: 'File type not supported' };\n      }\n    }\n\n    // Check filename for security\n    if (this.containsSuspiciousPatterns(file.name)) {\n      return { valid: false, error: 'Filename contains invalid characters' };\n    }\n\n    return { valid: true };\n  }\n\n  /**\n   * Check for suspicious patterns in filenames\n   */\n  private containsSuspiciousPatterns(filename: string): boolean {\n    const suspiciousPatterns = [\n      /\\.\\./,  // Directory traversal\n      /[<>:\"|?*]/,  // Invalid filename characters\n      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,  // Windows reserved names\n      /^\\./,  // Hidden files\n      /\\.(exe|bat|cmd|scr|vbs|js|jar|com|pif)$/i,  // Executable files\n    ];\n\n    return suspiciousPatterns.some(pattern => pattern.test(filename));\n  }\n\n  /**\n   * Sanitize filename for safe storage\n   */\n  sanitizeFilename(filename: string): string {\n    // Remove or replace unsafe characters\n    return filename\n      .replace(/[^a-zA-Z0-9._-]/g, '_')  // Replace unsafe chars with underscore\n      .replace(/_{2,}/g, '_')  // Replace multiple underscores with single\n      .replace(/^_+|_+$/g, '')  // Remove leading/trailing underscores\n      .substring(0, 255);  // Limit length\n  }\n\n  /**\n   * Generate secure document key\n   */\n  generateSecureDocumentKey(fileId: string, timestamp: number): string {\n    const data = `${fileId}_${timestamp}_${this.jwtSecret}`;\n    return Buffer.from(data).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);\n  }\n\n  /**\n   * Validate request origin\n   */\n  validateOrigin(request: NextRequest): boolean {\n    const origin = request.headers.get('origin');\n    const referer = request.headers.get('referer');\n\n    const allowedOrigins = [\n      'https://only.34sy.org',\n      'http://localhost:3000',\n      'http://localhost:3001',\n      'http://127.0.0.1:3000',\n      'http://127.0.0.1:3001',\n      process.env.NEXT_PUBLIC_APP_URL,\n    ].filter(Boolean);\n\n    // Allow requests without origin (direct API calls, same-origin requests)\n    if (!origin && !referer) return true;\n\n    // Check origin\n    if (origin && allowedOrigins.includes(origin)) return true;\n\n    // Check referer as fallback\n    if (referer) {\n      try {\n        const refererUrl = new URL(referer);\n        const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`;\n        return allowedOrigins.includes(refererOrigin);\n      } catch {\n        return false;\n      }\n    }\n\n    // Allow same-origin requests (when origin is null but referer matches)\n    return true;\n  }\n\n  /**\n   * Rate limiting check (simple in-memory implementation)\n   */\n  private rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\n  checkRateLimit(identifier: string, maxRequests: number = 100, windowMs: number = 60000): boolean {\n    const now = Date.now();\n    const record = this.rateLimitStore.get(identifier);\n\n    if (!record || now > record.resetTime) {\n      this.rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs });\n      return true;\n    }\n\n    if (record.count >= maxRequests) {\n      return false;\n    }\n\n    record.count++;\n    return true;\n  }\n\n  /**\n   * Clean up expired rate limit records\n   */\n  cleanupRateLimit(): void {\n    const now = Date.now();\n    for (const [key, record] of this.rateLimitStore.entries()) {\n      if (now > record.resetTime) {\n        this.rateLimitStore.delete(key);\n      }\n    }\n  }\n}\n\nexport const securityService = new SecurityService();\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM;IACH,UAAkB;IAE1B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,qBAAqB,IAAI;IACxD;IAEA;;GAEC,GACD,wBAAwB,KAAa,EAAO;QAC1C,IAAI;YACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,mBAAmB,IAAS,EAAU;QACpC,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE;YAAE,WAAW;QAAQ;IAC7D;IAEA;;GAEC,GACD,wBAAwB,OAAoB,EAAiB;QAC3D,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,QAAQ,WAAW,OAAO,CAAC,WAAW;QAC5C,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB,IAAU,EAAsC;QACjE,+BAA+B;QAC/B,MAAM,UAAU,KAAK,OAAO;QAC5B,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBAAE,OAAO;gBAAO,OAAO;YAA+B;QAC/D;QAEA,kBAAkB;QAClB,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI;YACzD,yDAAyD;YACzD,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;YAC9C,MAAM,oBAAoB;gBACxB;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAO;gBAAO;gBAC3E;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAO;gBACpE;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAC7D;aACD;YAED,IAAI,CAAC,aAAa,CAAC,kBAAkB,QAAQ,CAAC,YAAY;gBACxD,OAAO;oBAAE,OAAO;oBAAO,OAAO;gBAA0B;YAC1D;QACF;QAEA,8BAA8B;QAC9B,IAAI,IAAI,CAAC,0BAA0B,CAAC,KAAK,IAAI,GAAG;YAC9C,OAAO;gBAAE,OAAO;gBAAO,OAAO;YAAuC;QACvE;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA;;GAEC,GACD,AAAQ,2BAA2B,QAAgB,EAAW;QAC5D,MAAM,qBAAqB;YACzB;YACA;YACA;YACA;YACA;SACD;QAED,OAAO,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;IACzD;IAEA;;GAEC,GACD,iBAAiB,QAAgB,EAAU;QACzC,sCAAsC;QACtC,OAAO,SACJ,OAAO,CAAC,oBAAoB,KAAM,uCAAuC;SACzE,OAAO,CAAC,UAAU,KAAM,2CAA2C;SACnE,OAAO,CAAC,YAAY,IAAK,sCAAsC;SAC/D,SAAS,CAAC,GAAG,MAAO,eAAe;IACxC;IAEA;;GAEC,GACD,0BAA0B,MAAc,EAAE,SAAiB,EAAU;QACnE,MAAM,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE;QACvD,OAAO,OAAO,IAAI,CAAC,MAAM,QAAQ,CAAC,UAAU,OAAO,CAAC,iBAAiB,IAAI,SAAS,CAAC,GAAG;IACxF;IAEA;;GAEC,GACD,eAAe,OAAoB,EAAW;QAC5C,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEpC,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;YACA;;SAED,CAAC,MAAM,CAAC;QAET,yEAAyE;QACzE,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;QAEhC,eAAe;QACf,IAAI,UAAU,eAAe,QAAQ,CAAC,SAAS,OAAO;QAEtD,4BAA4B;QAC5B,IAAI,SAAS;YACX,IAAI;gBACF,MAAM,aAAa,IAAI,IAAI;gBAC3B,MAAM,gBAAgB,GAAG,WAAW,QAAQ,CAAC,EAAE,EAAE,WAAW,IAAI,EAAE;gBAClE,OAAO,eAAe,QAAQ,CAAC;YACjC,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,uEAAuE;QACvE,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,iBAAiB,IAAI,MAAoD;IAEjF,eAAe,UAAkB,EAAE,cAAsB,GAAG,EAAE,WAAmB,KAAK,EAAW;QAC/F,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,UAAU,MAAM,OAAO,SAAS,EAAE;YACrC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY;gBAAE,OAAO;gBAAG,WAAW,MAAM;YAAS;YAC1E,OAAO;QACT;QAEA,IAAI,OAAO,KAAK,IAAI,aAAa;YAC/B,OAAO;QACT;QAEA,OAAO,KAAK;QACZ,OAAO;IACT;IAEA;;GAEC,GACD,mBAAyB;QACvB,MAAM,MAAM,KAAK,GAAG;QACpB,KAAK,MAAM,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,GAAI;YACzD,IAAI,MAAM,OAAO,SAAS,EAAE;gBAC1B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YAC7B;QACF;IACF;AACF;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { FileManager } from '@/lib/fileUtils';\nimport { securityService } from '@/lib/security';\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Validate origin\n    if (!securityService.validateOrigin(request)) {\n      return NextResponse.json(\n        { error: 'Invalid origin' },\n        { status: 403 }\n      );\n    }\n\n    // Rate limiting\n    const clientIp = request.headers.get('x-forwarded-for') || 'unknown';\n    if (!securityService.checkRateLimit(clientIp, 10, 60000)) { // 10 uploads per minute\n      return NextResponse.json(\n        { error: 'Rate limit exceeded. Please try again later.' },\n        { status: 429 }\n      );\n    }\n\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No file provided' },\n        { status: 400 }\n      );\n    }\n\n    // Security validation\n    const validation = securityService.validateFileUpload(file);\n    if (!validation.valid) {\n      return NextResponse.json(\n        { error: validation.error },\n        { status: 400 }\n      );\n    }\n\n    const fileManager = new FileManager();\n\n    // Additional file type validation\n    if (!fileManager.isValidFileType(file.name)) {\n      return NextResponse.json(\n        { error: 'Invalid file type. Please upload a supported document format.' },\n        { status: 400 }\n      );\n    }\n\n    // Save file with sanitized name\n    const sanitizedFile = new File([file], securityService.sanitizeFilename(file.name), {\n      type: file.type,\n      lastModified: file.lastModified,\n    });\n\n    const fileInfo = await fileManager.saveFile(sanitizedFile);\n\n    return NextResponse.json({\n      success: true,\n      file: fileInfo,\n    });\n  } catch (error) {\n    console.error('Upload error:', error);\n    return NextResponse.json(\n      { error: 'Failed to upload file' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  try {\n    const fileManager = new FileManager();\n    const files = fileManager.getAllFiles();\n\n    return NextResponse.json({\n      success: true,\n      files,\n    });\n  } catch (error) {\n    console.error('Get files error:', error);\n    return NextResponse.json(\n      { error: 'Failed to get files' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,kBAAkB;QAClB,IAAI,CAAC,wHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,UAAU;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;QAC3D,IAAI,CAAC,wHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,UAAU,IAAI,QAAQ;YACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;QACtD,IAAI,CAAC,WAAW,KAAK,EAAE;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,WAAW,KAAK;YAAC,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,IAAI,yHAAA,CAAA,cAAW;QAEnC,kCAAkC;QAClC,IAAI,CAAC,YAAY,eAAe,CAAC,KAAK,IAAI,GAAG;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgE,GACzE;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,gBAAgB,IAAI,KAAK;YAAC;SAAK,EAAE,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,KAAK,IAAI,GAAG;YAClF,MAAM,KAAK,IAAI;YACf,cAAc,KAAK,YAAY;QACjC;QAEA,MAAM,WAAW,MAAM,YAAY,QAAQ,CAAC;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,IAAI,yHAAA,CAAA,cAAW;QACnC,MAAM,QAAQ,YAAY,WAAW;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}