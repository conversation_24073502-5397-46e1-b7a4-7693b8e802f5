import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // Create response
  const response = NextResponse.next();

  // CORS headers for OnlyOffice integration
  const origin = request.headers.get('origin');
  const allowedOrigins = [
    'https://only.34sy.org',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3001',
    'http://127.0.0.1:3002',
    'http://127.0.0.1:3003',
    process.env.NEXT_PUBLIC_APP_URL,
  ].filter(Boolean);

  // For file serving endpoints, allow all origins (OnlyOffice server needs access)
  if (request.nextUrl.pathname.startsWith('/api/files/serve/')) {
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Credentials', 'false');
  } else if (origin && allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
    response.headers.set('Access-Control-Allow-Credentials', 'true');
  } else if (origin) {
    // Additional check for localhost with any port (development mode)
    try {
      const url = new URL(origin);
      const isLocalhost = url.hostname === 'localhost' || url.hostname === '127.0.0.1';
      const isLocalPort = parseInt(url.port) >= 3000 && parseInt(url.port) <= 3010;
      
      if (isLocalhost && isLocalPort) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
      }
    } catch (error) {
      console.warn('Error parsing origin URL in middleware:', origin, error);
    }
  }

  response.headers.set(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  response.headers.set(
    'Access-Control-Allow-Headers',
    'Content-Type, Authorization, X-Requested-With, Accept, Origin'
  );

  // Security headers
  response.headers.set('X-Frame-Options', 'SAMEORIGIN');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://only.34sy.org; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: https:; " +
    "font-src 'self' data:; " +
    "connect-src 'self' https://only.34sy.org; " +
    "frame-src 'self' https://only.34sy.org; " +
    "object-src 'none'; " +
    "base-uri 'self';"
  );

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: response.headers });
  }

  return response;
}

export const config = {
  matcher: [
    '/api/:path*',
    '/editor/:path*',
    '/uploads/:path*',
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
