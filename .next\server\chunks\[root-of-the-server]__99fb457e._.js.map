{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/api/debug-jwt/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const jwtSecret = process.env.ONLYOFFICE_JWT_SECRET;\n    const serverUrl = process.env.ONLYOFFICE_SERVER_URL;\n    \n    return NextResponse.json({\n      success: true,\n      jwtSecret: jwtSecret ? `${jwtSecret.substring(0, 3)}***` : 'NOT SET',\n      jwtSecretLength: jwtSecret?.length || 0,\n      serverUrl,\n      hasJwtSecret: !!jwtSecret,\n      environment: {\n        ONLYOFFICE_JWT_SECRET: process.env.ONLYOFFICE_JWT_SECRET ? 'SET' : 'NOT SET',\n        ONLYOFFICE_SERVER_URL: process.env.ONLYOFFICE_SERVER_URL || 'NOT SET'\n      }\n    });\n  } catch (error) {\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,YAAY,QAAQ,GAAG,CAAC,qBAAqB;QACnD,MAAM;QAEN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,WAAW,YAAY,GAAG,UAAU,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;YAC3D,iBAAiB,WAAW,UAAU;YACtC;YACA,cAAc,CAAC,CAAC;YAChB,aAAa;gBACX,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB,GAAG,QAAQ;gBACnE,uBAAuB,6DAAqC;YAC9D;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}