globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/debug/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"578":{"*":{"id":"972","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"1739":{"*":{"id":"6760","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"5656","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"7031":{"*":{"id":"8830","name":"*","chunks":[],"async":false}},"7152":{"*":{"id":"8286","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":2093,"name":"*","chunks":["177","static/chunks/app/layout-7deb517560ae672a.js"],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":7735,"name":"*","chunks":["177","static/chunks/app/layout-7deb517560ae672a.js"],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\globals.css":{"id":347,"name":"*","chunks":["177","static/chunks/app/layout-7deb517560ae672a.js"],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\editor\\[id]\\page.tsx":{"id":1739,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\debug\\page.tsx":{"id":7152,"name":"*","chunks":["302","static/chunks/app/debug/page-614cf86ce76bddd1.js"],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\test-onlyoffice\\page.tsx":{"id":578,"name":"*","chunks":[],"async":false},"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\page.tsx":{"id":7031,"name":"*","chunks":["974","static/chunks/app/page-0940ad947dd8f7f9.js"],"async":false}},"entryCSSFiles":{"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\":[],"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\layout":[{"inlined":false,"path":"static/css/6e51d96f7f482338.css"}],"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\page":[],"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\debug\\page":[]},"rscModuleMapping":{"347":{"*":{"id":"1135","name":"*","chunks":[],"async":false}},"578":{"*":{"id":"7834","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"1739":{"*":{"id":"9974","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"8170","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"7031":{"*":{"id":"1204","name":"*","chunks":[],"async":false}},"7152":{"*":{"id":"8024","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}