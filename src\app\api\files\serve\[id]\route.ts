import { NextRequest, NextResponse } from 'next/server';
import { FileManager } from '@/lib/fileUtils';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: fileId } = await params;
    const fileManager = new FileManager();

    const fileInfo = fileManager.getFileMetadata(fileId);

    if (!fileInfo) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Construct file path
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    const extension = path.extname(fileInfo.name) || `.${fileInfo.extension}`;
    const filename = `${fileId}${extension}`;
    const filepath = path.join(uploadDir, filename);

    // Check if file exists
    if (!fs.existsSync(filepath)) {
      return NextResponse.json(
        { error: 'File not found on disk' },
        { status: 404 }
      );
    }

    // Read file
    const fileBuffer = fs.readFileSync(filepath);

    // Set appropriate headers for OnlyOffice
    const headers = new Headers();
    headers.set('Content-Type', fileInfo.type || 'application/octet-stream');
    headers.set('Content-Length', fileBuffer.length.toString());
    headers.set('Content-Disposition', `inline; filename="${fileInfo.name}"`);
    
    // CORS headers for OnlyOffice
    headers.set('Access-Control-Allow-Origin', 'https://only.34sy.org');
    headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    // Cache headers
    headers.set('Cache-Control', 'public, max-age=3600');
    headers.set('ETag', `"${fileId}-${fileInfo.lastModified}"`);

    return new NextResponse(fileBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('File serve error:', error);
    return NextResponse.json(
      { error: 'Failed to serve file' },
      { status: 500 }
    );
  }
}

export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: fileId } = await params;
    const fileManager = new FileManager();

    const fileInfo = fileManager.getFileMetadata(fileId);

    if (!fileInfo) {
      return new NextResponse(null, { status: 404 });
    }

    // Construct file path
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    const extension = path.extname(fileInfo.name) || `.${fileInfo.extension}`;
    const filename = `${fileId}${extension}`;
    const filepath = path.join(uploadDir, filename);

    // Check if file exists
    if (!fs.existsSync(filepath)) {
      return new NextResponse(null, { status: 404 });
    }

    // Get file stats
    const stats = fs.statSync(filepath);

    // Set appropriate headers
    const headers = new Headers();
    headers.set('Content-Type', fileInfo.type || 'application/octet-stream');
    headers.set('Content-Length', stats.size.toString());
    headers.set('Content-Disposition', `inline; filename="${fileInfo.name}"`);
    
    // CORS headers for OnlyOffice
    headers.set('Access-Control-Allow-Origin', 'https://only.34sy.org');
    headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    // Cache headers
    headers.set('Cache-Control', 'public, max-age=3600');
    headers.set('ETag', `"${fileId}-${fileInfo.lastModified}"`);

    return new NextResponse(null, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('File head error:', error);
    return new NextResponse(null, { status: 500 });
  }
}

export async function OPTIONS() {
  const headers = new Headers();
  headers.set('Access-Control-Allow-Origin', 'https://only.34sy.org');
  headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  return new NextResponse(null, {
    status: 200,
    headers,
  });
}
