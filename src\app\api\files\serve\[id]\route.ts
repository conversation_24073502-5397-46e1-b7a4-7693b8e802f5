import { NextRequest, NextResponse } from 'next/server';
import { FileManager } from '@/lib/fileUtils';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: fileId } = await params;
    
    console.log('File serve request for ID:', fileId);
    
    const fileManager = new FileManager();

    const fileInfo = fileManager.getFileMetadata(fileId);

    if (!fileInfo) {
      console.error('File metadata not found for ID:', fileId);
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    console.log('File metadata found:', {
      id: fileId,
      name: fileInfo.name,
      type: fileInfo.type,
      size: fileInfo.size,
      extension: fileInfo.extension
    });

    // Construct file path
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');

    // Try multiple possible file paths
    const possiblePaths = [
      // First try: file with extension from filename
      path.join(uploadDir, `${fileId}${path.extname(fileInfo.name)}`),
      // Second try: file with extension from metadata
      path.join(uploadDir, `${fileId}.${fileInfo.extension}`),
      // Third try: file without extension (as stored)
      path.join(uploadDir, fileId),
      // Fourth try: file with .xlsx extension (common case)
      path.join(uploadDir, `${fileId}.xlsx`),
      // Fifth try: file with .docx extension
      path.join(uploadDir, `${fileId}.docx`),
      // Sixth try: file with .pptx extension
      path.join(uploadDir, `${fileId}.pptx`)
    ];

    let filepath = '';
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        filepath = possiblePath;
        console.log('File found at path:', filepath);
        break;
      }
    }

    // Check if file exists
    if (!filepath || !fs.existsSync(filepath)) {
      console.error('File not found on disk. Tried paths:', possiblePaths);
      console.error('Upload directory contents:', fs.existsSync(uploadDir) ? fs.readdirSync(uploadDir) : 'Directory does not exist');
      return NextResponse.json(
        { error: 'File not found on disk' },
        { status: 404 }
      );
    }

    // Read file
    const fileBuffer = fs.readFileSync(filepath);
    
    console.log('File successfully read:', {
      path: filepath,
      size: fileBuffer.length,
      type: fileInfo.type
    });

    // Set appropriate headers for OnlyOffice
    const headers = new Headers();
    headers.set('Content-Type', fileInfo.type || 'application/octet-stream');
    headers.set('Content-Length', fileBuffer.length.toString());
    headers.set('Content-Disposition', `inline; filename="${fileInfo.name}"`);

    // CORS headers for OnlyOffice - must allow all origins for external server access
    headers.set('Access-Control-Allow-Origin', '*');
    headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    headers.set('Access-Control-Allow-Credentials', 'false');

    // Cache headers
    headers.set('Cache-Control', 'public, max-age=3600');
    headers.set('ETag', `"${fileId}-${fileInfo.lastModified}"`);

    // Additional headers for OnlyOffice compatibility
    headers.set('Accept-Ranges', 'bytes');
    headers.set('X-Content-Type-Options', 'nosniff');

    return new NextResponse(fileBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('File serve error:', error);
    return NextResponse.json(
      { error: 'Failed to serve file' },
      { status: 500 }
    );
  }
}

export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: fileId } = await params;
    const fileManager = new FileManager();

    const fileInfo = fileManager.getFileMetadata(fileId);

    if (!fileInfo) {
      return new NextResponse(null, { status: 404 });
    }

    // Construct file path
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');

    // Try multiple possible file paths
    const possiblePaths = [
      // First try: file with extension from filename
      path.join(uploadDir, `${fileId}${path.extname(fileInfo.name)}`),
      // Second try: file with extension from metadata
      path.join(uploadDir, `${fileId}.${fileInfo.extension}`),
      // Third try: file without extension (as stored)
      path.join(uploadDir, fileId),
      // Fourth try: file with .xlsx extension (common case)
      path.join(uploadDir, `${fileId}.xlsx`),
      // Fifth try: file with .docx extension
      path.join(uploadDir, `${fileId}.docx`),
      // Sixth try: file with .pptx extension
      path.join(uploadDir, `${fileId}.pptx`)
    ];

    let filepath = '';
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        filepath = possiblePath;
        break;
      }
    }

    // Check if file exists
    if (!filepath || !fs.existsSync(filepath)) {
      return new NextResponse(null, { status: 404 });
    }

    // Get file stats
    const stats = fs.statSync(filepath);

    // Set appropriate headers
    const headers = new Headers();
    headers.set('Content-Type', fileInfo.type || 'application/octet-stream');
    headers.set('Content-Length', stats.size.toString());
    headers.set('Content-Disposition', `inline; filename="${fileInfo.name}"`);

    // CORS headers for OnlyOffice - must allow all origins for external server access
    headers.set('Access-Control-Allow-Origin', '*');
    headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    headers.set('Access-Control-Allow-Credentials', 'false');

    // Cache headers
    headers.set('Cache-Control', 'public, max-age=3600');
    headers.set('ETag', `"${fileId}-${fileInfo.lastModified}"`);

    // Additional headers for OnlyOffice compatibility
    headers.set('Accept-Ranges', 'bytes');
    headers.set('X-Content-Type-Options', 'nosniff');

    return new NextResponse(null, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('File head error:', error);
    return new NextResponse(null, { status: 500 });
  }
}

export async function OPTIONS() {
  const headers = new Headers();
  headers.set('Access-Control-Allow-Origin', '*');
  headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  headers.set('Access-Control-Allow-Credentials', 'false');
  headers.set('Access-Control-Max-Age', '86400');

  return new NextResponse(null, {
    status: 200,
    headers,
  });
}
