import { NextRequest, NextResponse } from 'next/server';
import { OnlyOfficeService } from '@/lib/onlyoffice';
import { FileManager } from '@/lib/fileUtils';

export async function POST(request: NextRequest) {
  try {
    const { fileId, mode = 'edit', userId = 'test-user', userName = 'Test User' } = await request.json();

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      );
    }

    const fileManager = new FileManager();
    const fileInfo = fileManager.getFileMetadata(fileId);

    if (!fileInfo) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    const onlyOfficeService = new OnlyOfficeService();
    
    // Get the current host and port from the request
    const host = request.headers.get('host') || 'localhost:3001';
    const protocol = request.headers.get('x-forwarded-proto') || 'http';
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || `${protocol}://${host}`;

    // Use the dedicated file serving endpoint for OnlyOffice
    const fileUrl = `${baseUrl}/api/files/serve/${fileId}`;
    const callbackUrl = `${baseUrl}/api/onlyoffice/callback`;

    console.log('Creating OnlyOffice config WITH JWT for:', {
      fileId,
      fileName: fileInfo.name,
      fileUrl,
      callbackUrl,
      mode,
      detectedHost: host,
      detectedProtocol: protocol
    });

    // Create OnlyOffice configuration
    const config = onlyOfficeService.createConfig(
      fileInfo.name,
      fileUrl,
      callbackUrl,
      userId,
      userName,
      mode as 'edit' | 'view'
    );

    // Force JWT signing (ignoring ONLYOFFICE_JWT_DISABLED)
    const originalEnv = process.env.ONLYOFFICE_JWT_DISABLED;
    process.env.ONLYOFFICE_JWT_DISABLED = 'false';
    
    const token = onlyOfficeService.signConfig(config);
    
    // Restore original environment
    process.env.ONLYOFFICE_JWT_DISABLED = originalEnv;

    const responseConfig = {
      ...config,
      serverUrl: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',
    };

    // Add token to the root level (OnlyOffice expects it here)
    if (token) {
      responseConfig.token = token;
    }

    console.log('Sending OnlyOffice config WITH JWT:', {
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      configKeys: Object.keys(responseConfig),
      documentKey: config.document.key,
      documentUrl: config.document.url,
      callbackUrl: config.editorConfig.callbackUrl,
      jwtSecret: process.env.ONLYOFFICE_JWT_SECRET ? 'SET' : 'NOT SET'
    });

    return NextResponse.json({
      success: true,
      config: responseConfig,
      token: token,
      serverUrl: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',
      jwtEnabled: !!token,
      message: 'Configuration generated WITH JWT authentication (forced)',
      debug: {
        jwtSecret: process.env.ONLYOFFICE_JWT_SECRET ? 'SET' : 'NOT SET',
        tokenGenerated: !!token,
        tokenLength: token ? token.length : 0
      }
    });
  } catch (error) {
    console.error('JWT config generation error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to generate configuration with JWT',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 