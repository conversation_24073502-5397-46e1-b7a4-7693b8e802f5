import { NextRequest, NextResponse } from 'next/server';
import { OnlyOfficeService } from '@/lib/onlyoffice';
import { FileManager } from '@/lib/fileUtils';

export async function POST(request: NextRequest) {
  try {
    const { fileId, mode = 'edit', userId = 'test-user', userName = 'Test User' } = await request.json();

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      );
    }

    const fileManager = new FileManager();
    const fileInfo = fileManager.getFileMetadata(fileId);

    if (!fileInfo) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    const onlyOfficeService = new OnlyOfficeService();
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001';

    // Use the dedicated file serving endpoint for OnlyOffice
    const fileUrl = `${baseUrl}/api/files/serve/${fileId}`;
    const callbackUrl = `${baseUrl}/api/onlyoffice/callback`;

    console.log('Creating OnlyOffice config WITHOUT JWT for:', {
      fileId,
      fileName: fileInfo.name,
      fileUrl,
      callbackUrl,
      mode
    });

    // Create OnlyOffice configuration
    const config = onlyOfficeService.createConfig(
      fileInfo.name,
      fileUrl,
      callbackUrl,
      userId,
      userName,
      mode as 'edit' | 'view'
    );

    // Explicitly disable JWT for this test
    const responseConfig = {
      ...config,
      serverUrl: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',
      // No token property - completely disable JWT
    };

    console.log('Sending OnlyOffice config WITHOUT JWT:', {
      hasToken: false,
      configKeys: Object.keys(responseConfig),
      documentKey: config.document.key,
      documentUrl: config.document.url,
      callbackUrl: config.editorConfig.callbackUrl
    });

    return NextResponse.json({
      success: true,
      config: responseConfig,
      token: null,
      serverUrl: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',
      jwtEnabled: false,
      message: 'Configuration generated WITHOUT JWT authentication'
    });
  } catch (error) {
    console.error('JWT-disabled config generation error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to generate configuration without JWT',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 