# JWT Token Error Fix Guide

## Problem
You were getting OnlyOffice error code -20: "The document security token is not correctly formed." This happens when the JWT token doesn't match what the OnlyOffice server expects.

## Root Causes Identified

1. **Wrong JWT Secret**: The app was using 'mysecret' but your OnlyOffice server expects 'fzX5rJRaYYPtns6t'
2. **Port Mismatch**: App runs on port 3002 but URLs were hardcoded to port 3001
3. **Missing JWT Fallback**: When JWT fails, the app should try without JWT authentication

## Fixes Applied

### 1. Updated JWT Secret
**File**: `src/lib/onlyoffice.ts`
- Changed default JWT secret from 'mysecret' to 'fzX5rJRaYYPtns6t'
- This matches the secret configured in your OnlyOffice server

### 2. Fixed Port Detection
**File**: `src/app/api/onlyoffice/config/route.ts`
- Now detects the actual port from request headers
- Builds correct URLs for file serving and callbacks
- No more hardcoded port 3001

### 3. Enhanced JWT Fallback
**File**: `src/components/OnlyOfficeEditor.tsx`
- Detects error code -20 specifically
- Automatically retries with JWT-disabled configuration
- Better error handling and logging

### 4. Origin Validation Fix
**Files**: `src/lib/security.ts`, `src/middleware.ts`
- Added support for ports 3000-3010 in development
- Fixed the 403 "Invalid origin" errors

## Testing the Fix

### Step 1: Test Configuration Endpoints
Visit: `http://localhost:3002/test-config`

This page has three test buttons:
- **Test Regular Config**: Uses your current environment settings
- **Test JWT-Enabled Config**: Forces JWT authentication with the correct secret
- **Test JWT-Disabled Config**: Uses no JWT authentication

### Step 2: Check Server Logs
Look for these log messages:
```
OnlyOffice Service Configuration: {
  serverUrl: 'http://localhost:8080',
  hasJwtSecret: true,
  jwtEnabled: true,
  jwtSecret: 'Using production secret'
}
```

### Step 3: Test Document Editor
1. Visit: `http://localhost:3002/editor/2d8319a9-57c1-4758-af8f-6a7dbe0d612c`
2. The editor should load without JWT errors
3. Check browser console for success messages

## Environment Variables

Create/update your `.env.local` file:

```env
# OnlyOffice Configuration
ONLYOFFICE_SERVER_URL=http://localhost:8080
ONLYOFFICE_JWT_SECRET=fzX5rJRaYYPtns6t

# App URL (will auto-detect port if not set)
NEXT_PUBLIC_APP_URL=http://*************:3002

# Optional: Disable JWT for testing
# ONLYOFFICE_JWT_DISABLED=true
```

## Expected Behavior

### With JWT Enabled (Default)
- JWT token is generated using the correct secret
- OnlyOffice server accepts the token
- Documents load successfully

### With JWT Disabled (Fallback)
- No JWT token is sent
- OnlyOffice server must be configured to accept unsigned requests
- Documents load successfully

### Automatic Fallback
- If JWT fails with error -20, the app automatically tries JWT-disabled mode
- User sees no error if fallback succeeds
- Logs show the fallback attempt

## Troubleshooting

### Still Getting Error -20?
1. Check that your OnlyOffice server uses JWT secret: `fzX5rJRaYYPtns6t`
2. Test with the JWT-enabled config endpoint
3. Verify the token is being generated (check logs)

### Port Issues?
1. Check what port your app is running on
2. Verify file URLs use the correct port
3. Update NEXT_PUBLIC_APP_URL if needed

### Origin Validation Errors?
1. Ensure your app port is between 3000-3010
2. Check middleware logs for origin validation messages
3. Verify browser and server are on the same network

## Test Results Expected

### JWT-Enabled Config Test
```json
{
  "success": true,
  "jwtEnabled": true,
  "debug": {
    "jwtSecret": "SET",
    "tokenGenerated": true,
    "tokenLength": 200+ // Should be a long token
  }
}
```

### JWT-Disabled Config Test
```json
{
  "success": true,
  "jwtEnabled": false,
  "message": "Configuration generated WITHOUT JWT authentication"
}
```

## Summary

The fix addresses the JWT token issue by:
1. Using the correct JWT secret that matches your OnlyOffice server
2. Properly detecting the app's port for correct URL generation
3. Implementing automatic fallback to JWT-disabled mode if JWT fails
4. Fixing origin validation to support dynamic ports

The editor should now work without the error -20 JWT token issue! 