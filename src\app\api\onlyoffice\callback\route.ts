import { NextRequest, NextResponse } from 'next/server';
import { OnlyOfficeService } from '@/lib/onlyoffice';
import { FileManager } from '@/lib/fileUtils';
import fs from 'fs';
import path from 'path';

interface CallbackData {
  key: string;
  status: number;
  url?: string;
  users?: string[];
  actions?: any[];
  lastsave?: string;
  notmodified?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as CallbackData;
    const onlyOfficeService = new OnlyOfficeService();
    
    console.log('OnlyOffice callback received:', body);

    // Status codes:
    // 0 - no document with the key identifier could be found
    // 1 - document is being edited
    // 2 - document is ready for saving (must save the document)
    // 3 - document saving error has occurred
    // 4 - document is closed with no changes
    // 6 - document is being edited, but the current document state is saved
    // 7 - error has occurred while force saving the document

    if (body.status === 2 || body.status === 6) {
      // Document needs to be saved
      if (body.url) {
        try {
          // Download the document from OnlyOffice
          const response = await fetch(body.url);
          if (!response.ok) {
            throw new Error(`Failed to download document: ${response.statusText}`);
          }

          const buffer = await response.arrayBuffer();
          
          // Find the file by document key
          const fileManager = new FileManager();
          const files = fileManager.getAllFiles();
          
          // Extract file ID from document key (assuming key format includes file ID)
          const fileId = Buffer.from(body.key, 'base64').toString('utf-8').split('_')[0];
          const fileInfo = fileManager.getFileMetadata(fileId);
          
          if (fileInfo) {
            // Save the updated document
            const uploadDir = path.join(process.cwd(), 'public', 'uploads');
            const extension = path.extname(fileInfo.name);
            const filename = `${fileId}${extension}`;
            const filepath = path.join(uploadDir, filename);
            
            fs.writeFileSync(filepath, Buffer.from(buffer));
            
            // Update metadata
            fileInfo.lastModified = new Date();
            const metadataPath = path.join(uploadDir, `${fileId}.json`);
            fs.writeFileSync(metadataPath, JSON.stringify(fileInfo, null, 2));
            
            console.log(`Document saved successfully: ${filename}`);
          }
        } catch (error) {
          console.error('Error saving document:', error);
          return NextResponse.json({ error: 0 }, { status: 500 });
        }
      }
    }

    // Return success response
    return NextResponse.json({ error: 0 });
  } catch (error) {
    console.error('Callback error:', error);
    return NextResponse.json({ error: 1 }, { status: 500 });
  }
}
