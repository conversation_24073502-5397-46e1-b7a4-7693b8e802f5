(()=>{var e={};e.id=165,e.ids=[165],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},6957:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>d,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var r={};t.r(r),t.d(r,{GET:()=>c});var n=t(6559),o=t(8088),a=t(7719),i=t(2190);async function c(e){try{console.log("Testing OnlyOffice server accessibility...");let e=await fetch("https://only.34sy.org/web-apps/apps/api/documents/api.js",{method:"HEAD",headers:{"User-Agent":"OnlyOffice-Test-Client"}});if(console.log("OnlyOffice server response:",{status:e.status,statusText:e.statusText,headers:Object.fromEntries(e.headers.entries())}),e.ok)return i.NextResponse.json({success:!0,message:"OnlyOffice server is accessible",status:e.status,headers:Object.fromEntries(e.headers.entries())});return i.NextResponse.json({success:!1,message:"OnlyOffice server returned error",status:e.status,statusText:e.statusText},{status:500})}catch(e){return console.error("Error testing OnlyOffice server:",e),i.NextResponse.json({success:!1,message:"Failed to connect to OnlyOffice server",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let u=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/test-onlyoffice/route",pathname:"/api/test-onlyoffice",filename:"route",bundlePath:"app/api/test-onlyoffice/route"},resolvedPagePath:"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\api\\test-onlyoffice\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:d}=u;function f(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,580],()=>t(6957));module.exports=r})();