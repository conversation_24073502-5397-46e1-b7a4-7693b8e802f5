import { NextRequest, NextResponse } from 'next/server';
import { FileManager } from '@/lib/fileUtils';
import { securityService } from '@/lib/security';

export async function POST(request: NextRequest) {
  try {
    // Validate origin
    if (!securityService.validateOrigin(request)) {
      return NextResponse.json(
        { error: 'Invalid origin' },
        { status: 403 }
      );
    }

    // Rate limiting
    const clientIp = request.headers.get('x-forwarded-for') || 'unknown';
    if (!securityService.checkRateLimit(clientIp, 10, 60000)) { // 10 uploads per minute
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Security validation
    const validation = securityService.validateFileUpload(file);
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    const fileManager = new FileManager();

    // Additional file type validation
    if (!fileManager.isValidFileType(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload a supported document format.' },
        { status: 400 }
      );
    }

    // Save file with sanitized name
    const sanitizedFile = new File([file], securityService.sanitizeFilename(file.name), {
      type: file.type,
      lastModified: file.lastModified,
    });

    const fileInfo = await fileManager.saveFile(sanitizedFile);

    return NextResponse.json({
      success: true,
      file: fileInfo,
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const fileManager = new FileManager();
    const files = fileManager.getAllFiles();

    return NextResponse.json({
      success: true,
      files,
    });
  } catch (error) {
    console.error('Get files error:', error);
    return NextResponse.json(
      { error: 'Failed to get files' },
      { status: 500 }
    );
  }
}
