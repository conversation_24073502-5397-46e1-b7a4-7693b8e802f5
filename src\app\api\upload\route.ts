import { NextRequest, NextResponse } from 'next/server';
import { FileManager } from '@/lib/fileUtils';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    const fileManager = new FileManager();

    // Validate file type
    if (!fileManager.isValidFileType(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload a supported document format.' },
        { status: 400 }
      );
    }

    // Check file size (limit to 50MB)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 50MB.' },
        { status: 400 }
      );
    }

    // Save file
    const fileInfo = await fileManager.saveFile(file);

    return NextResponse.json({
      success: true,
      file: fileInfo,
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const fileManager = new FileManager();
    const files = fileManager.getAllFiles();

    return NextResponse.json({
      success: true,
      files,
    });
  } catch (error) {
    console.error('Get files error:', error);
    return NextResponse.json(
      { error: 'Failed to get files' },
      { status: 500 }
    );
  }
}
