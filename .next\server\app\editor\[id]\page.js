(()=>{var e={};e.id=92,e.ids=[92],e.modules={297:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},436:(e,r,t)=>{Promise.resolve().then(t.bind(t,6760))},440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(1658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1571:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=t(5239),o=t(8088),n=t(8170),i=t.n(n),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["editor",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9974)),"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\editor\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\editor\\[id]\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/editor/[id]/page",pathname:"/editor/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2153:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d,viewport:()=>l});var s=t(7413),o=t(2376),n=t.n(o),i=t(8726),a=t.n(i);t(1135);let d={title:"OnlyOffice Demo - Document Collaboration",description:"A professional demo application showcasing OnlyOffice Document Server integration with Next.js for collaborative document editing.",keywords:"OnlyOffice, document editing, collaboration, Next.js, TypeScript",authors:[{name:"OnlyOffice Demo Team"}]},l={width:"device-width",initialScale:1};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} ${a().variable} antialiased`,children:e})})}},4611:()=>{},4859:()=>{},5758:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(687),o=t(3210);class n extends o.Component{constructor(e){super(e),this.handleReset=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("ErrorBoundary caught an error:",e,r),this.setState({hasError:!0,error:e,errorInfo:r}),this.props.onError&&this.props.onError(e,r)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,s.jsx)("div",{className:"min-h-[400px] flex items-center justify-center bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"text-center p-6 max-w-md",children:[(0,s.jsx)("div",{className:"text-red-600 mb-4",children:(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-red-800 mb-2",children:"Something went wrong"}),(0,s.jsx)("p",{className:"text-red-600 mb-4",children:this.state.error?.message||"An unexpected error occurred"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("button",{onClick:this.handleReset,className:"w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Try Again"}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",children:"Reload Page"})]}),!1]})}):this.props.children}}let i=n},6189:(e,r,t)=>{"use strict";var s=t(5773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},6760:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(687),o=t(3210),n=t(6189);function i({fileId:e,mode:r="edit",userId:t="user1",userName:n="User",onError:i,onDocumentReady:a}){let[d,l]=(0,o.useState)(!0),[c,m]=(0,o.useState)(null),u=(0,o.useRef)(null);return((0,o.useRef)(null),c)?(0,s.jsx)("div",{className:"flex items-center justify-center h-full min-h-[400px] bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"text-center p-6",children:[(0,s.jsx)("div",{className:"text-red-600 mb-2",children:(0,s.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-red-800 mb-2",children:"Editor Error"}),(0,s.jsx)("p",{className:"text-red-600",children:c}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:"Reload Page"})]})}):(0,s.jsxs)("div",{className:"relative w-full h-full min-h-[600px]",children:[d&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading document editor..."})]})}),(0,s.jsx)("div",{ref:u,id:`onlyoffice-editor-${e}`,className:"w-full h-full min-h-[600px]"})]})}var a=t(5758);function d({size:e="md",color:r="blue",text:t,className:o=""}){return(0,s.jsxs)("div",{className:`flex flex-col items-center justify-center ${o}`,children:[(0,s.jsx)("div",{className:`animate-spin rounded-full border-2 border-t-transparent ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[e]} ${{blue:"border-blue-600",gray:"border-gray-600",green:"border-green-600",red:"border-red-600",yellow:"border-yellow-600"}[r]}`,role:"status","aria-label":"Loading"}),t&&(0,s.jsx)("p",{className:`mt-2 text-gray-600 ${{sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"}[e]}`,children:t})]})}function l({text:e="Loading..."}){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)(d,{size:"lg",text:e})})}function c(){let e=(0,n.useParams)(),r=(0,n.useRouter)(),t=e.id,[d,c]=(0,o.useState)(null),[m,u]=(0,o.useState)(!0),[x,h]=(0,o.useState)(null),p=()=>{r.push("/")};return m?(0,s.jsx)(l,{text:"Loading document..."}):x||!d?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center p-6",children:[(0,s.jsx)("div",{className:"text-red-600 mb-4",children:(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Document Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:x||"The requested document could not be found."}),(0,s.jsx)("button",{onClick:p,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Back to Documents"})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("button",{onClick:p,className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Documents"]}),(0,s.jsx)("div",{className:"h-6 border-l border-gray-300"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:d.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Last modified: ",new Date(d.lastModified).toLocaleString()]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:d.extension.toUpperCase()}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[(d.size/1024/1024).toFixed(2)," MB"]})]})]})}),(0,s.jsx)("div",{className:"h-[calc(100vh-80px)]",children:(0,s.jsx)(a.A,{onError:(e,r)=>{console.error("Editor Error Boundary:",e,r),h(`Editor Error: ${e.message}`)},children:(0,s.jsx)(i,{fileId:t,mode:"edit",userId:"user1",userName:"Demo User",onError:e=>{h(e)},onDocumentReady:()=>{console.log("Document is ready for editing")}})})})]})}},7284:(e,r,t)=>{Promise.resolve().then(t.bind(t,9974))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9974:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\xampp\\\\htdocs\\\\ai\\\\onlyofice\\\\src\\\\app\\\\editor\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\xampp\\htdocs\\ai\\onlyofice\\src\\app\\editor\\[id]\\page.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,145,658],()=>t(1571));module.exports=s})();