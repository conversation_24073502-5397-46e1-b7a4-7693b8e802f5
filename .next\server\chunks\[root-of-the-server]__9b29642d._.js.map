{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/lib/onlyoffice.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\n\nexport interface OnlyOfficeConfig {\n  documentType: 'word' | 'cell' | 'slide';\n  document: {\n    fileType: string;\n    key: string;\n    title: string;\n    url: string;\n    permissions: {\n      comment: boolean;\n      copy: boolean;\n      download: boolean;\n      edit: boolean;\n      fillForms: boolean;\n      modifyFilter: boolean;\n      modifyContentControl: boolean;\n      review: boolean;\n      print: boolean;\n    };\n  };\n  editorConfig: {\n    mode: 'edit' | 'view';\n    lang: string;\n    callbackUrl: string;\n    user: {\n      id: string;\n      name: string;\n    };\n    customization: {\n      autosave: boolean;\n      forcesave: boolean;\n      comments: boolean;\n      compactHeader: boolean;\n      compactToolbar: boolean;\n      compatibleFeatures: boolean;\n      customer: {\n        address: string;\n        info: string;\n        logo: string;\n        mail: string;\n        name: string;\n        phone: string;\n        www: string;\n      };\n      feedback: {\n        url: string;\n        visible: boolean;\n      };\n      goback: {\n        url: string;\n        text: string;\n      };\n      logo: {\n        image: string;\n        imageEmbedded: string;\n        url: string;\n      };\n      reviewDisplay: string;\n      showReviewChanges: boolean;\n      spellcheck: boolean;\n      toolbarNoTabs: boolean;\n      unit: string;\n      zoom: number;\n    };\n  };\n  width: string;\n  height: string;\n  token?: string; // JWT token for authentication\n}\n\nexport class OnlyOfficeService {\n  private serverUrl: string;\n  private jwtSecret: string;\n\n  constructor() {\n    this.serverUrl = process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080';\n    this.jwtSecret = process.env.ONLYOFFICE_JWT_SECRET || 'fzX5rJRaYYPtns6t'; // Use the actual secret from your setup\n    \n    // Log configuration for debugging\n    console.log('OnlyOffice Service Configuration:', {\n      serverUrl: this.serverUrl,\n      hasJwtSecret: !!this.jwtSecret,\n      jwtEnabled: !!this.jwtSecret && this.jwtSecret !== 'mysecret',\n      jwtDisabled: process.env.ONLYOFFICE_JWT_DISABLED === 'true',\n      jwtSecret: this.jwtSecret === 'fzX5rJRaYYPtns6t' ? 'Using production secret' : 'Using custom secret'\n    });\n  }\n\n  getDocumentType(fileExtension: string): 'word' | 'cell' | 'slide' {\n    const wordFormats = ['doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt', 'html', 'htm', 'mht', 'pdf', 'djvu', 'fb2', 'epub', 'xps'];\n    const cellFormats = ['xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv'];\n    const slideFormats = ['ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp'];\n\n    if (wordFormats.includes(fileExtension.toLowerCase())) {\n      return 'word';\n    } else if (cellFormats.includes(fileExtension.toLowerCase())) {\n      return 'cell';\n    } else if (slideFormats.includes(fileExtension.toLowerCase())) {\n      return 'slide';\n    }\n    \n    return 'word'; // default\n  }\n\n  generateDocumentKey(filename: string, lastModified: number): string {\n    const keyData = `${filename}_${lastModified}_${Date.now()}`;\n    return Buffer.from(keyData).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);\n  }\n\n  createConfig(\n    filename: string,\n    fileUrl: string,\n    callbackUrl: string,\n    userId: string = 'user1',\n    userName: string = 'User',\n    mode: 'edit' | 'view' = 'edit'\n  ): OnlyOfficeConfig {\n    const fileExtension = filename.split('.').pop() || 'docx'; // Default to docx if no extension\n    const documentType = this.getDocumentType(fileExtension);\n    const documentKey = this.generateDocumentKey(filename, Date.now());\n\n    const config: OnlyOfficeConfig = {\n      documentType,\n      document: {\n        fileType: fileExtension,\n        key: documentKey,\n        title: filename,\n        url: fileUrl,\n        permissions: {\n          comment: true,\n          copy: true,\n          download: true,\n          edit: mode === 'edit',\n          fillForms: true,\n          modifyFilter: true,\n          modifyContentControl: true,\n          review: true,\n          print: true,\n        },\n      },\n      editorConfig: {\n        mode,\n        lang: 'en',\n        callbackUrl,\n        user: {\n          id: userId,\n          name: userName,\n        },\n        customization: {\n          autosave: true,\n          forcesave: false,\n          comments: true,\n          compactHeader: false,\n          compactToolbar: false,\n          compatibleFeatures: false,\n          customer: {\n            address: '',\n            info: '',\n            logo: '',\n            mail: '',\n            name: 'OnlyOffice Demo',\n            phone: '',\n            www: '',\n          },\n          feedback: {\n            url: '',\n            visible: false,\n          },\n          goback: {\n            url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n            text: 'Back to Documents',\n          },\n          logo: {\n            image: '',\n            imageEmbedded: '',\n            url: '',\n          },\n          reviewDisplay: 'original',\n          showReviewChanges: false,\n          spellcheck: true,\n          toolbarNoTabs: false,\n          unit: 'cm',\n          zoom: 100,\n        },\n      },\n      width: '100%',\n      height: '100%',\n    };\n\n    return config;\n  }\n\n  signConfig(config: OnlyOfficeConfig): string | null {\n    // Check if JWT is explicitly disabled\n    if (process.env.ONLYOFFICE_JWT_DISABLED === 'true') {\n      console.log('JWT signing explicitly disabled via ONLYOFFICE_JWT_DISABLED environment variable');\n      return null;\n    }\n\n    // For the OnlyOffice server at only.34sy.org, we need to provide JWT\n    if (!this.jwtSecret) {\n      console.log('JWT signing disabled - no secret provided');\n      return null;\n    }\n\n    try {\n      // Create the payload exactly as OnlyOffice expects it\n      const payload = {\n        document: config.document,\n        editorConfig: config.editorConfig,\n        documentType: config.documentType,\n        width: config.width,\n        height: config.height\n      };\n\n      console.log('Signing JWT payload with secret:', this.jwtSecret === 'fzX5rJRaYYPtns6t' ? 'production secret' : 'custom secret');\n\n      // OnlyOffice expects the entire config as payload\n      const token = jwt.sign(payload, this.jwtSecret, { \n        algorithm: 'HS256',\n        expiresIn: '1h' // Add expiration for security\n      });\n\n      console.log('JWT token generated successfully, length:', token.length);\n      return token;\n    } catch (error) {\n      console.error('JWT signing error:', error);\n      return null;\n    }\n  }\n\n  verifyToken(token: string): any {\n    try {\n      return jwt.verify(token, this.jwtSecret);\n    } catch (error) {\n      throw new Error('Invalid JWT token');\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAuEO,MAAM;IACH,UAAkB;IAClB,UAAkB;IAE1B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,qBAAqB,IAAI;QACtD,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,qBAAqB,IAAI,oBAAoB,wCAAwC;QAElH,kCAAkC;QAClC,QAAQ,GAAG,CAAC,qCAAqC;YAC/C,WAAW,IAAI,CAAC,SAAS;YACzB,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS;YAC9B,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK;YACnD,aAAa,QAAQ,GAAG,CAAC,uBAAuB,KAAK;YACrD,WAAW,IAAI,CAAC,SAAS,KAAK,qBAAqB,4BAA4B;QACjF;IACF;IAEA,gBAAgB,aAAqB,EAA6B;QAChE,MAAM,cAAc;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YAAO;YAAO;YAAQ;YAAO;YAAO;YAAO;YAAQ;YAAO;YAAQ;SAAM;QACjK,MAAM,cAAc;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;SAAM;QAC/F,MAAM,eAAe;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;SAAM;QAEzF,IAAI,YAAY,QAAQ,CAAC,cAAc,WAAW,KAAK;YACrD,OAAO;QACT,OAAO,IAAI,YAAY,QAAQ,CAAC,cAAc,WAAW,KAAK;YAC5D,OAAO;QACT,OAAO,IAAI,aAAa,QAAQ,CAAC,cAAc,WAAW,KAAK;YAC7D,OAAO;QACT;QAEA,OAAO,QAAQ,UAAU;IAC3B;IAEA,oBAAoB,QAAgB,EAAE,YAAoB,EAAU;QAClE,MAAM,UAAU,GAAG,SAAS,CAAC,EAAE,aAAa,CAAC,EAAE,KAAK,GAAG,IAAI;QAC3D,OAAO,OAAO,IAAI,CAAC,SAAS,QAAQ,CAAC,UAAU,OAAO,CAAC,iBAAiB,IAAI,SAAS,CAAC,GAAG;IAC3F;IAEA,aACE,QAAgB,EAChB,OAAe,EACf,WAAmB,EACnB,SAAiB,OAAO,EACxB,WAAmB,MAAM,EACzB,OAAwB,MAAM,EACZ;QAClB,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM,QAAQ,kCAAkC;QAC7F,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC;QAC1C,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC,UAAU,KAAK,GAAG;QAE/D,MAAM,SAA2B;YAC/B;YACA,UAAU;gBACR,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,KAAK;gBACL,aAAa;oBACX,SAAS;oBACT,MAAM;oBACN,UAAU;oBACV,MAAM,SAAS;oBACf,WAAW;oBACX,cAAc;oBACd,sBAAsB;oBACtB,QAAQ;oBACR,OAAO;gBACT;YACF;YACA,cAAc;gBACZ;gBACA,MAAM;gBACN;gBACA,MAAM;oBACJ,IAAI;oBACJ,MAAM;gBACR;gBACA,eAAe;oBACb,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,eAAe;oBACf,gBAAgB;oBAChB,oBAAoB;oBACpB,UAAU;wBACR,SAAS;wBACT,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,OAAO;wBACP,KAAK;oBACP;oBACA,UAAU;wBACR,KAAK;wBACL,SAAS;oBACX;oBACA,QAAQ;wBACN,KAAK,iEAAmC;wBACxC,MAAM;oBACR;oBACA,MAAM;wBACJ,OAAO;wBACP,eAAe;wBACf,KAAK;oBACP;oBACA,eAAe;oBACf,mBAAmB;oBACnB,YAAY;oBACZ,eAAe;oBACf,MAAM;oBACN,MAAM;gBACR;YACF;YACA,OAAO;YACP,QAAQ;QACV;QAEA,OAAO;IACT;IAEA,WAAW,MAAwB,EAAiB;QAClD,sCAAsC;QACtC,IAAI,QAAQ,GAAG,CAAC,uBAAuB,KAAK,QAAQ;YAClD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,qEAAqE;QACrE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,IAAI;YACF,sDAAsD;YACtD,MAAM,UAAU;gBACd,UAAU,OAAO,QAAQ;gBACzB,cAAc,OAAO,YAAY;gBACjC,cAAc,OAAO,YAAY;gBACjC,OAAO,OAAO,KAAK;gBACnB,QAAQ,OAAO,MAAM;YACvB;YAEA,QAAQ,GAAG,CAAC,oCAAoC,IAAI,CAAC,SAAS,KAAK,qBAAqB,sBAAsB;YAE9G,kDAAkD;YAClD,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;gBAC9C,WAAW;gBACX,WAAW,KAAK,8BAA8B;YAChD;YAEA,QAAQ,GAAG,CAAC,6CAA6C,MAAM,MAAM;YACrE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO;QACT;IACF;IAEA,YAAY,KAAa,EAAO;QAC9B,IAAI;YACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/lib/fileUtils.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { v4 as uuidv4 } from 'uuid';\n\nexport interface FileInfo {\n  id: string;\n  name: string;\n  size: number;\n  type: string;\n  extension: string;\n  uploadDate: Date;\n  lastModified: Date;\n  url: string;\n}\n\nexport class FileManager {\n  private uploadDir: string;\n\n  constructor() {\n    this.uploadDir = path.join(process.cwd(), 'public', 'uploads');\n    this.ensureUploadDir();\n  }\n\n  private ensureUploadDir(): void {\n    if (!fs.existsSync(this.uploadDir)) {\n      fs.mkdirSync(this.uploadDir, { recursive: true });\n    }\n  }\n\n  async saveFile(file: File): Promise<FileInfo> {\n    const fileId = uuidv4();\n    const extension = path.extname(file.name);\n    const filename = `${fileId}${extension}`;\n    const filepath = path.join(this.uploadDir, filename);\n\n    // Convert File to Buffer\n    const arrayBuffer = await file.arrayBuffer();\n    const buffer = Buffer.from(arrayBuffer);\n\n    // Save file\n    fs.writeFileSync(filepath, buffer);\n\n    const fileInfo: FileInfo = {\n      id: fileId,\n      name: file.name || `document${extension}`, // Ensure we have a proper name\n      size: file.size,\n      type: file.type,\n      extension: extension.slice(1) || 'unknown', // Remove the dot, fallback to 'unknown'\n      uploadDate: new Date(),\n      lastModified: new Date(),\n      url: `/uploads/${filename}`,\n    };\n\n    // Save metadata\n    this.saveFileMetadata(fileInfo);\n\n    return fileInfo;\n  }\n\n  private saveFileMetadata(fileInfo: FileInfo): void {\n    const metadataPath = path.join(this.uploadDir, `${fileInfo.id}.json`);\n    fs.writeFileSync(metadataPath, JSON.stringify(fileInfo, null, 2));\n  }\n\n  getFileMetadata(fileId: string): FileInfo | null {\n    try {\n      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);\n      if (!fs.existsSync(metadataPath)) {\n        return null;\n      }\n      const metadata = fs.readFileSync(metadataPath, 'utf-8');\n      return JSON.parse(metadata);\n    } catch (error) {\n      return null;\n    }\n  }\n\n  getAllFiles(): FileInfo[] {\n    try {\n      const files = fs.readdirSync(this.uploadDir);\n      const metadataFiles = files.filter(file => file.endsWith('.json'));\n      \n      return metadataFiles.map(file => {\n        const metadata = fs.readFileSync(path.join(this.uploadDir, file), 'utf-8');\n        return JSON.parse(metadata);\n      }).sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());\n    } catch (error) {\n      return [];\n    }\n  }\n\n  deleteFile(fileId: string): boolean {\n    try {\n      const fileInfo = this.getFileMetadata(fileId);\n      if (!fileInfo) {\n        return false;\n      }\n\n      const extension = path.extname(fileInfo.name);\n      const filename = `${fileId}${extension}`;\n      const filepath = path.join(this.uploadDir, filename);\n      const metadataPath = path.join(this.uploadDir, `${fileId}.json`);\n\n      // Delete file and metadata\n      if (fs.existsSync(filepath)) {\n        fs.unlinkSync(filepath);\n      }\n      if (fs.existsSync(metadataPath)) {\n        fs.unlinkSync(metadataPath);\n      }\n\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  getFileUrl(fileId: string): string | null {\n    const fileInfo = this.getFileMetadata(fileId);\n    if (!fileInfo) {\n      return null;\n    }\n    \n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';\n    return `${baseUrl}${fileInfo.url}`;\n  }\n\n  isValidFileType(filename: string): boolean {\n    const allowedExtensions = [\n      // Word documents\n      'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt',\n      // Excel spreadsheets\n      'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv',\n      // PowerPoint presentations\n      'ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp',\n      // PDF\n      'pdf'\n    ];\n\n    const extension = path.extname(filename).slice(1).toLowerCase();\n    return allowedExtensions.includes(extension);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAaO,MAAM;IACH,UAAkB;IAE1B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACpD,IAAI,CAAC,eAAe;IACtB;IAEQ,kBAAwB;QAC9B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,GAAG;YAClC,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE;gBAAE,WAAW;YAAK;QACjD;IACF;IAEA,MAAM,SAAS,IAAU,EAAqB;QAC5C,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QACpB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,KAAK,IAAI;QACxC,MAAM,WAAW,GAAG,SAAS,WAAW;QACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAE3C,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,YAAY;QACZ,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU;QAE3B,MAAM,WAAqB;YACzB,IAAI;YACJ,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,WAAW;YACzC,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,WAAW,UAAU,KAAK,CAAC,MAAM;YACjC,YAAY,IAAI;YAChB,cAAc,IAAI;YAClB,KAAK,CAAC,SAAS,EAAE,UAAU;QAC7B;QAEA,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,CAAC;QAEtB,OAAO;IACT;IAEQ,iBAAiB,QAAkB,EAAQ;QACjD,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC,KAAK,CAAC;QACpE,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,cAAc,KAAK,SAAS,CAAC,UAAU,MAAM;IAChE;IAEA,gBAAgB,MAAc,EAAmB;QAC/C,IAAI;YACF,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,OAAO,KAAK,CAAC;YAC/D,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;gBAChC,OAAO;YACT;YACA,MAAM,WAAW,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,cAAc;YAC/C,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,cAA0B;QACxB,IAAI;YACF,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YAC3C,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;YAEzD,OAAO,cAAc,GAAG,CAAC,CAAA;gBACvB,MAAM,WAAW,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO;gBAClE,OAAO,KAAK,KAAK,CAAC;YACpB,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QACrF,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA,WAAW,MAAc,EAAW;QAClC,IAAI;YACF,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;YACtC,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YAEA,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,SAAS,IAAI;YAC5C,MAAM,WAAW,GAAG,SAAS,WAAW;YACxC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC3C,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,OAAO,KAAK,CAAC;YAE/D,2BAA2B;YAC3B,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;gBAC3B,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YAChB;YACA,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;gBAC/B,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YAChB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,WAAW,MAAc,EAAiB;QACxC,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QACtC,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,MAAM,UAAU,iEAAmC;QACnD,OAAO,GAAG,UAAU,SAAS,GAAG,EAAE;IACpC;IAEA,gBAAgB,QAAgB,EAAW;QACzC,MAAM,oBAAoB;YACxB,iBAAiB;YACjB;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YAAO;YAC3E,qBAAqB;YACrB;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAO;YACpE,2BAA2B;YAC3B;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAC7D,MAAM;YACN;SACD;QAED,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,GAAG,WAAW;QAC7D,OAAO,kBAAkB,QAAQ,CAAC;IACpC;AACF", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/lib/security.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\n\nexport class SecurityService {\n  private jwtSecret: string;\n\n  constructor() {\n    this.jwtSecret = process.env.ONLYOFFICE_JWT_SECRET || 'fzX5rJRaYYPtns6t';\n  }\n\n  /**\n   * Validate JWT token from OnlyOffice callback\n   */\n  validateOnlyOfficeToken(token: string): any {\n    try {\n      return jwt.verify(token, this.jwtSecret);\n    } catch (error) {\n      throw new Error('Invalid OnlyOffice JWT token');\n    }\n  }\n\n  /**\n   * Sign data with JWT for OnlyOffice\n   */\n  signOnlyOfficeData(data: any): string {\n    return jwt.sign(data, this.jwtSecret, { algorithm: 'HS256' });\n  }\n\n  /**\n   * Extract and validate JWT from request headers\n   */\n  extractTokenFromRequest(request: NextRequest): string | null {\n    const authHeader = request.headers.get('authorization');\n    if (!authHeader) return null;\n\n    const token = authHeader.replace('Bearer ', '');\n    return token;\n  }\n\n  /**\n   * Validate file upload security\n   */\n  validateFileUpload(file: File): { valid: boolean; error?: string } {\n    // Check file size (50MB limit)\n    const maxSize = 50 * 1024 * 1024;\n    if (file.size > maxSize) {\n      return { valid: false, error: 'File size exceeds 50MB limit' };\n    }\n\n    // Check file type\n    const allowedTypes = [\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx\n      'application/msword', // .doc\n      'application/vnd.ms-excel', // .xls\n      'application/vnd.ms-powerpoint', // .ppt\n      'application/pdf',\n      'text/plain',\n      'text/csv',\n      'application/vnd.oasis.opendocument.text', // .odt\n      'application/vnd.oasis.opendocument.spreadsheet', // .ods\n      'application/vnd.oasis.opendocument.presentation', // .odp\n    ];\n\n    if (!allowedTypes.includes(file.type) && file.type !== '') {\n      // Also check by extension if MIME type is not recognized\n      const extension = file.name.split('.').pop()?.toLowerCase();\n      const allowedExtensions = [\n        'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt',\n        'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv',\n        'ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp',\n        'pdf'\n      ];\n\n      if (!extension || !allowedExtensions.includes(extension)) {\n        return { valid: false, error: 'File type not supported' };\n      }\n    }\n\n    // Check filename for security\n    if (this.containsSuspiciousPatterns(file.name)) {\n      return { valid: false, error: 'Filename contains invalid characters' };\n    }\n\n    return { valid: true };\n  }\n\n  /**\n   * Check for suspicious patterns in filenames\n   */\n  private containsSuspiciousPatterns(filename: string): boolean {\n    const suspiciousPatterns = [\n      /\\.\\./,  // Directory traversal\n      /[<>:\"|?*]/,  // Invalid filename characters\n      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,  // Windows reserved names\n      /^\\./,  // Hidden files\n      /\\.(exe|bat|cmd|scr|vbs|js|jar|com|pif)$/i,  // Executable files\n    ];\n\n    return suspiciousPatterns.some(pattern => pattern.test(filename));\n  }\n\n  /**\n   * Sanitize filename for safe storage\n   */\n  sanitizeFilename(filename: string): string {\n    // Remove or replace unsafe characters\n    return filename\n      .replace(/[^a-zA-Z0-9._-]/g, '_')  // Replace unsafe chars with underscore\n      .replace(/_{2,}/g, '_')  // Replace multiple underscores with single\n      .replace(/^_+|_+$/g, '')  // Remove leading/trailing underscores\n      .substring(0, 255);  // Limit length\n  }\n\n  /**\n   * Generate secure document key\n   */\n  generateSecureDocumentKey(fileId: string, timestamp: number): string {\n    const data = `${fileId}_${timestamp}_${this.jwtSecret}`;\n    return Buffer.from(data).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);\n  }\n\n  /**\n   * Validate request origin\n   */\n  validateOrigin(request: NextRequest): boolean {\n    const origin = request.headers.get('origin');\n    const referer = request.headers.get('referer');\n\n    const allowedOrigins = [\n      'https://only.34sy.org',\n      'http://localhost:3000',\n      'http://localhost:3001',\n      'http://localhost:3002',\n      'http://localhost:3003',\n      'http://127.0.0.1:3000',\n      'http://127.0.0.1:3001',\n      'http://127.0.0.1:3002',\n      'http://127.0.0.1:3003',\n      process.env.NEXT_PUBLIC_APP_URL,\n    ].filter(Boolean);\n\n    // Allow requests without origin (direct API calls, same-origin requests)\n    if (!origin && !referer) return true;\n\n    // Check origin\n    if (origin && allowedOrigins.includes(origin)) return true;\n\n    // Additional check for localhost with any port (development mode)\n    if (origin) {\n      try {\n        const url = new URL(origin);\n        const isLocalhost = url.hostname === 'localhost' || url.hostname === '127.0.0.1';\n        const isLocalPort = parseInt(url.port) >= 3000 && parseInt(url.port) <= 3010;\n        \n        if (isLocalhost && isLocalPort) {\n          console.log('Origin validated for localhost development:', origin);\n          return true;\n        }\n      } catch (error) {\n        console.warn('Error parsing origin URL:', origin, error);\n      }\n    }\n\n    // Check referer as fallback\n    if (referer) {\n      try {\n        const refererUrl = new URL(referer);\n        const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`;\n        \n        if (allowedOrigins.includes(refererOrigin)) return true;\n        \n        // Additional check for localhost referer\n        const isLocalhost = refererUrl.hostname === 'localhost' || refererUrl.hostname === '127.0.0.1';\n        const isLocalPort = parseInt(refererUrl.port) >= 3000 && parseInt(refererUrl.port) <= 3010;\n        \n        if (isLocalhost && isLocalPort) {\n          console.log('Referer validated for localhost development:', refererOrigin);\n          return true;\n        }\n      } catch (error) {\n        console.warn('Error parsing referer URL:', referer, error);\n      }\n    }\n\n    console.warn('Origin validation failed:', { origin, referer, allowedOrigins });\n    return false;\n  }\n\n  /**\n   * Rate limiting check (simple in-memory implementation)\n   */\n  private rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\n  checkRateLimit(identifier: string, maxRequests: number = 100, windowMs: number = 60000): boolean {\n    const now = Date.now();\n    const record = this.rateLimitStore.get(identifier);\n\n    if (!record || now > record.resetTime) {\n      this.rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs });\n      return true;\n    }\n\n    if (record.count >= maxRequests) {\n      return false;\n    }\n\n    record.count++;\n    return true;\n  }\n\n  /**\n   * Clean up expired rate limit records\n   */\n  cleanupRateLimit(): void {\n    const now = Date.now();\n    for (const [key, record] of this.rateLimitStore.entries()) {\n      if (now > record.resetTime) {\n        this.rateLimitStore.delete(key);\n      }\n    }\n  }\n}\n\nexport const securityService = new SecurityService();\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM;IACH,UAAkB;IAE1B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,qBAAqB,IAAI;IACxD;IAEA;;GAEC,GACD,wBAAwB,KAAa,EAAO;QAC1C,IAAI;YACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,mBAAmB,IAAS,EAAU;QACpC,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE;YAAE,WAAW;QAAQ;IAC7D;IAEA;;GAEC,GACD,wBAAwB,OAAoB,EAAiB;QAC3D,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,QAAQ,WAAW,OAAO,CAAC,WAAW;QAC5C,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB,IAAU,EAAsC;QACjE,+BAA+B;QAC/B,MAAM,UAAU,KAAK,OAAO;QAC5B,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBAAE,OAAO;gBAAO,OAAO;YAA+B;QAC/D;QAEA,kBAAkB;QAClB,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI;YACzD,yDAAyD;YACzD,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;YAC9C,MAAM,oBAAoB;gBACxB;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAO;gBAAO;gBAC3E;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAO;gBACpE;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAC7D;aACD;YAED,IAAI,CAAC,aAAa,CAAC,kBAAkB,QAAQ,CAAC,YAAY;gBACxD,OAAO;oBAAE,OAAO;oBAAO,OAAO;gBAA0B;YAC1D;QACF;QAEA,8BAA8B;QAC9B,IAAI,IAAI,CAAC,0BAA0B,CAAC,KAAK,IAAI,GAAG;YAC9C,OAAO;gBAAE,OAAO;gBAAO,OAAO;YAAuC;QACvE;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA;;GAEC,GACD,AAAQ,2BAA2B,QAAgB,EAAW;QAC5D,MAAM,qBAAqB;YACzB;YACA;YACA;YACA;YACA;SACD;QAED,OAAO,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;IACzD;IAEA;;GAEC,GACD,iBAAiB,QAAgB,EAAU;QACzC,sCAAsC;QACtC,OAAO,SACJ,OAAO,CAAC,oBAAoB,KAAM,uCAAuC;SACzE,OAAO,CAAC,UAAU,KAAM,2CAA2C;SACnE,OAAO,CAAC,YAAY,IAAK,sCAAsC;SAC/D,SAAS,CAAC,GAAG,MAAO,eAAe;IACxC;IAEA;;GAEC,GACD,0BAA0B,MAAc,EAAE,SAAiB,EAAU;QACnE,MAAM,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE;QACvD,OAAO,OAAO,IAAI,CAAC,MAAM,QAAQ,CAAC,UAAU,OAAO,CAAC,iBAAiB,IAAI,SAAS,CAAC,GAAG;IACxF;IAEA;;GAEC,GACD,eAAe,OAAoB,EAAW;QAC5C,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEpC,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;;SAED,CAAC,MAAM,CAAC;QAET,yEAAyE;QACzE,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;QAEhC,eAAe;QACf,IAAI,UAAU,eAAe,QAAQ,CAAC,SAAS,OAAO;QAEtD,kEAAkE;QAClE,IAAI,QAAQ;YACV,IAAI;gBACF,MAAM,MAAM,IAAI,IAAI;gBACpB,MAAM,cAAc,IAAI,QAAQ,KAAK,eAAe,IAAI,QAAQ,KAAK;gBACrE,MAAM,cAAc,SAAS,IAAI,IAAI,KAAK,QAAQ,SAAS,IAAI,IAAI,KAAK;gBAExE,IAAI,eAAe,aAAa;oBAC9B,QAAQ,GAAG,CAAC,+CAA+C;oBAC3D,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,6BAA6B,QAAQ;YACpD;QACF;QAEA,4BAA4B;QAC5B,IAAI,SAAS;YACX,IAAI;gBACF,MAAM,aAAa,IAAI,IAAI;gBAC3B,MAAM,gBAAgB,GAAG,WAAW,QAAQ,CAAC,EAAE,EAAE,WAAW,IAAI,EAAE;gBAElE,IAAI,eAAe,QAAQ,CAAC,gBAAgB,OAAO;gBAEnD,yCAAyC;gBACzC,MAAM,cAAc,WAAW,QAAQ,KAAK,eAAe,WAAW,QAAQ,KAAK;gBACnF,MAAM,cAAc,SAAS,WAAW,IAAI,KAAK,QAAQ,SAAS,WAAW,IAAI,KAAK;gBAEtF,IAAI,eAAe,aAAa;oBAC9B,QAAQ,GAAG,CAAC,gDAAgD;oBAC5D,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,8BAA8B,SAAS;YACtD;QACF;QAEA,QAAQ,IAAI,CAAC,6BAA6B;YAAE;YAAQ;YAAS;QAAe;QAC5E,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,iBAAiB,IAAI,MAAoD;IAEjF,eAAe,UAAkB,EAAE,cAAsB,GAAG,EAAE,WAAmB,KAAK,EAAW;QAC/F,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,UAAU,MAAM,OAAO,SAAS,EAAE;YACrC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY;gBAAE,OAAO;gBAAG,WAAW,MAAM;YAAS;YAC1E,OAAO;QACT;QAEA,IAAI,OAAO,KAAK,IAAI,aAAa;YAC/B,OAAO;QACT;QAEA,OAAO,KAAK;QACZ,OAAO;IACT;IAEA;;GAEC,GACD,mBAAyB;QACvB,MAAM,MAAM,KAAK,GAAG;QACpB,KAAK,MAAM,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,GAAI;YACzD,IAAI,MAAM,OAAO,SAAS,EAAE;gBAC1B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YAC7B;QACF;IACF;AACF;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/api/onlyoffice/config/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { OnlyOfficeService } from '@/lib/onlyoffice';\nimport { FileManager } from '@/lib/fileUtils';\nimport { securityService } from '@/lib/security';\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Validate origin\n    if (!securityService.validateOrigin(request)) {\n      return NextResponse.json(\n        { error: 'Invalid origin' },\n        { status: 403 }\n      );\n    }\n\n    // Rate limiting\n    const clientIp = request.headers.get('x-forwarded-for') || 'unknown';\n    if (!securityService.checkRateLimit(`config_${clientIp}`, 20, 60000)) {\n      return NextResponse.json(\n        { error: 'Rate limit exceeded' },\n        { status: 429 }\n      );\n    }\n\n    const { fileId, mode = 'edit', userId = 'user1', userName = 'User' } = await request.json();\n\n    if (!fileId) {\n      return NextResponse.json(\n        { error: 'File ID is required' },\n        { status: 400 }\n      );\n    }\n\n    const fileManager = new FileManager();\n    const fileInfo = fileManager.getFileMetadata(fileId);\n\n    if (!fileInfo) {\n      return NextResponse.json(\n        { error: 'File not found' },\n        { status: 404 }\n      );\n    }\n\n    const onlyOfficeService = new OnlyOfficeService();\n    \n    // Get the current host and port from the request\n    const host = request.headers.get('host') || 'localhost:3001';\n    const protocol = request.headers.get('x-forwarded-proto') || 'http';\n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || `${protocol}://${host}`;\n\n    // Check if we're using a local IP that won't be accessible from OnlyOffice server\n    const isLocalUrl = baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1') || baseUrl.includes('************');\n\n    if (isLocalUrl) {\n      console.warn('⚠️  WARNING: Using local URL that may not be accessible from OnlyOffice server:', baseUrl);\n      console.warn('   Consider using ngrok or setting NEXT_PUBLIC_APP_URL to a public URL');\n    }\n\n    // Use the dedicated file serving endpoint for OnlyOffice\n    const fileUrl = `${baseUrl}/api/files/serve/${fileId}`;\n    const callbackUrl = `${baseUrl}/api/onlyoffice/callback`;\n\n    console.log('Creating OnlyOffice config for:', {\n      fileId,\n      fileName: fileInfo.name,\n      fileUrl,\n      callbackUrl,\n      mode,\n      detectedHost: host,\n      detectedProtocol: protocol\n    });\n\n    // Create OnlyOffice configuration\n    const config = onlyOfficeService.createConfig(\n      fileInfo.name,\n      fileUrl,\n      callbackUrl,\n      userId,\n      userName,\n      mode as 'edit' | 'view'\n    );\n\n    // Sign the configuration with JWT (if JWT is enabled)\n    const token = onlyOfficeService.signConfig(config);\n\n    console.log('OnlyOffice config created successfully:', {\n      documentType: config.documentType,\n      documentKey: config.document.key,\n      hasToken: !!token,\n      jwtEnabled: !!token,\n      jwtDisabled: process.env.ONLYOFFICE_JWT_DISABLED === 'true'\n    });\n\n    // Create the response in the format OnlyOffice expects\n    const responseConfig = {\n      ...config,\n      serverUrl: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',\n    };\n\n    // Add token to the root level if JWT is enabled (OnlyOffice expects it here)\n    if (token) {\n      responseConfig.token = token;\n    }\n\n    console.log('Sending OnlyOffice config:', {\n      hasToken: !!token,\n      configKeys: Object.keys(responseConfig),\n      documentKey: config.document.key,\n      documentUrl: config.document.url,\n      callbackUrl: config.editorConfig.callbackUrl\n    });\n\n    // Return the response in the format the frontend expects\n    return NextResponse.json({\n      success: true,\n      config: responseConfig,\n      token: token, // Keep token at root level for backward compatibility\n      serverUrl: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',\n      jwtEnabled: !!token\n    });\n  } catch (error) {\n    console.error('Config generation error:', error);\n    return NextResponse.json(\n      { \n        success: false,\n        error: 'Failed to generate configuration',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,kBAAkB;QAClB,IAAI,CAAC,wHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,UAAU;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;QAC3D,IAAI,CAAC,wHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,QAAQ;YACpE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,SAAS,OAAO,EAAE,WAAW,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzF,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,IAAI,yHAAA,CAAA,cAAW;QACnC,MAAM,WAAW,YAAY,eAAe,CAAC;QAE7C,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,oBAAoB,IAAI,0HAAA,CAAA,oBAAiB;QAE/C,iDAAiD;QACjD,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,WAAW;QAC5C,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,wBAAwB;QAC7D,MAAM,UAAU,iEAAmC,GAAG,SAAS,GAAG,EAAE,MAAM;QAE1E,kFAAkF;QAClF,MAAM,aAAa,QAAQ,QAAQ,CAAC,gBAAgB,QAAQ,QAAQ,CAAC,gBAAgB,QAAQ,QAAQ,CAAC;QAEtG,IAAI,YAAY;YACd,QAAQ,IAAI,CAAC,mFAAmF;YAChG,QAAQ,IAAI,CAAC;QACf;QAEA,yDAAyD;QACzD,MAAM,UAAU,GAAG,QAAQ,iBAAiB,EAAE,QAAQ;QACtD,MAAM,cAAc,GAAG,QAAQ,wBAAwB,CAAC;QAExD,QAAQ,GAAG,CAAC,mCAAmC;YAC7C;YACA,UAAU,SAAS,IAAI;YACvB;YACA;YACA;YACA,cAAc;YACd,kBAAkB;QACpB;QAEA,kCAAkC;QAClC,MAAM,SAAS,kBAAkB,YAAY,CAC3C,SAAS,IAAI,EACb,SACA,aACA,QACA,UACA;QAGF,sDAAsD;QACtD,MAAM,QAAQ,kBAAkB,UAAU,CAAC;QAE3C,QAAQ,GAAG,CAAC,2CAA2C;YACrD,cAAc,OAAO,YAAY;YACjC,aAAa,OAAO,QAAQ,CAAC,GAAG;YAChC,UAAU,CAAC,CAAC;YACZ,YAAY,CAAC,CAAC;YACd,aAAa,QAAQ,GAAG,CAAC,uBAAuB,KAAK;QACvD;QAEA,uDAAuD;QACvD,MAAM,iBAAiB;YACrB,GAAG,MAAM;YACT,WAAW,QAAQ,GAAG,CAAC,qBAAqB,IAAI;QAClD;QAEA,6EAA6E;QAC7E,IAAI,OAAO;YACT,eAAe,KAAK,GAAG;QACzB;QAEA,QAAQ,GAAG,CAAC,8BAA8B;YACxC,UAAU,CAAC,CAAC;YACZ,YAAY,OAAO,IAAI,CAAC;YACxB,aAAa,OAAO,QAAQ,CAAC,GAAG;YAChC,aAAa,OAAO,QAAQ,CAAC,GAAG;YAChC,aAAa,OAAO,YAAY,CAAC,WAAW;QAC9C;QAEA,yDAAyD;QACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,QAAQ;YACR,OAAO;YACP,WAAW,QAAQ,GAAG,CAAC,qBAAqB,IAAI;YAChD,YAAY,CAAC,CAAC;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}