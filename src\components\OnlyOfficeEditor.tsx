'use client';

import React, { useEffect, useRef, useState } from 'react';

interface OnlyOfficeEditorProps {
  fileId: string;
  mode?: 'edit' | 'view';
  userId?: string;
  userName?: string;
  onError?: (error: string) => void;
  onDocumentReady?: () => void;
}

declare global {
  interface Window {
    DocsAPI: any;
  }
}

export default function OnlyOfficeEditor({
  fileId,
  mode = 'edit',
  userId = 'user1',
  userName = 'User',
  onError,
  onDocumentReady,
}: OnlyOfficeEditorProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  const docEditorRef = useRef<any>(null);

  useEffect(() => {
    const initializeEditor = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Load OnlyOffice API script
        await loadOnlyOfficeAPI();

        // Get configuration from API
        const response = await fetch('/api/onlyoffice/config', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fileId,
            mode,
            userId,
            userName,
          }),
        });

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || 'Failed to get configuration');
        }

        // Initialize OnlyOffice editor
        if (window.DocsAPI && editorRef.current) {
          // Clear any existing editor
          if (docEditorRef.current) {
            try {
              docEditorRef.current.destroyEditor();
            } catch (e) {
              console.warn('Error destroying previous editor:', e);
            }
          }

          // Clear the container
          editorRef.current.innerHTML = '';

          // Prepare the configuration
          const config = {
            ...result.config,
            events: {
              onDocumentReady: () => {
                console.log('OnlyOffice: Document ready');
                setIsLoading(false);
                onDocumentReady?.();
              },
              onError: (event: any) => {
                console.error('OnlyOffice Error Event:', event);
                let errorMessage = 'OnlyOffice Error: ';
                if (typeof event.data === 'string') {
                  errorMessage += event.data;
                } else if (typeof event.data === 'object') {
                  errorMessage += JSON.stringify(event.data);
                } else {
                  errorMessage += 'Unknown error occurred';
                }
                setError(errorMessage);
                onError?.(errorMessage);
                setIsLoading(false);
              },
              onWarning: (event: any) => {
                console.warn('OnlyOffice Warning:', event.data);
              },
              onInfo: (event: any) => {
                console.log('OnlyOffice Info:', event.data);
              },
            },
          };

          // Add JWT token if available
          if (result.token) {
            config.token = result.token;
          }

          // Check if we're using the real OnlyOffice API or mock
          const isRealAPI = window.DocsAPI && !window.DocsAPI.toString().includes('Mock');
          console.log('OnlyOffice API type:', isRealAPI ? 'Real OnlyOffice API' : 'Mock API');
          console.log('window.DocsAPI:', window.DocsAPI);
          console.log('window.DocsAPI.DocEditor:', window.DocsAPI?.DocEditor);

          console.log('Initializing OnlyOffice editor with config:', {
            documentType: config.documentType,
            documentKey: config.document?.key,
            fileUrl: config.document?.url,
            hasToken: !!config.token,
            serverUrl: result.serverUrl,
            apiType: isRealAPI ? 'Real' : 'Mock'
          });

          try {
            if (!window.DocsAPI || typeof window.DocsAPI.DocEditor !== 'function') {
              throw new Error('DocsAPI.DocEditor is not available');
            }

            docEditorRef.current = new window.DocsAPI.DocEditor(editorRef.current.id, config);
            console.log('OnlyOffice editor initialized successfully');
            setIsLoading(false);
          } catch (editorError) {
            console.error('Error initializing OnlyOffice editor:', editorError);
            setError(`Failed to initialize editor: ${editorError}`);
            setIsLoading(false);
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize editor';
        setError(errorMessage);
        onError?.(errorMessage);
        setIsLoading(false);
      }
    };

    if (fileId) {
      initializeEditor();
    }

    return () => {
      if (docEditorRef.current) {
        try {
          docEditorRef.current.destroyEditor();
        } catch (err) {
          console.warn('Error destroying editor:', err);
        }
      }
    };
  }, [fileId, mode, userId, userName]);

  const loadOnlyOfficeAPI = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      // Check if DocsAPI is already available
      if (window.DocsAPI && typeof window.DocsAPI.DocEditor === 'function') {
        console.log('OnlyOffice API already available');
        resolve();
        return;
      }

      console.log('Loading OnlyOffice API...');

      // Try to load the real OnlyOffice API first
      const loadRealAPI = () => {
        return new Promise<void>((resolveReal, rejectReal) => {
          const script = document.createElement('script');
          script.src = 'https://only.34sy.org/web-apps/apps/api/documents/api.js';
          script.type = 'text/javascript';

          const timeoutId = setTimeout(() => {
            console.log('Real API loading timeout, falling back to mock');
            rejectReal(new Error('Timeout loading real API'));
          }, 10000); // 10 second timeout

          script.onload = () => {
            clearTimeout(timeoutId);
            console.log('Real OnlyOffice API script loaded');

            // Check if DocsAPI is available
            if (window.DocsAPI && typeof window.DocsAPI.DocEditor === 'function') {
              console.log('Real OnlyOffice API is ready');
              resolveReal();
            } else {
              console.log('Real OnlyOffice API loaded but DocsAPI not available');
              rejectReal(new Error('DocsAPI not available'));
            }
          };

          script.onerror = () => {
            clearTimeout(timeoutId);
            console.log('Failed to load real OnlyOffice API');
            rejectReal(new Error('Failed to load real API'));
          };

          document.head.appendChild(script);
        });
      };

      // Try real API first, then fall back to mock
      loadRealAPI()
        .then(() => {
          console.log('Successfully loaded real OnlyOffice API');
          resolve();
        })
        .catch((error) => {
          console.log('Real API failed, loading mock API:', error.message);
          loadMockAPI().then(resolve).catch(reject);
        });
    });
  };

  const loadMockAPI = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      console.log('Loading OnlyOffice mock API as fallback...');
      const mockScript = document.createElement('script');
      mockScript.src = '/onlyoffice-api-mock.js';
      mockScript.async = true;

      mockScript.onload = () => {
        console.log('OnlyOffice Mock API loaded successfully');
        if (window.DocsAPI) {
          resolve();
        } else {
          reject(new Error('Failed to load OnlyOffice API (including mock)'));
        }
      };

      mockScript.onerror = () => {
        reject(new Error('Failed to load OnlyOffice API from all available sources'));
      };

      document.head.appendChild(mockScript);
    });
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-full min-h-[400px] bg-red-50 border border-red-200 rounded-lg">
        <div className="text-center p-6">
          <div className="text-red-600 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-red-800 mb-2">Editor Error</h3>
          <p className="text-red-600">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full min-h-[600px]">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading document editor...</p>
          </div>
        </div>
      )}
      <div
        ref={editorRef}
        id={`onlyoffice-editor-${fileId}`}
        className="w-full h-full min-h-[600px]"
      />
    </div>
  );
}
