'use client';

import React, { useEffect, useRef, useState } from 'react';

interface OnlyOfficeEditorProps {
  fileId: string;
  mode?: 'edit' | 'view';
  userId?: string;
  userName?: string;
  onError?: (error: string) => void;
  onDocumentReady?: () => void;
}

declare global {
  interface Window {
    DocsAPI: any;
  }
}

export default function OnlyOfficeEditor({
  fileId,
  mode = 'edit',
  userId = 'user1',
  userName = 'User',
  onError,
  onDocumentReady,
}: OnlyOfficeEditorProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  const docEditorRef = useRef<any>(null);

  useEffect(() => {
    const initializeEditor = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Load OnlyOffice API script
        await loadOnlyOfficeAPI();

        // Get configuration from API
        const response = await fetch('/api/onlyoffice/config', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fileId,
            mode,
            userId,
            userName,
          }),
        });

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || 'Failed to get configuration');
        }

        // Initialize OnlyOffice editor
        if (window.DocsAPI && editorRef.current) {
          // Clear any existing editor
          if (docEditorRef.current) {
            docEditorRef.current.destroyEditor();
          }

          // Clear the container
          editorRef.current.innerHTML = '';

          const config = {
            ...result.config,
            token: result.token,
            events: {
              onDocumentReady: () => {
                setIsLoading(false);
                onDocumentReady?.();
              },
              onError: (event: any) => {
                const errorMessage = `OnlyOffice Error: ${event.data}`;
                setError(errorMessage);
                onError?.(errorMessage);
                setIsLoading(false);
              },
              onWarning: (event: any) => {
                console.warn('OnlyOffice Warning:', event.data);
              },
            },
          };

          docEditorRef.current = new window.DocsAPI.DocEditor(editorRef.current.id, config);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize editor';
        setError(errorMessage);
        onError?.(errorMessage);
        setIsLoading(false);
      }
    };

    if (fileId) {
      initializeEditor();
    }

    return () => {
      if (docEditorRef.current) {
        try {
          docEditorRef.current.destroyEditor();
        } catch (err) {
          console.warn('Error destroying editor:', err);
        }
      }
    };
  }, [fileId, mode, userId, userName]);

  const loadOnlyOfficeAPI = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (window.DocsAPI) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://only.34sy.org/web-apps/apps/api/documents/api.js`;
      script.async = true;
      script.onload = () => {
        if (window.DocsAPI) {
          resolve();
        } else {
          reject(new Error('OnlyOffice API failed to load'));
        }
      };
      script.onerror = () => {
        reject(new Error('Failed to load OnlyOffice API script'));
      };

      document.head.appendChild(script);
    });
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-full min-h-[400px] bg-red-50 border border-red-200 rounded-lg">
        <div className="text-center p-6">
          <div className="text-red-600 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-red-800 mb-2">Editor Error</h3>
          <p className="text-red-600">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full min-h-[600px]">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading document editor...</p>
          </div>
        </div>
      )}
      <div
        ref={editorRef}
        id={`onlyoffice-editor-${fileId}`}
        className="w-full h-full min-h-[600px]"
      />
    </div>
  );
}
