{"description": "Final OnlyOffice Docker Configuration for JWT Fix", "environment": {"ONLYOFFICE_SERVER_URL": "http://localhost:8080", "ONLYOFFICE_JWT_SECRET": "fzX5rJRaYYPtns6t", "NEXT_PUBLIC_APP_URL": "http://*************:3002", "NODE_ENV": "development", "PORT": "3002"}, "docker": {"onlyoffice_command": "docker run -d --name onlyoffice -p 8080:80 -e JWT_ENABLED=true -e JWT_SECRET=fzX5rJRaYYPtns6t onlyoffice/documentserver", "restart_command": "docker restart onlyoffice"}, "nextjs": {"dev_command": "npm run dev", "expected_port": 3002, "expected_network": "http://************:3002 or http://*************:3002"}, "expected_logs": {"jwt_enabled": true, "port_detected": "3002", "file_urls": "http://*************:3002/api/files/serve/...", "callback_urls": "http://*************:3002/api/onlyoffice/callback"}, "troubleshooting": {"issue_1": "JWT still disabled", "solution_1": "Check ONLYOFFICE_JWT_SECRET environment variable is set", "issue_2": "Port mismatch (3001 vs 3002)", "solution_2": "Update NEXT_PUBLIC_APP_URL to match actual running port", "issue_3": "OnlyOffice server rejects JWT", "solution_3": "Ensure Docker container uses same JWT_SECRET"}}