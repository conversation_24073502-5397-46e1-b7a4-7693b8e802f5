import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable experimental features for better performance
  experimental: {
    optimizePackageImports: ['@/components', '@/lib'],
  },

  // Allow cross-origin requests for development
  allowedDevOrigins: ['172.29.240.1:3001'],

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: 'https://only.34sy.org',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization, X-Requested-With',
          },
        ],
      },
    ];
  },

  // Rewrites for OnlyOffice integration
  async rewrites() {
    return [
      {
        source: '/onlyoffice/:path*',
        destination: 'https://only.34sy.org/:path*',
      },
    ];
  },

  // Image optimization settings
  images: {
    domains: ['only.34sy.org'],
    formats: ['image/webp', 'image/avif'],
  },

  // Webpack configuration for better bundling
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }
    return config;
  },

  // Environment variables validation
  env: {
    ONLYOFFICE_SERVER_URL: process.env.ONLYOFFICE_SERVER_URL,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
  },

  // Output configuration for deployment
  output: 'standalone',

  // Compression
  compress: true,

  // Power by header removal for security
  poweredByHeader: false,
};

export default nextConfig;
