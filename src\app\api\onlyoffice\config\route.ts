import { NextRequest, NextResponse } from 'next/server';
import { OnlyOfficeService } from '@/lib/onlyoffice';
import { FileManager } from '@/lib/fileUtils';

export async function POST(request: NextRequest) {
  try {
    const { fileId, mode = 'edit', userId = 'user1', userName = 'User' } = await request.json();

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      );
    }

    const fileManager = new FileManager();
    const fileInfo = fileManager.getFileMetadata(fileId);

    if (!fileInfo) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    const onlyOfficeService = new OnlyOfficeService();
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const fileUrl = `${baseUrl}${fileInfo.url}`;
    const callbackUrl = `${baseUrl}/api/onlyoffice/callback`;

    // Create OnlyOffice configuration
    const config = onlyOfficeService.createConfig(
      fileInfo.name,
      fileUrl,
      callbackUrl,
      userId,
      userName,
      mode as 'edit' | 'view'
    );

    // Sign the configuration with JWT
    const token = onlyOfficeService.signConfig(config);

    return NextResponse.json({
      success: true,
      config,
      token,
      serverUrl: process.env.ONLYOFFICE_SERVER_URL || 'https://only.34sy.org',
    });
  } catch (error) {
    console.error('Config generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate configuration' },
      { status: 500 }
    );
  }
}
