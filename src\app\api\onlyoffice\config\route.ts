import { NextRequest, NextResponse } from 'next/server';
import { OnlyOfficeService } from '@/lib/onlyoffice';
import { FileManager } from '@/lib/fileUtils';
import { securityService } from '@/lib/security';

export async function POST(request: NextRequest) {
  try {
    // Validate origin
    if (!securityService.validateOrigin(request)) {
      return NextResponse.json(
        { error: 'Invalid origin' },
        { status: 403 }
      );
    }

    // Rate limiting
    const clientIp = request.headers.get('x-forwarded-for') || 'unknown';
    if (!securityService.checkRateLimit(`config_${clientIp}`, 20, 60000)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      );
    }

    const { fileId, mode = 'edit', userId = 'user1', userName = 'User' } = await request.json();

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      );
    }

    const fileManager = new FileManager();
    const fileInfo = fileManager.getFileMetadata(fileId);

    if (!fileInfo) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    const onlyOfficeService = new OnlyOfficeService();
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001';

    // Check if we're using a local IP that won't be accessible from OnlyOffice server
    const isLocalUrl = baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1') || baseUrl.includes('************');

    if (isLocalUrl) {
      console.warn('⚠️  WARNING: Using local URL that may not be accessible from OnlyOffice server:', baseUrl);
      console.warn('   Consider using ngrok or setting NEXT_PUBLIC_APP_URL to a public URL');
    }

    // Use the dedicated file serving endpoint for OnlyOffice
    const fileUrl = `${baseUrl}/api/files/serve/${fileId}`;
    const callbackUrl = `${baseUrl}/api/onlyoffice/callback`;

    console.log('Creating OnlyOffice config for:', {
      fileId,
      fileName: fileInfo.name,
      fileUrl,
      callbackUrl,
      mode
    });

    // Create OnlyOffice configuration
    const config = onlyOfficeService.createConfig(
      fileInfo.name,
      fileUrl,
      callbackUrl,
      userId,
      userName,
      mode as 'edit' | 'view'
    );

    // Sign the configuration with JWT (if JWT is enabled)
    const token = onlyOfficeService.signConfig(config);

    console.log('OnlyOffice config created successfully:', {
      documentType: config.documentType,
      documentKey: config.document.key,
      hasToken: !!token,
      jwtEnabled: !!token
    });

    // Add server URL to config for client-side use
    const responseConfig = {
      ...config,
      serverUrl: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',
    };

    return NextResponse.json({
      success: true,
      config: responseConfig,
      token,
      serverUrl: process.env.ONLYOFFICE_SERVER_URL || 'http://localhost:8080',
    });
  } catch (error) {
    console.error('Config generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate configuration' },
      { status: 500 }
    );
  }
}
