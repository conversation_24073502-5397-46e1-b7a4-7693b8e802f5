import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing OnlyOffice server accessibility...');
    
    // Test if the OnlyOffice server is accessible
    const response = await fetch('https://only.34sy.org/web-apps/apps/api/documents/api.js', {
      method: 'HEAD',
      headers: {
        'User-Agent': 'OnlyOffice-Test-Client'
      }
    });

    console.log('OnlyOffice server response:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    });

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: 'OnlyOffice server is accessible',
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'OnlyOffice server returned error',
        status: response.status,
        statusText: response.statusText
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error testing OnlyOffice server:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to connect to OnlyOffice server',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
