{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/editor/:path*{(\\\\.json)}?", "originalSource": "/editor/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/uploads/:path*{(\\\\.json)}?", "originalSource": "/uploads/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FHbgEg/LvL83bAMQlaTJWLma3/iCdTl+r2H1vdHGiqc=", "__NEXT_PREVIEW_MODE_ID": "1d3005baf1f0fe377c27e911aea3781c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "df9a8f37d23b8faab9c0250236a8ce9f3e2c7e909752ed1a597f0d41cf85013b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "05bc22fdb931f2ad9a18146408778e3971076cf2fbe2792d8d4efea0e06f81d1"}}}, "instrumentation": null, "functions": {}}