{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/editor/:path*{(\\\\.json)}?", "originalSource": "/editor/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/uploads/:path*{(\\\\.json)}?", "originalSource": "/uploads/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FHbgEg/LvL83bAMQlaTJWLma3/iCdTl+r2H1vdHGiqc=", "__NEXT_PREVIEW_MODE_ID": "01af97470254f7d0195d8cbe381b2b23", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ca1ed9ad2e9779cb53072413e6e52280a6c85b030b3879e5bae3812a75e2d2f5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bb60f949a2c368ac205f52f90a6d404dd5ae07cb17e9c44746f96ee56de8ce73"}}}, "instrumentation": null, "functions": {}}