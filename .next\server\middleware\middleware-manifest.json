{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/editor/:path*{(\\\\.json)}?", "originalSource": "/editor/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FHbgEg/LvL83bAMQlaTJWLma3/iCdTl+r2H1vdHGiqc=", "__NEXT_PREVIEW_MODE_ID": "3feb84e3cf6e3c7e7ee42f743f044ec0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c8a2d4028b98f1406beaca7a4447dd3ddbebc8a4cdc2d3075e5498bb0193d1d1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d38817f2374ef5270d43c84b166141b78a8849d99ef1e6839aa5fcca3f375f4b"}}}, "instrumentation": null, "functions": {}}