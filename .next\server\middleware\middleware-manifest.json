{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/editor/:path*{(\\\\.json)}?", "originalSource": "/editor/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/uploads/:path*{(\\\\.json)}?", "originalSource": "/uploads/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FHbgEg/LvL83bAMQlaTJWLma3/iCdTl+r2H1vdHGiqc=", "__NEXT_PREVIEW_MODE_ID": "ef539c898b0000d14813ee1f8dd2ea5f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "73b14eebbff0eecec840e8a6fc678f1b921ab25d324ba744bca1e6ee0e2cb1b8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0023783666729d6d1e9b8f4bb749370a87b7dbb5ab7b9ffc4ac99c9db57cc471"}}}, "instrumentation": null, "functions": {}}