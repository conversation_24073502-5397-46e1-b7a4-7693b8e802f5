{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/editor/:path*{(\\\\.json)}?", "originalSource": "/editor/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FHbgEg/LvL83bAMQlaTJWLma3/iCdTl+r2H1vdHGiqc=", "__NEXT_PREVIEW_MODE_ID": "58d21545824556f69c24c3616ab4ec2d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6bc7d73ccb57c0bb88de5e3153d802a9eac4fe614509525f624677f7be65a054", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b1ffc50c9dced367d9674e9ec7e899b48e8b160c1c7c9bcf8da49360cc417ac7"}}}, "instrumentation": null, "functions": {}}