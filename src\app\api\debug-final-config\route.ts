import { NextRequest, NextResponse } from 'next/server';
import { OnlyOfficeService } from '@/lib/onlyoffice';

export async function GET(request: NextRequest) {
  try {
    // Get current configuration
    const onlyOfficeService = new OnlyOfficeService();
    
    // Get request details
    const host = request.headers.get('host') || 'localhost:3001';
    const protocol = request.headers.get('x-forwarded-proto') || 'http';
    const currentUrl = `${protocol}://${host}`;
    
    // Environment check
    const envConfig = {
      ONLYOFFICE_SERVER_URL: process.env.ONLYOFFICE_SERVER_URL || 'NOT SET',
      ONLYOFFICE_JWT_SECRET: process.env.ONLYOFFICE_JWT_SECRET ? 'SET (length: ' + process.env.ONLYOFFICE_JWT_SECRET.length + ')' : 'NOT SET',
      ONLYOFFICE_JWT_DISABLED: process.env.ONLYOFFICE_JWT_DISABLED || 'NOT SET',
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'NOT SET',
      NODE_ENV: process.env.NODE_ENV || 'NOT SET',
      PORT: process.env.PORT || 'NOT SET'
    };
    
    // Port analysis
    const detectedPort = host.split(':')[1] || '3000';
    const expectedPort = '3002';
    const portMismatch = detectedPort !== expectedPort;
    
    // URL analysis
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || currentUrl;
    const expectedFileUrl = `${baseUrl}/api/files/serve/test-file-id`;
    const expectedCallbackUrl = `${baseUrl}/api/onlyoffice/callback`;
    
    // JWT analysis
    const hasJwtSecret = !!process.env.ONLYOFFICE_JWT_SECRET;
    const jwtSecretCorrect = process.env.ONLYOFFICE_JWT_SECRET === 'fzX5rJRaYYPtns6t';
    const jwtDisabled = process.env.ONLYOFFICE_JWT_DISABLED === 'true';
    
    // Create test config to see actual behavior
    let testConfig = null;
    let jwtToken = null;
    try {
      testConfig = onlyOfficeService.createConfig(
        'test.docx',
        expectedFileUrl,
        expectedCallbackUrl,
        'debug-user',
        'Debug User',
        'edit'
      );
      jwtToken = onlyOfficeService.signConfig(testConfig);
    } catch (error) {
      console.error('Error creating test config:', error);
    }
    
    // Issues detection
    const issues = [];
    const fixes = [];
    
    if (!hasJwtSecret) {
      issues.push('JWT secret not set');
      fixes.push('Set ONLYOFFICE_JWT_SECRET=fzX5rJRaYYPtns6t in .env.local');
    } else if (!jwtSecretCorrect) {
      issues.push('JWT secret incorrect');
      fixes.push('Update ONLYOFFICE_JWT_SECRET to fzX5rJRaYYPtns6t');
    }
    
    if (portMismatch) {
      issues.push(`Port mismatch: detected ${detectedPort}, expected ${expectedPort}`);
      fixes.push(`Update NEXT_PUBLIC_APP_URL to http://*************:${detectedPort}`);
    }
    
    if (!jwtToken && !jwtDisabled) {
      issues.push('JWT token not generated despite having secret');
      fixes.push('Check JWT secret configuration and ensure it matches OnlyOffice server');
    }
    
    if (baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1')) {
      issues.push('Using localhost URL - OnlyOffice server cannot access');
      fixes.push('Use ngrok or update NEXT_PUBLIC_APP_URL to machine IP address');
    }
    
    const debugInfo = {
      timestamp: new Date().toISOString(),
      request: {
        currentUrl,
        host,
        protocol,
        detectedPort
      },
      environment: envConfig,
      ports: {
        detected: detectedPort,
        expected: expectedPort,
        mismatch: portMismatch
      },
      urls: {
        base: baseUrl,
        fileServing: expectedFileUrl,
        callback: expectedCallbackUrl
      },
      jwt: {
        hasSecret: hasJwtSecret,
        secretCorrect: jwtSecretCorrect,
        disabled: jwtDisabled,
        tokenGenerated: !!jwtToken,
        tokenLength: jwtToken ? jwtToken.length : 0
      },
      testConfig: testConfig ? {
        documentType: testConfig.documentType,
        documentKey: testConfig.document.key,
        fileUrl: testConfig.document.url,
        callbackUrl: testConfig.editorConfig.callbackUrl
      } : null,
      issues,
      fixes,
      status: issues.length === 0 ? 'READY' : 'NEEDS_FIXES'
    };
    
    return NextResponse.json({
      success: true,
      debug: debugInfo,
      summary: {
        totalIssues: issues.length,
        status: issues.length === 0 ? 'Configuration is correct' : 'Configuration needs fixes',
        criticalIssues: issues.filter(issue => 
          issue.includes('JWT') || 
          issue.includes('port') || 
          issue.includes('localhost')
        ).length
      }
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      debug: {
        timestamp: new Date().toISOString(),
        status: 'ERROR'
      }
    }, { status: 500 });
  }
} 