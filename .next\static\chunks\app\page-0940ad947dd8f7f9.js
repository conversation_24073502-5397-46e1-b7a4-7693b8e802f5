(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5080:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(5155),l=t(2115);class a extends l.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s),this.setState({hasError:!0,error:e,errorInfo:s}),this.props.onError&&this.props.onError(e,s)}render(){if(this.state.hasError){var e;return this.props.fallback?this.props.fallback:(0,r.jsx)("div",{className:"min-h-[400px] flex items-center justify-center bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-center p-6 max-w-md",children:[(0,r.jsx)("div",{className:"text-red-600 mb-4",children:(0,r.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-red-800 mb-2",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-red-600 mb-4",children:(null==(e=this.state.error)?void 0:e.message)||"An unexpected error occurred"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("button",{onClick:this.handleReset,className:"w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Try Again"}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",children:"Reload Page"})]}),!1]})})}return this.props.children}constructor(e){super(e),this.handleReset=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.state={hasError:!1}}}let o=a},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},6200:(e,s,t)=>{Promise.resolve().then(t.bind(t,7031))},7031:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(5155),l=t(2115);function a(e){let{onUploadSuccess:s,onUploadError:t}=e,[a,o]=(0,l.useState)(!1),[n,i]=(0,l.useState)(!1),d=(0,l.useRef)(null),c=async e=>{if(0===e.length)return;let r=e[0];o(!0);try{let e=new FormData;e.append("file",r);let l=await fetch("/api/upload",{method:"POST",body:e}),a=await l.json();a.success?s(a.file):t(a.error||"Upload failed")}catch(e){t("Upload failed. Please try again.")}finally{o(!1)}},x=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?i(!0):"dragleave"===e.type&&i(!1)};return(0,r.jsx)("div",{className:"w-full",children:(0,r.jsxs)("div",{className:"relative border-2 border-dashed rounded-lg p-6 transition-colors ".concat(n?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"," ").concat(a?"opacity-50 pointer-events-none":""),onDragEnter:x,onDragLeave:x,onDragOver:x,onDrop:e=>{e.preventDefault(),e.stopPropagation(),i(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&c(e.dataTransfer.files)},children:[(0,r.jsx)("input",{ref:d,type:"file",className:"hidden",accept:".doc,.docx,.docm,.dot,.dotx,.dotm,.odt,.fodt,.ott,.rtf,.txt,.xls,.xlsx,.xlsm,.xlt,.xltx,.xltm,.ods,.fods,.ots,.csv,.ppt,.pptx,.pptm,.pot,.potx,.potm,.odp,.fodp,.otp,.pdf",onChange:e=>{e.preventDefault(),e.target.files&&e.target.files[0]&&c(e.target.files)},disabled:a}),(0,r.jsx)("div",{className:"text-center",children:a?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400 mb-4",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,r.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),(0,r.jsxs)("div",{className:"flex text-sm text-gray-600",children:[(0,r.jsx)("label",{className:"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500",children:(0,r.jsx)("span",{children:"Upload a file"})}),(0,r.jsx)("p",{className:"pl-1",children:"or drag and drop"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Word, Excel, PowerPoint, or PDF files up to 50MB"}),(0,r.jsx)("button",{type:"button",onClick:()=>{var e;null==(e=d.current)||e.click()},className:"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Choose File"})]})})]})})}var o=t(5695);function n(e){let{file:s,onDelete:t}=e,a=(0,o.useRouter)(),[n,i]=(0,l.useState)(!1),[d,c]=(0,l.useState)(!1),x=async()=>{i(!0);try{(await fetch("/api/files/".concat(s.id),{method:"DELETE"})).ok?t(s.id):console.error("Failed to delete file")}catch(e){console.error("Error deleting file:",e)}finally{i(!1),c(!1)}};return(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(e=>{let s=e.toLowerCase();return["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt"].includes(s)?(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z",clipRule:"evenodd"})})}):["xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv"].includes(s)?(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z",clipRule:"evenodd"})})}):["ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp"].includes(s)?(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-orange-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})})}):"pdf"===s?(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z",clipRule:"evenodd"})})}):(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z",clipRule:"evenodd"})})})})(s.extension),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",title:s.name,children:s.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]})(s.size)," • ",s.extension.toUpperCase()]}),(0,r.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Modified: ",new Date(s.lastModified).toLocaleDateString()]})]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("button",{onClick:()=>{a.push("/editor/".concat(s.id))},className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Edit document",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,r.jsx)("button",{onClick:()=>{a.push("/editor/".concat(s.id,"?mode=view"))},className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"View document",children:(0,r.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})}),(0,r.jsx)("button",{onClick:()=>{c(!0)},disabled:n,className:"p-1 text-gray-400 hover:text-red-600 transition-colors disabled:opacity-50",title:"Delete document",children:n?(0,r.jsx)("div",{className:"w-4 h-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent"}):(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})})]}),d&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-sm mx-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Delete Document"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:['Are you sure you want to delete "',s.name,'"? This action cannot be undone.']}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("button",{onClick:x,disabled:n,className:"flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors",children:n?"Deleting...":"Delete"}),(0,r.jsx)("button",{onClick:()=>{c(!1)},disabled:n,className:"flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 disabled:opacity-50 transition-colors",children:"Cancel"})]})]})})]})}function i(e){let s,{onRefresh:t,refreshTrigger:a}=e,[o,i]=(0,l.useState)([]),[d,c]=(0,l.useState)(!0),[x,m]=(0,l.useState)(null),[u,h]=(0,l.useState)("date"),[p,f]=(0,l.useState)("desc"),[g,b]=(0,l.useState)("all"),j=async()=>{try{c(!0),m(null);let e=await fetch("/api/upload"),s=await e.json();s.success?i(s.files):m(s.error||"Failed to load files")}catch(e){m("Failed to load files")}finally{c(!1)}};(0,l.useEffect)(()=>{j()},[a]);let v=e=>{i(o.filter(s=>s.id!==e)),null==t||t()},y=e=>{u===e?f("asc"===p?"desc":"asc"):(h(e),f("desc"))},N=(s=o,"all"!==g&&(s=o.filter(e=>{let s=e.extension.toLowerCase();switch(g){case"word":return["doc","docx","docm","dot","dotx","dotm","odt","fodt","ott","rtf","txt"].includes(s);case"excel":return["xls","xlsx","xlsm","xlt","xltx","xltm","ods","fods","ots","csv"].includes(s);case"powerpoint":return["ppt","pptx","pptm","pot","potx","potm","odp","fodp","otp"].includes(s);case"pdf":return"pdf"===s;default:return!0}})),s.sort((e,s)=>{let t=0;switch(u){case"name":t=e.name.localeCompare(s.name);break;case"date":t=new Date(e.lastModified).getTime()-new Date(s.lastModified).getTime();break;case"size":t=e.size-s.size}return"asc"===p?t:-t}),s);return d?(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading documents..."})]})}):x?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-red-600 mb-4",children:(0,r.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Documents"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:x}),(0,r.jsx)("button",{onClick:j,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Try Again"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Filter:"}),(0,r.jsxs)("select",{value:g,onChange:e=>b(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Documents"}),(0,r.jsx)("option",{value:"word",children:"Word Documents"}),(0,r.jsx)("option",{value:"excel",children:"Excel Spreadsheets"}),(0,r.jsx)("option",{value:"powerpoint",children:"PowerPoint Presentations"}),(0,r.jsx)("option",{value:"pdf",children:"PDF Files"})]})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,r.jsxs)("button",{onClick:()=>y("name"),className:"px-3 py-1 text-sm rounded-md transition-colors ".concat("name"===u?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100"),children:["Name ","name"===u&&("asc"===p?"↑":"↓")]}),(0,r.jsxs)("button",{onClick:()=>y("date"),className:"px-3 py-1 text-sm rounded-md transition-colors ".concat("date"===u?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100"),children:["Date ","date"===u&&("asc"===p?"↑":"↓")]}),(0,r.jsxs)("button",{onClick:()=>y("size"),className:"px-3 py-1 text-sm rounded-md transition-colors ".concat("size"===u?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100"),children:["Size ","size"===u&&("asc"===p?"↑":"↓")]})]})]}),0===N.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("svg",{className:"w-16 h-16 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Documents Found"}),(0,r.jsx)("p",{className:"text-gray-600",children:"all"===g?"Upload your first document to get started.":"No ".concat(g," documents found. Try changing the filter or upload a new document.")})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",children:N.map(e=>(0,r.jsx)(n,{file:e,onDelete:v},e.id))}),N.length>0&&(0,r.jsxs)("div",{className:"text-center text-sm text-gray-500 pt-4",children:["Showing ",N.length," of ",o.length," documents"]})]})}var d=t(5080);function c(e){let{isOpen:s,onClose:t,onCreateSuccess:a,onCreateError:o}=e,[n,i]=(0,l.useState)(""),[d,c]=(0,l.useState)("excel"),[x,m]=(0,l.useState)(!1),u=async()=>{if(!n.trim())return void o("Please enter a file name");m(!0);try{let e=await fetch("/api/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fileName:n.trim(),fileType:d})}),s=await e.json();s.success?(a(s.file),i(""),c("excel"),t()):o(s.error||"Failed to create file")}catch(e){o("Failed to create file. Please try again.")}finally{m(!1)}},h=()=>{x||(i(""),c("excel"),t())};return s?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Create New Document"}),(0,r.jsx)("button",{onClick:h,disabled:x,className:"text-gray-400 hover:text-gray-600 disabled:opacity-50",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"fileName",className:"block text-sm font-medium text-gray-700 mb-2",children:"File Name"}),(0,r.jsx)("input",{type:"text",id:"fileName",value:n,onChange:e=>i(e.target.value),placeholder:"Enter file name",disabled:x,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"fileType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Document Type"}),(0,r.jsxs)("select",{id:"fileType",value:d,onChange:e=>c(e.target.value),disabled:x,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed",children:[(0,r.jsx)("option",{value:"excel",children:"Excel Spreadsheet (.xlsx)"}),(0,r.jsx)("option",{value:"word",children:"Word Document (.docx)"}),(0,r.jsx)("option",{value:"powerpoint",children:"PowerPoint Presentation (.pptx)"})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-md p-3",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-500 mt-0.5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{className:"font-medium",children:"Creating a new document will:"}),(0,r.jsxs)("ul",{className:"mt-1 list-disc list-inside space-y-1",children:[(0,r.jsxs)("li",{children:["Generate a blank ",d," document"]}),(0,r.jsx)("li",{children:"Open it in the OnlyOffice editor"}),(0,r.jsx)("li",{children:"Allow you to start editing immediately"})]})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200",children:[(0,r.jsx)("button",{onClick:h,disabled:x,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),(0,r.jsx)("button",{onClick:u,disabled:x||!n.trim(),className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Creating..."]}):"Create Document"})]})]})}):null}function x(){let[e,s]=(0,l.useState)(0),[t,o]=(0,l.useState)(!1),[n,x]=(0,l.useState)(!1),[m,u]=(0,l.useState)(null),h=()=>{s(e=>e+1)};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"OnlyOffice Demo"})}),(0,r.jsx)("div",{className:"hidden md:block ml-6",children:(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Collaborative document editing with OnlyOffice Document Server"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:h,className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Refresh"]}),(0,r.jsxs)("button",{onClick:()=>x(!0),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Create New"]}),(0,r.jsxs)("button",{onClick:()=>o(!t),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Upload Document"]})]})]})})}),m&&(0,r.jsx)("div",{className:"fixed top-4 right-4 z-50 max-w-sm w-full ".concat("success"===m.type?"bg-green-100 border-green-400 text-green-700":"bg-red-100 border-red-400 text-red-700"," border rounded-md p-4 shadow-lg"),children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:"success"===m.type?(0,r.jsx)("svg",{className:"h-5 w-5 text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm font-medium",children:m.message})}),(0,r.jsx)("div",{className:"ml-auto pl-3",children:(0,r.jsx)("button",{onClick:()=>u(null),className:"inline-flex text-gray-400 hover:text-gray-600",children:(0,r.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t&&(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Upload New Document"}),(0,r.jsx)("button",{onClick:()=>o(!1),className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)(d.A,{children:(0,r.jsx)(a,{onUploadSuccess:e=>{u({type:"success",message:'Successfully uploaded "'.concat(e.name,'"')}),s(e=>e+1),o(!1),setTimeout(()=>u(null),5e3)},onUploadError:e=>{u({type:"error",message:e}),setTimeout(()=>u(null),5e3)}})})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Your Documents"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Click on a document to edit or view it"})]}),(0,r.jsx)(d.A,{children:(0,r.jsx)(i,{onRefresh:h,refreshTrigger:e})})]})]}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-12",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600",children:(0,r.jsx)("p",{children:"OnlyOffice Document Server Demo - Built with Next.js"})}),(0,r.jsxs)("div",{className:"mt-4 md:mt-0 flex space-x-6",children:[(0,r.jsx)("a",{href:"https://www.onlyoffice.com/",target:"_blank",rel:"noopener noreferrer",className:"text-sm text-gray-600 hover:text-gray-900",children:"OnlyOffice"}),(0,r.jsx)("a",{href:"https://nextjs.org/",target:"_blank",rel:"noopener noreferrer",className:"text-sm text-gray-600 hover:text-gray-900",children:"Next.js"})]})]})})}),(0,r.jsx)(c,{isOpen:n,onClose:()=>x(!1),onCreateSuccess:e=>{u({type:"success",message:'Successfully created "'.concat(e.name,'"')}),s(e=>e+1),x(!1),setTimeout(()=>{window.open("/editor/".concat(e.id),"_blank")},500),setTimeout(()=>u(null),5e3)},onCreateError:e=>{u({type:"error",message:e}),setTimeout(()=>u(null),5e3)}})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(6200)),_N_E=e.O()}]);