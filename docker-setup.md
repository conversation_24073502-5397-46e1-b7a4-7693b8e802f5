# OnlyOffice Docker Integration Setup Guide

## Current Issues and Solutions

### 1. Environment Configuration

Create a `.env.local` file in your project root with these variables:

```env
# OnlyOffice Server Configuration
ONLYOFFICE_SERVER_URL=http://localhost:8080
ONLYOFFICE_JWT_SECRET=mysecret

# Application URL that Only<PERSON>ff<PERSON> can access
# IMPORTANT: For Docker, OnlyOff<PERSON> needs to access your app
# Use your machine's IP address, not localhost
NEXT_PUBLIC_APP_URL=http://YOUR_IP_ADDRESS:3001

# Development settings
NODE_ENV=development
PORT=3001
```

### 2. Docker Networking Issues

The main issue is that OnlyOffice running in Docker cannot access `localhost` URLs. You need to:

**Option A: Use Your Machine's IP Address**
1. Find your machine's IP address:
   - Windows: `ipconfig | findstr IPv4`
   - Mac/Linux: `ifconfig | grep inet`
2. Replace `localhost` with your IP address in `NEXT_PUBLIC_APP_URL`

**Option B: Use ngrok (Recommended for Development)**
1. Install ngrok: https://ngrok.com/download
2. Start your Next.js app: `npm run dev`
3. In another terminal: `ngrok http 3001`
4. Use the ngrok URL for `NEXT_PUBLIC_APP_URL`

### 3. Docker Compose Configuration

Make sure your OnlyOffice Docker setup allows external connections:

```yaml
version: '3.8'
services:
  onlyoffice:
    image: onlyoffice/documentserver:latest
    ports:
      - "8080:80"
    environment:
      - JWT_ENABLED=false
      - JWT_SECRET=mysecret
    networks:
      - onlyoffice-network
    restart: unless-stopped

networks:
  onlyoffice-network:
    driver: bridge
```

### 4. CORS Configuration

The app already includes CORS headers in the file serving endpoint. Ensure your OnlyOffice server can access:
- `http://your-ip:3001/api/files/serve/[id]`
- `http://your-ip:3001/api/onlyoffice/callback`

### 5. Testing the Setup

1. Start OnlyOffice Docker container:
   ```bash
   docker run -d --name onlyoffice -p 8080:80 onlyoffice/documentserver
   ```

2. Set your environment variables (replace YOUR_IP with actual IP):
   ```bash
   export ONLYOFFICE_SERVER_URL=http://localhost:8080
   export NEXT_PUBLIC_APP_URL=http://YOUR_IP:3001
   ```

3. Start your Next.js app:
   ```bash
   npm run dev
   ```

4. Test the integration:
   - Visit `http://localhost:3001/debug` to check OnlyOffice API loading
   - Try opening a document in the editor

### 6. Troubleshooting

**"Document Not Found" Error:**
- Check that files exist in `public/uploads/`
- Verify file serving endpoint responds correctly
- Ensure OnlyOffice can access your app's URL

**"Failed to get configuration" Error:**
- Check browser console for detailed error messages
- Verify environment variables are set correctly
- Test the config API endpoint directly

**OnlyOffice API Not Loading:**
- Check if OnlyOffice server is running: `curl http://localhost:8080/healthcheck`
- Verify firewall settings allow connections
- Try using the mock API fallback for testing

### 7. Production Deployment

For production:
1. Use a proper domain name instead of IP addresses
2. Enable HTTPS for both OnlyOffice and your app
3. Configure proper JWT authentication
4. Set up proper Docker networking or use external OnlyOffice service

### 8. JWT Authentication Issues

If you're getting JWT token errors (error code -20), try these solutions:

**Option A: Disable JWT Authentication (Recommended for Development)**
Add this to your `.env.local`:
```env
ONLYOFFICE_JWT_DISABLED=true
```

**Option B: Use the Correct JWT Secret**
If your OnlyOffice server requires JWT, ensure you have the correct secret:
```env
ONLYOFFICE_JWT_SECRET=your-actual-jwt-secret
```

**Option C: Configure OnlyOffice Docker to Disable JWT**
When starting OnlyOffice Docker container:
```bash
docker run -d --name onlyoffice \
  -p 8080:80 \
  -e JWT_ENABLED=false \
  -e JWT_SECRET=mysecret \
  onlyoffice/documentserver
```

**Option D: Test JWT-Disabled Configuration**
The app now includes a fallback mechanism that automatically tries JWT-disabled configuration when JWT fails.

### 9. Environment Variables Summary

Create a `.env.local` file with:
```env
# OnlyOffice Server Configuration
ONLYOFFICE_SERVER_URL=http://localhost:8080
ONLYOFFICE_JWT_DISABLED=true

# Application URL (use your machine's IP, not localhost)
NEXT_PUBLIC_APP_URL=http://YOUR_IP_ADDRESS:3001

# Development settings
NODE_ENV=development
PORT=3001
```

### 10. Testing JWT Configuration

Test different JWT configurations:

1. **Test with JWT disabled:**
   ```bash
   curl -X POST http://localhost:3001/api/test-jwt-disabled \
     -H "Content-Type: application/json" \
     -d '{"fileId":"your-file-id","mode":"edit"}'
   ```

2. **Test regular configuration:**
   ```bash
   curl -X POST http://localhost:3001/api/onlyoffice/config \
     -H "Content-Type: application/json" \
     -d '{"fileId":"your-file-id","mode":"edit"}'
   ```

## Quick Fix Commands

```bash
# Check if OnlyOffice is running
curl http://localhost:8080/healthcheck

# Test file serving
curl -I http://localhost:3001/api/files/serve/test-file-id

# Test config generation
curl -X POST http://localhost:3001/api/onlyoffice/config \
  -H "Content-Type: application/json" \
  -d '{"fileId":"test-file-id","mode":"edit"}'

# Test JWT-disabled configuration
curl -X POST http://localhost:3001/api/test-jwt-disabled \
  -H "Content-Type: application/json" \
  -d '{"fileId":"test-file-id","mode":"edit"}'
``` 