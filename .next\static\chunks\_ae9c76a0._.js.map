{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/components/OnlyOfficeEditor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\n\ninterface OnlyOfficeEditorProps {\n  fileId: string;\n  mode?: 'edit' | 'view';\n  userId?: string;\n  userName?: string;\n  onError?: (error: string) => void;\n  onDocumentReady?: () => void;\n}\n\ndeclare global {\n  interface Window {\n    DocsAPI: any;\n  }\n}\n\nexport default function OnlyOfficeEditor({\n  fileId,\n  mode = 'edit',\n  userId = 'user1',\n  userName = 'User',\n  onError,\n  onDocumentReady,\n}: OnlyOfficeEditorProps) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const editorRef = useRef<HTMLDivElement>(null);\n  const docEditorRef = useRef<any>(null);\n\n  useEffect(() => {\n    const initializeEditor = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n\n        // Load OnlyOffice API script\n        await loadOnlyOfficeAPI();\n\n        // Get configuration from API\n        const response = await fetch('/api/onlyoffice/config', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            fileId,\n            mode,\n            userId,\n            userName,\n          }),\n        });\n\n        const result = await response.json();\n\n        if (!result.success) {\n          throw new Error(result.error || 'Failed to get configuration');\n        }\n\n        // Initialize OnlyOffice editor\n        if (window.DocsAPI && editorRef.current) {\n          // Clear any existing editor\n          if (docEditorRef.current) {\n            try {\n              docEditorRef.current.destroyEditor();\n            } catch (e) {\n              console.warn('Error destroying previous editor:', e);\n            }\n          }\n\n          // Clear the container\n          editorRef.current.innerHTML = '';\n\n          // Prepare the configuration\n          const config = {\n            ...result.config,\n            events: {\n              onDocumentReady: () => {\n                console.log('OnlyOffice: Document ready');\n                setIsLoading(false);\n                onDocumentReady?.();\n              },\n              onError: (event: any) => {\n                console.error('OnlyOffice Error Event:', event);\n                let errorMessage = 'OnlyOffice Error: ';\n                if (typeof event.data === 'string') {\n                  errorMessage += event.data;\n                } else if (typeof event.data === 'object') {\n                  errorMessage += JSON.stringify(event.data);\n                } else {\n                  errorMessage += 'Unknown error occurred';\n                }\n                setError(errorMessage);\n                onError?.(errorMessage);\n                setIsLoading(false);\n              },\n              onWarning: (event: any) => {\n                console.warn('OnlyOffice Warning:', event.data);\n              },\n              onInfo: (event: any) => {\n                console.log('OnlyOffice Info:', event.data);\n              },\n            },\n          };\n\n          // Add JWT token if available\n          if (result.token) {\n            config.token = result.token;\n          }\n\n          console.log('Initializing OnlyOffice editor with config:', {\n            documentType: config.documentType,\n            documentKey: config.document?.key,\n            fileUrl: config.document?.url,\n            hasToken: !!config.token,\n            serverUrl: result.serverUrl\n          });\n\n          try {\n            docEditorRef.current = new window.DocsAPI.DocEditor(editorRef.current.id, config);\n          } catch (editorError) {\n            console.error('Error initializing OnlyOffice editor:', editorError);\n            setError(`Failed to initialize editor: ${editorError}`);\n            setIsLoading(false);\n          }\n        }\n      } catch (err) {\n        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize editor';\n        setError(errorMessage);\n        onError?.(errorMessage);\n        setIsLoading(false);\n      }\n    };\n\n    if (fileId) {\n      initializeEditor();\n    }\n\n    return () => {\n      if (docEditorRef.current) {\n        try {\n          docEditorRef.current.destroyEditor();\n        } catch (err) {\n          console.warn('Error destroying editor:', err);\n        }\n      }\n    };\n  }, [fileId, mode, userId, userName]);\n\n  const loadOnlyOfficeAPI = (): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      if (window.DocsAPI) {\n        resolve();\n        return;\n      }\n\n      const script = document.createElement('script');\n      script.src = `https://only.34sy.org/web-apps/apps/api/documents/api.js`;\n      script.async = true;\n      script.crossOrigin = 'anonymous';\n\n      script.onload = () => {\n        console.log('OnlyOffice API script loaded successfully');\n        if (window.DocsAPI) {\n          resolve();\n        } else {\n          reject(new Error('OnlyOffice API failed to load - DocsAPI not available'));\n        }\n      };\n\n      script.onerror = (error) => {\n        console.error('Failed to load OnlyOffice API script:', error);\n        reject(new Error('Failed to load OnlyOffice API script from https://only.34sy.org'));\n      };\n\n      console.log('Loading OnlyOffice API script...');\n      document.head.appendChild(script);\n    });\n  };\n\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center h-full min-h-[400px] bg-red-50 border border-red-200 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <div className=\"text-red-600 mb-2\">\n            <svg className=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-medium text-red-800 mb-2\">Editor Error</h3>\n          <p className=\"text-red-600\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors\"\n          >\n            Reload Page\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"relative w-full h-full min-h-[600px]\">\n      {isLoading && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading document editor...</p>\n          </div>\n        </div>\n      )}\n      <div\n        ref={editorRef}\n        id={`onlyoffice-editor-${fileId}`}\n        className=\"w-full h-full min-h-[600px]\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAmBe,SAAS,iBAAiB,EACvC,MAAM,EACN,OAAO,MAAM,EACb,SAAS,OAAO,EAChB,WAAW,MAAM,EACjB,OAAO,EACP,eAAe,EACO;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;+DAAmB;oBACvB,IAAI;wBACF,aAAa;wBACb,SAAS;wBAET,6BAA6B;wBAC7B,MAAM;wBAEN,6BAA6B;wBAC7B,MAAM,WAAW,MAAM,MAAM,0BAA0B;4BACrD,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;4BACA,MAAM,KAAK,SAAS,CAAC;gCACnB;gCACA;gCACA;gCACA;4BACF;wBACF;wBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;wBAElC,IAAI,CAAC,OAAO,OAAO,EAAE;4BACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;wBAClC;wBAEA,+BAA+B;wBAC/B,IAAI,OAAO,OAAO,IAAI,UAAU,OAAO,EAAE;4BACvC,4BAA4B;4BAC5B,IAAI,aAAa,OAAO,EAAE;gCACxB,IAAI;oCACF,aAAa,OAAO,CAAC,aAAa;gCACpC,EAAE,OAAO,GAAG;oCACV,QAAQ,IAAI,CAAC,qCAAqC;gCACpD;4BACF;4BAEA,sBAAsB;4BACtB,UAAU,OAAO,CAAC,SAAS,GAAG;4BAE9B,4BAA4B;4BAC5B,MAAM,SAAS;gCACb,GAAG,OAAO,MAAM;gCAChB,QAAQ;oCACN,eAAe;uFAAE;4CACf,QAAQ,GAAG,CAAC;4CACZ,aAAa;4CACb;wCACF;;oCACA,OAAO;uFAAE,CAAC;4CACR,QAAQ,KAAK,CAAC,2BAA2B;4CACzC,IAAI,eAAe;4CACnB,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;gDAClC,gBAAgB,MAAM,IAAI;4CAC5B,OAAO,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;gDACzC,gBAAgB,KAAK,SAAS,CAAC,MAAM,IAAI;4CAC3C,OAAO;gDACL,gBAAgB;4CAClB;4CACA,SAAS;4CACT,UAAU;4CACV,aAAa;wCACf;;oCACA,SAAS;uFAAE,CAAC;4CACV,QAAQ,IAAI,CAAC,uBAAuB,MAAM,IAAI;wCAChD;;oCACA,MAAM;uFAAE,CAAC;4CACP,QAAQ,GAAG,CAAC,oBAAoB,MAAM,IAAI;wCAC5C;;gCACF;4BACF;4BAEA,6BAA6B;4BAC7B,IAAI,OAAO,KAAK,EAAE;gCAChB,OAAO,KAAK,GAAG,OAAO,KAAK;4BAC7B;4BAEA,QAAQ,GAAG,CAAC,+CAA+C;gCACzD,cAAc,OAAO,YAAY;gCACjC,aAAa,OAAO,QAAQ,EAAE;gCAC9B,SAAS,OAAO,QAAQ,EAAE;gCAC1B,UAAU,CAAC,CAAC,OAAO,KAAK;gCACxB,WAAW,OAAO,SAAS;4BAC7B;4BAEA,IAAI;gCACF,aAAa,OAAO,GAAG,IAAI,OAAO,OAAO,CAAC,SAAS,CAAC,UAAU,OAAO,CAAC,EAAE,EAAE;4BAC5E,EAAE,OAAO,aAAa;gCACpB,QAAQ,KAAK,CAAC,yCAAyC;gCACvD,SAAS,CAAC,6BAA6B,EAAE,aAAa;gCACtD,aAAa;4BACf;wBACF;oBACF,EAAE,OAAO,KAAK;wBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;wBAC1D,SAAS;wBACT,UAAU;wBACV,aAAa;oBACf;gBACF;;YAEA,IAAI,QAAQ;gBACV;YACF;YAEA;8CAAO;oBACL,IAAI,aAAa,OAAO,EAAE;wBACxB,IAAI;4BACF,aAAa,OAAO,CAAC,aAAa;wBACpC,EAAE,OAAO,KAAK;4BACZ,QAAQ,IAAI,CAAC,4BAA4B;wBAC3C;oBACF;gBACF;;QACF;qCAAG;QAAC;QAAQ;QAAM;QAAQ;KAAS;IAEnC,MAAM,oBAAoB;QACxB,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,OAAO,OAAO,EAAE;gBAClB;gBACA;YACF;YAEA,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,GAAG,GAAG,CAAC,wDAAwD,CAAC;YACvE,OAAO,KAAK,GAAG;YACf,OAAO,WAAW,GAAG;YAErB,OAAO,MAAM,GAAG;gBACd,QAAQ,GAAG,CAAC;gBACZ,IAAI,OAAO,OAAO,EAAE;oBAClB;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM;gBACnB;YACF;YAEA,OAAO,OAAO,GAAG,CAAC;gBAChB,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,OAAO,IAAI,MAAM;YACnB;YAEA,QAAQ,GAAG,CAAC;YACZ,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAoB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC3E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAInC,6LAAC;gBACC,KAAK;gBACL,IAAI,CAAC,kBAAkB,EAAE,QAAQ;gBACjC,WAAU;;;;;;;;;;;;AAIlB;GA1MwB;KAAA", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ReactNode } from 'react';\nimport { ErrorBoundaryState } from '@/types/onlyoffice';\n\ninterface ErrorBoundaryProps {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: any) => void;\n}\n\nclass ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    this.setState({\n      hasError: true,\n      error,\n      errorInfo,\n    });\n\n    // Call the onError callback if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n  }\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default error UI\n      return (\n        <div className=\"min-h-[400px] flex items-center justify-center bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"text-center p-6 max-w-md\">\n            <div className=\"text-red-600 mb-4\">\n              <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h2 className=\"text-xl font-semibold text-red-800 mb-2\">Something went wrong</h2>\n            <p className=\"text-red-600 mb-4\">\n              {this.state.error?.message || 'An unexpected error occurred'}\n            </p>\n            <div className=\"space-y-2\">\n              <button\n                onClick={this.handleReset}\n                className=\"w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\n              >\n                Try Again\n              </button>\n              <button\n                onClick={() => window.location.reload()}\n                className=\"w-full px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors\"\n              >\n                Reload Page\n              </button>\n            </div>\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"mt-4 text-left\">\n                <summary className=\"cursor-pointer text-sm text-red-700 hover:text-red-800\">\n                  Error Details (Development)\n                </summary>\n                <pre className=\"mt-2 text-xs text-red-600 bg-red-100 p-2 rounded overflow-auto max-h-32\">\n                  {this.state.error.stack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;AA6Ea;;AA3Eb;AAFA;;;AAWA,MAAM,sBAAsB,6JAAA,CAAA,YAAS;IACnC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAc,EAAE;QAC9C,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,IAAI,CAAC,QAAQ,CAAC;YACZ,UAAU;YACV;YACA;QACF;QAEA,wCAAwC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IACF;IAEA,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;YAAW,WAAW;QAAU;IAC1E,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBAAqB;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,mBAAmB;YACnB,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC3E,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;4BAAG,WAAU;sCAA0C;;;;;;sCACxD,6LAAC;4BAAE,WAAU;sCACV,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW;;;;;;sCAEhC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAI,CAAC,WAAW;oCACzB,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;;wBAIF,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAQ,WAAU;8CAAyD;;;;;;8CAG5E,6LAAC;oCAAI,WAAU;8CACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;QAOrC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  color?: 'blue' | 'gray' | 'green' | 'red' | 'yellow';\n  text?: string;\n  className?: string;\n}\n\nexport default function LoadingSpinner({ \n  size = 'md', \n  color = 'blue', \n  text,\n  className = '' \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n    xl: 'h-16 w-16'\n  };\n\n  const colorClasses = {\n    blue: 'border-blue-600',\n    gray: 'border-gray-600',\n    green: 'border-green-600',\n    red: 'border-red-600',\n    yellow: 'border-yellow-600'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-xs',\n    md: 'text-sm',\n    lg: 'text-base',\n    xl: 'text-lg'\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <div\n        className={`animate-spin rounded-full border-2 border-t-transparent ${sizeClasses[size]} ${colorClasses[color]}`}\n        role=\"status\"\n        aria-label=\"Loading\"\n      />\n      {text && (\n        <p className={`mt-2 text-gray-600 ${textSizeClasses[size]}`}>\n          {text}\n        </p>\n      )}\n    </div>\n  );\n}\n\n// Preset loading components for common use cases\nexport function PageLoader({ text = 'Loading...' }: { text?: string }) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <LoadingSpinner size=\"lg\" text={text} />\n    </div>\n  );\n}\n\nexport function ComponentLoader({ text }: { text?: string }) {\n  return (\n    <div className=\"flex items-center justify-center py-12\">\n      <LoadingSpinner size=\"md\" text={text} />\n    </div>\n  );\n}\n\nexport function InlineLoader({ size = 'sm' }: { size?: 'sm' | 'md' }) {\n  return <LoadingSpinner size={size} className=\"inline-flex\" />;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAWe,SAAS,eAAe,EACrC,OAAO,IAAI,EACX,QAAQ,MAAM,EACd,IAAI,EACJ,YAAY,EAAE,EACM;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,MAAM;QACN,OAAO;QACP,KAAK;QACL,QAAQ;IACV;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,6LAAC;gBACC,WAAW,CAAC,wDAAwD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,EAAE;gBAChH,MAAK;gBACL,cAAW;;;;;;YAEZ,sBACC,6LAAC;gBAAE,WAAW,CAAC,mBAAmB,EAAE,eAAe,CAAC,KAAK,EAAE;0BACxD;;;;;;;;;;;;AAKX;KA1CwB;AA6CjB,SAAS,WAAW,EAAE,OAAO,YAAY,EAAqB;IACnE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAe,MAAK;YAAK,MAAM;;;;;;;;;;;AAGtC;MANgB;AAQT,SAAS,gBAAgB,EAAE,IAAI,EAAqB;IACzD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAe,MAAK;YAAK,MAAM;;;;;;;;;;;AAGtC;MANgB;AAQT,SAAS,aAAa,EAAE,OAAO,IAAI,EAA0B;IAClE,qBAAO,6LAAC;QAAe,MAAM;QAAM,WAAU;;;;;;AAC/C;MAFgB", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/src/app/editor/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport OnlyOfficeEditor from '@/components/OnlyOfficeEditor';\nimport ErrorBoundary from '@/components/ErrorBoundary';\nimport { PageLoader } from '@/components/LoadingSpinner';\nimport { FileMetadata } from '@/types/onlyoffice';\n\n\n\nexport default function EditorPage() {\n  const params = useParams();\n  const router = useRouter();\n  const fileId = params.id as string;\n  \n  const [fileInfo, setFileInfo] = useState<FileMetadata | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchFileInfo = async () => {\n      try {\n        const response = await fetch(`/api/files/${fileId}`);\n        const result = await response.json();\n\n        if (result.success) {\n          setFileInfo(result.file);\n        } else {\n          setError(result.error || 'File not found');\n        }\n      } catch (err) {\n        setError('Failed to load file information');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (fileId) {\n      fetchFileInfo();\n    }\n  }, [fileId]);\n\n  const handleEditorError = (errorMessage: string) => {\n    setError(errorMessage);\n  };\n\n  const handleDocumentReady = () => {\n    console.log('Document is ready for editing');\n  };\n\n  const handleGoBack = () => {\n    router.push('/');\n  };\n\n  if (loading) {\n    return <PageLoader text=\"Loading document...\" />;\n  }\n\n  if (error || !fileInfo) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center p-6\">\n          <div className=\"text-red-600 mb-4\">\n            <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Document Not Found</h1>\n          <p className=\"text-gray-600 mb-6\">{error || 'The requested document could not be found.'}</p>\n          <button\n            onClick={handleGoBack}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Documents\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 px-4 py-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={handleGoBack}\n              className=\"flex items-center text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n              Back to Documents\n            </button>\n            <div className=\"h-6 border-l border-gray-300\"></div>\n            <div>\n              <h1 className=\"text-lg font-semibold text-gray-900\">{fileInfo.name}</h1>\n              <p className=\"text-sm text-gray-500\">\n                Last modified: {new Date(fileInfo.lastModified).toLocaleString()}\n              </p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n              {fileInfo.extension.toUpperCase()}\n            </span>\n            <span className=\"text-sm text-gray-500\">\n              {(fileInfo.size / 1024 / 1024).toFixed(2)} MB\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Editor */}\n      <div className=\"h-[calc(100vh-80px)]\">\n        <ErrorBoundary\n          onError={(error, errorInfo) => {\n            console.error('Editor Error Boundary:', error, errorInfo);\n            setError(`Editor Error: ${error.message}`);\n          }}\n        >\n          <OnlyOfficeEditor\n            fileId={fileId}\n            mode=\"edit\"\n            userId=\"user1\"\n            userName=\"Demo User\"\n            onError={handleEditorError}\n            onDocumentReady={handleDocumentReady}\n          />\n        </ErrorBoundary>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAWe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IAExB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;sDAAgB;oBACpB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;wBACnD,MAAM,SAAS,MAAM,SAAS,IAAI;wBAElC,IAAI,OAAO,OAAO,EAAE;4BAClB,YAAY,OAAO,IAAI;wBACzB,OAAO;4BACL,SAAS,OAAO,KAAK,IAAI;wBAC3B;oBACF,EAAE,OAAO,KAAK;wBACZ,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,IAAI,QAAQ;gBACV;YACF;QACF;+BAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,MAAM,sBAAsB;QAC1B,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,SAAS;QACX,qBAAO,6LAAC,uIAAA,CAAA,aAAU;YAAC,MAAK;;;;;;IAC1B;IAEA,IAAI,SAAS,CAAC,UAAU;QACtB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAoB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC3E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAsB,SAAS;;;;;;kCAC5C,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAGR,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAuC,SAAS,IAAI;;;;;;sDAClE,6LAAC;4CAAE,WAAU;;gDAAwB;gDACnB,IAAI,KAAK,SAAS,YAAY,EAAE,cAAc;;;;;;;;;;;;;;;;;;;sCAIpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,SAAS,SAAS,CAAC,WAAW;;;;;;8CAEjC,6LAAC;oCAAK,WAAU;;wCACb,CAAC,SAAS,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sIAAA,CAAA,UAAa;oBACZ,SAAS,CAAC,OAAO;wBACf,QAAQ,KAAK,CAAC,0BAA0B,OAAO;wBAC/C,SAAS,CAAC,cAAc,EAAE,MAAM,OAAO,EAAE;oBAC3C;8BAEA,cAAA,6LAAC,yIAAA,CAAA,UAAgB;wBACf,QAAQ;wBACR,MAAK;wBACL,QAAO;wBACP,UAAS;wBACT,SAAS;wBACT,iBAAiB;;;;;;;;;;;;;;;;;;;;;;AAM7B;GA5HwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/ai/onlyofice/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}